'use client'
/**
 * 导入所需的依赖和组件
 */
import {useEffect, useRef, useState} from 'react'
import styles from './index.module.css'
import StartView from './contents/start'
import HintView from './contents/hint'
import useInitQuestion from '@/hooks/useInitQuestion'
import useQuizStore from '@/store/quiz'
import {Toaster} from '@/components/ui/toaster'
import ExitHintView from './contents/exam.exit'
import ExamContentView from './contents/exam.content'
import {getStorage, getStorageJson, setStorage} from '@/utils/storage'
import ExamFinish from './contents/exam.finish'
import {publishAnswerApi} from '@/http/http'
import {catchErrorPlus} from '@/utils/sentry'
// import {toast} from '@/hooks/use-toast'
import useCheckUrlQuery from '@/hooks/useCheckQuery'
import useWechatAuth from '@/hooks/useWechatAuth'
// import useRefreshHint from '@/hooks/useRefreshHint'
import useBehavioralLimit from '@/hooks/useBehavioralLimit'
import useCheckAllowExam from '@/hooks/useCheckAllowEaxm'
// import useMediaPermission from '@/hooks/useMediaPermission'
import {useVh} from '@/hooks/useVh'
import ErrHint from '@/components/business/ErrHint'
import CheckContent from './contents/exam.check'
import ExamRequestView from './contents/exam.request'
import ExamRecordView from './contents/exam.record'
import ExamPlayView from './contents/exam.play'

/**
 * 组件属性接口定义
 */
interface Props {
    quizType: string
}

/**
 * 测验视图组件
 * @param props - 组件属性
 */
const QuizNewView = (props: Props) => {    
  // 考试状态管理
  const [examStatus, setExamStatus] = useState<string>('init_exam')

  const currentAnswerRef = useRef<any[]>([])

  // 检查当前路由
  const [initToken] = useCheckUrlQuery()

  // useWechatAuth(initToken)
  const [allowNext] = useWechatAuth(initToken)

  const examRef = useRef<any>(null)

  const [errHint, setErrHint] = useState({
    isOpen: false,
    icon: '',
    title: '',
    content: '',
    btnText: '',
    fn: () => {}
  })

  // 检查刷新提示
  // useRefreshHint()
  // 检查行为限制
  useBehavioralLimit()
  // 设置vh
  useVh()
  // 检查是否允许考试
  // useCheckAllowExam(initToken)

  // const {isRequestMediaPermission} = useMediaPermission({allowNext})

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const VConsole = require('vconsole')

      if (window.location.href.includes('debug')) {
        new VConsole()
      }
    }
  }, [])

  /**
   * 初始化函数
   * @param isOnlyCloseLoading - 是否仅关闭加载状态
   */
  const initFn = (isOnlyCloseLoading: boolean) => {
    if (isOnlyCloseLoading) {
      setExamStatus('start_exam')
    } else {
      setExamStatus('exit_exam')
    }
  }

  // 初始化题目
  const {updateQuestionNumber} = useInitQuestion({quizType: props.quizType, fn: initFn, allowNext})

  // 从状态管理中获取当前题目相关信息
  const {
    currentQuestionNumber, 
    totalNumber, 
    currentQuestion = {} as any, 
    currentAnswer, 
    currentExamId
  } = useQuizStore(state => state)

  const {setCurrentAnswer, setCurrentQuestion} = useQuizStore.getState()

  useEffect(() => {
    currentAnswerRef.current = currentAnswer
  }, [currentAnswer])

  /**
   * 处理文本题目答案变更
   * @param text - 答案文本
   * @param isInput - 是否为输入类型
   */
  const onChangeText = (text: string, isInput?: boolean) => {
    const ans = {question_id: currentQuestion.id, answer: [{type: isInput ? 'input' : 'text', value: text || ' '}]}

    let fin = currentAnswer || []

    if (currentAnswer?.length) {
      const findIndex = currentAnswer.findIndex(item => item.question_id === currentQuestion.id)

      if (findIndex !== -1) {
        fin = currentAnswer.map(item => {
          if (item.question_id === currentQuestion.id) {
            return ans
          }

          return item
        })
      } else {
        fin = fin.concat([ans])
      }
    } else {
      fin = (fin || []).concat([ans])
    }
    setCurrentAnswer(fin!)
    setStorage(currentExamId + '___cacheAnswer', {answer: fin!, id: currentExamId, number: fin?.length})
  }

  /**
   * 处理选择题答案变更
   * @param s - 选择的选项
   * @param isInput - 是否为输入类型
   */
  const onChangeSelect = (s: {[key: string]: string[]}, isInput?: boolean) => {
    const ans = {question_id: currentQuestion.id, answer: isInput ? [{type: 'input', inputs: s}] : [{type: 'option', value: '', options: s}]} as any

    let fin = currentAnswer || []

    if (currentAnswer?.length) {
      const findIndex = currentAnswer.findIndex(item => item.question_id === currentQuestion.id)

      if (findIndex !== -1) {
        fin = currentAnswer.map(item => {
          if (item.question_id === currentQuestion.id) {
            return ans
          }

          return item
        })
      } else {
        fin = fin.concat([ans])
      }
    } else {
      fin = (fin || []).concat([ans])
    }
    setCurrentAnswer(fin!)
    setStorage(currentExamId + '___cacheAnswer', {answer: fin!, id: currentExamId, number: fin?.length})
  }

  /**
   * 提交答案
   */
  const onSubmit = () => {
    // console.log('Submit quiz',currentAnswer, currentQuestion)
    const cacheAnswer = getStorageJson(currentExamId + '___cacheAnswer')?.answer || []

    let temp = [...(currentAnswer || [])]

    if (temp.length < cacheAnswer.length) {
      temp = cacheAnswer
    }

    const ans = {question_id: currentQuestion.id} as any

    const index = temp?.findIndex(item => item.question_id === currentQuestion.id)

    console.info(temp, 'temp')
    if (index === -1) {
      temp.push(ans)
    }
    publishAnswerApi(currentExamId, temp).then(() => {
      setStorage(currentExamId + '___cacheAnswer', {})
      setExamStatus('result')
    }).catch(err => {
      catchErrorPlus(err, {
        tags: {component: 'quiz', type: props.quizType}, extras: {
          cartId: currentExamId, token: getStorage('x-5e-token'), answer: temp, question: currentQuestion
        } 
      })
      setErrHint({
        isOpen: true,
        icon: '😢',
        title: '提交失败',
        content: err.message + '请点击按钮重新尝试提交',
        btnText: '重试',
        fn: () => {
          onSubmit()
        }
      })
    })
  }

  /**
   * 处理下一题
   */
  const handleNextQuestion = () => {
    // 判断当前题目是否已经回答
    // return
    console.info('我在执行下一步逻辑', currentQuestionNumber, totalNumber)
    const hasAnswer = currentAnswerRef.current?.some?.((item: any) => item.question_id === currentQuestion?.id)

    if (currentQuestionNumber > totalNumber) {
      setStorage(currentExamId + '___cacheAnswer', {})
      window.location.reload()

      return
    }
    // console.info(currentAnswer, 'currentAnswer', '1111', currentQuestionNumber, totalNumber, hasAnswer)
    if (hasAnswer) {
      if (currentQuestionNumber === totalNumber) {
        // 如果是最后一题，跳转到结果页
        onSubmit()
      } else {
        // 如果不是最后一题，更新题目
        updateQuestionNumber(currentQuestionNumber + 1)
        examRef.current?.updateDirection()
      }
    } else {
      // 提示用户
      console.info('请先回答当前题目')
      const ans = {question_id: currentQuestion.id} as any

      const temp = currentAnswerRef.current.concat([ans])

      setCurrentAnswer(temp)
      setStorage(currentExamId + '___cacheAnswer', {answer: temp!, id: currentExamId, number: temp?.length})
      if (currentQuestionNumber === totalNumber) {
        // 如果是最后一题，跳转到结果页
        onSubmit()
      } else {
        // 如果不是最后一题，更新题目
        updateQuestionNumber(currentQuestionNumber + 1)
        examRef.current?.updateDirection()
      }
    }
  }

  // 授权成功的微信移动端
  
  // if (!isRequestMediaPermission) return <div className={styles.page}><Toaster /></div>
  // 根据考试状态渲染不同页面
  if (examStatus === 'result') {
    return (
      <div className={styles.page}>
        <ExamFinish onNext={() => {
          setExamStatus('init_exam')
          window.location.reload()
        }} />
      </div>
    )
  }

  if (examStatus === 'start_exam') {
    return (
      <div className={styles.page}>
        <StartView totalNumber={totalNumber}  onStart={() => {
          setExamStatus('request_exam')
        }} />
      </div>
    )
  }

  if (examStatus === 'exit_exam') {
    return (
      <div className={styles.page}>
        <ExitHintView totalNumber={totalNumber} onNext={() => {
          setExamStatus('request_exam')
        }} />
      </div>
    )
  }
  if (examStatus === 'check_exam') {
    return (
      <div className={styles.page}>
        <CheckContent onNext={() => {
          setExamStatus('question')
        }} />
      </div>
    )
  }
  // 请求权限
  if (examStatus === 'request_exam') {
    return (
      <div className={styles.page}>
        <ExamRequestView onNext={() => {
          setExamStatus('record_exam')
        }} />
      </div>
    )
  }
  // 测试语音
  if (examStatus === 'record_exam') {
    return (
      <div>
        <ExamRecordView onNext={() => {
          setExamStatus('play_exam')
        }} />
      </div>
    )
  }
  // 播放页面
  if (examStatus === 'play_exam') {
    return (
      <div>
        <ExamPlayView onNext={() => {
          setExamStatus('question')
        }} />
      </div>
    )
  }
  // 考试区域的页面
  if (examStatus !== 'question') return <div className={styles.page}><Toaster /></div>

  // console.info(currentAnswer, 'currentAnswer')

  return (
    <div className={styles.page}>
      <audio src='https://gusto-english-oss.wemore.com/exam/ding.mp3' className={styles.audioHide} />
      <HintView 
        isOpen={currentQuestion?.tipInfo?.isShow!} 
        onClose={() => {
          setCurrentQuestion({...currentQuestion, tipInfo: {...currentQuestion.tipInfo!, isShow: false} })
        }} 
        content={currentQuestion?.tipInfo?.content!}
        introduction={currentQuestion?.introduction!}
      />
      {!currentQuestion?.tipInfo?.isShow && <ExamContentView 
        ref={examRef}
        model={currentQuestion} 
        currentNumber={currentQuestionNumber} 
        totalNumber={totalNumber} 
        onNextQuestion={handleNextQuestion}
        onChangeText={onChangeText}
        currentAnswer={currentAnswer}
        onChangeSelect={onChangeSelect}
      />}

      <ErrHint 
        isOpen={errHint.isOpen} 
        onClose={() => {
          setErrHint({...errHint, isOpen: false})
          errHint.fn()
        }} 
        icon={errHint.icon} 
        title={errHint.title} 
        content={errHint.content} 
        btnText={errHint.btnText} 
      />
    </div>
  )
}

export default QuizNewView