.page {
    width: 100vw;
    height: calc(var(--vh, 1vh) * 100);
    background: linear-gradient(180deg, #080808 18.04%, #EC2726 100%);
}   


/* 从右侧进入的动画 */
.slide-right-enter {
    transform: translateX(100%);
    opacity: 0;
  }
  
  .slide-right-enter-active {
    transform: translateX(0);
    opacity: 1;
    transition: all 300ms ease-out;
  }
  
  .slide-right-exit {
    transform: translateX(0);
    opacity: 1;
  }
  
  .slide-right-exit-active {
    transform: translateX(-100%);
    opacity: 0;
    transition: all 300ms ease-out;
  }
  
  /* 从左侧进入的动画 */
  .slide-left-enter {
    transform: translateX(-100%);
    opacity: 0;
  }
  
  .slide-left-enter-active {
    transform: translateX(0);
    opacity: 1;
    transition: all 300ms ease-out;
  }
  
  .slide-left-exit {
    transform: translateX(0);
    opacity: 1;
  }
  
  .slide-left-exit-active {
    transform: translateX(100%);
    opacity: 0;
    transition: all 300ms ease-out;
  }
 .audioHide {
  position: fixed;
  left: -1000px;
  top: -1000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
  opacity: 0;
 } 