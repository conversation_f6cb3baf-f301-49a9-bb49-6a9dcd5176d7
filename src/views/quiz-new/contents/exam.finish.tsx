'use client'
import Header<PERSON><PERSON> from '@/components/business/HeaderLogo'
import styles from './index.module.css'
import dynamic from 'next/dynamic'
import {useEffect, useState} from 'react'

const LottieAnimation = dynamic(() => import('@/components/business/LottieAnimation'), {
  ssr: false,
  loading: () => <div style={{width: 420, height: 420}} />
})

interface Props {
  onNext: () => void
}

const ExitHintView = (props: Props) => {
  const [delay, setDelay] = useState<boolean>(false)

  useEffect(() => {
    setTimeout(() => {
      setDelay(true)
    }, 200)  
  }, [])

  return (
    <div className={styles.hintContainer}>
      <HeaderLogo />  

      <div>
        {delay && <LottieAnimation name="finish" loop={false} size={420} styles={{
          position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)'
        }} />}
      </div>
      <div style={{
        display: 'flex', alignItems: 'center', flexDirection: 'column',           position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)'
      }}>
        <p className='text-[80px] text-center text-white'>🎉</p>
        <p className="font-['SF Compact'] font-[457] text-[16px] leading-[100%] tracking-[0px] text-center text-white mt-[5px] w-[300px]">
            测试已完成
        </p>
        <p className="font-['SF Compact'] font-[457] text-[16px] leading-[100%] tracking-[0px] text-center text-white mt-[5px] w-[300px]">
            稍后请在  Gusto English 服务号查看成绩
        </p>
      </div>  
      <div className={`${styles.startButton} ${styles.fixedButton} w-[300px]`} onClick={props.onNext}>
        完成
      </div>
    </div>
  )
}

export default ExitHintView