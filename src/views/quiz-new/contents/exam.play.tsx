'use client'
import <PERSON><PERSON><PERSON><PERSON> from '@/components/business/HeaderLogo'
import styles from './index.module.css'
import LottieAnimation from '@/components/business/LottieAnimation'
import LottieAnimationStop from '@/components/business/LottieAnimation/stop'
import {useEffect, useRef, useState} from 'react'
import {getGlobalAudioContext, resumeAudioContextOnUserInteraction} from './utils'

interface Props {
  onNext: () => void
}

const ExamPlayView = (props: Props) => {
  const [audioProgressTime, setAudioProgressTime] = useState(0)

  const [isPlaying, setIsPlaying] = useState(false)

  const audioRef = useRef<HTMLAudioElement | null>(null)

  useEffect(() => {
    playFn()
  }, [])

  const playFn = () => {
    const audio = audioRef.current

    if (!audio || !audio.paused) {
      return
    }
    try {
      audio.play?.().then(() => {
        setIsPlaying(true)
        const duration = audio.duration

        setAudioProgressTime(duration)
      })
    } catch (error) {
      console.info(error, 'error')
    }
    audio.addEventListener('ended', () => {
      setIsPlaying(false)
      setAudioProgressTime(0)
    }, {once: true})
  }

  const onNextFn = async () => {
    resumeAudioContextOnUserInteraction()
    props.onNext()
  }

  return (
    <div className={styles.hintContainer}>
      <HeaderLogo />

      <div style={{
        display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center'
      }}>
        <div
          className={
            'w-[100px] h-[100px] rounded-[21px] bg-[rgba(237,40,39,0.5)] cursor-pointer flex items-center justify-center relative overflow-hidden'
          }
          onClick={() => {
            const audio = audioRef.current

            if (!audio || !audio.paused) {
              return
            }
            try {
              audio.play?.().then(() => {
                setIsPlaying(true)
                const duration = audio.duration

                setAudioProgressTime(duration)
              })
            } catch (error) {
              console.info(error, 'error')
            }
            audio.addEventListener('ended', () => {
              setIsPlaying(false)
            }, {once: true})
          }}
        >
          {audioProgressTime ? (
            <div
              className={`absolute top-0 left-0 flex items-center justify-center bg-[#ED2827] ${styles.animationView}`}
              key={audioProgressTime}
              style={{animationDuration: `${audioProgressTime + 1}s`}}
            />
          ) : null}
          {isPlaying ? (
            <LottieAnimation name="record" size={100} />
          ) : (
            <LottieAnimationStop name="record" size={100} isPause={true} />
          )}
        </div>
        <audio
          src={'/audios/Gusto_test_demo.mp3'}
          ref={audioRef}
          id={'audio-check'}
          style={{
            width: '1px !important',
            height: '1px !important',
            position: 'fixed',
            top: '-1000px',
            left: '-1000px',
          }}
        />
        <p className="font-['SF Compact'] font-[457] text-[16px] leading-[100%] tracking-[0px] text-center text-white mt-[5px] w-[300px]" style={{marginTop: '40px'}}>
          如果没有听到声音，请检查音量是否太小
          或开启静音
        </p>
      </div>
      <div className={`${styles.startButton} ${styles.fixedButton}`} onClick={onNextFn}>
        声音播放正常
      </div>
    </div>
  )
}

export default ExamPlayView