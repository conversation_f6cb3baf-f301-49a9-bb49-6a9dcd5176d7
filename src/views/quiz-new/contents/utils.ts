export const formatTimeCount = (seconds: number) => {
  if (seconds <= 0) return ''
  const minutes = Math.floor(seconds / 60).toString().padStart(2, '0')

  const remainingSeconds = seconds % 60

  return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`
}

export const hasMediaContent = (content: any[], type?: 'audio' | 'video') => {
  if (type) {
    return content?.filter?.((item: any) => item.type === type)?.length > 0
  }

  return content?.filter?.((item: any) => item.type === 'audio' || item.type === 'video')?.length > 0
}

export const vibrate = (duration = 100) => {
  if (typeof window !== 'undefined' && 'vibrate' in navigator) {
    navigator.vibrate(duration)
  }
}

// 检查麦克风权限状态
export const checkMicrophonePermission = async (): Promise<boolean | null | string> => {
  if ('permissions' in navigator && typeof navigator.permissions.query === 'function') {
    try {
      const result = await navigator.permissions.query({name: 'microphone' as PermissionName})

      if (result.state === 'granted') {
        // 已授权
        return true
      } else if (result.state === 'denied') {
        return false
        // 已拒绝
      } else if (result.state === 'prompt') {
        // 需要请求
      }

      return result.state
    } catch (error) {
      console.warn('⚠️ 无法使用 Permissions API 查询权限:', error)

      return null
    }
  } else {
    console.warn('⚠️ 浏览器不支持 Permissions API')

    return true
  }
}

// 申请麦克风权限
export const requestMicrophonePermission = async (): Promise<boolean> => {
  try {
    const stream: MediaStream = await navigator.mediaDevices.getUserMedia({audio: true})

    // 释放麦克风资源
    for (const track of stream.getTracks()) {
      track.stop()
    }
    console.log('✅ 用户授权麦克风权限成功')

    return true
  } catch (err) {
    console.error('❌ 获取麦克风权限失败:', err)
    alert('无法访问麦克风，请检查浏览器或设备权限设置。')

    return false
  }
}
let globalAudioContext: AudioContext | null = null

export function getGlobalAudioContext(): AudioContext {
  if (!globalAudioContext) {
    globalAudioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
  }

  return globalAudioContext
}

// 页面加载或用户首次点击时触发，提前恢复 AudioContext
export function resumeAudioContextOnUserInteraction() {
  const ctx = getGlobalAudioContext()

  const resume = async () => {
    try {
      if (ctx.state === 'suspended') {
        await ctx.resume()
        console.log('[WebAudio] AudioContext resumed by user interaction')
      }
    } catch (err) {
      console.warn('[WebAudio] Resume error:', err)
    }
  }

  document.addEventListener('click', resume, {once: true})
  document.addEventListener('touchstart', resume, {once: true})
}

let isWebAudioPlaying = false

export const playAudioWithWebAudio = async (
  url: string,
  gainValue: number = 2.0,
  {startCb, endCb}: { startCb?: () => void; endCb?: () => void }
): Promise<void> => {
  if (isWebAudioPlaying) {
    console.warn('[WebAudio] 已在播放中，忽略本次请求')

    return
  }

  isWebAudioPlaying = true
  const audioContext = getGlobalAudioContext()

  try {
    // 1. 创建音频元素
    const audio = new Audio(url)

    audio.crossOrigin = 'anonymous' // 避免跨域警告

    // 2. 确保 AudioContext 可用
    for (let i = 0; i < 3 && audioContext.state !== 'running'; i++) {
      try {
        await audioContext.resume()
        console.log(`[WebAudio] 尝试第 ${i + 1} 次 resume`)
      } catch (e) {
        console.warn('[WebAudio] resume 失败:', e)
      }
    }

    // 3. 创建节点连接
    const gainNode = audioContext.createGain()

    gainNode.gain.value = gainValue

    let source: MediaElementAudioSourceNode

    try {
      source = audioContext.createMediaElementSource(audio)
    } catch (e) {
      // 某些情况下（同一个 audio 被多次 createMediaElementSource）会抛错
      console.error('[WebAudio] createMediaElementSource error:', e)
      isWebAudioPlaying = false

      return
    }

    source.connect(gainNode).connect(audioContext.destination)

    // 4. 播放成功/失败事件绑定
    audio.onplay = () => {
      console.log('[WebAudio] 播放开始')
      startCb?.()
    }

    audio.onended = () => {
      console.log('[WebAudio] 播放结束')
      cleanup()
      endCb?.()
    }

    audio.onerror = (e) => {
      console.error('[WebAudio] 播放失败:', e)
      cleanup()
    }

    // 5. 等待音频加载完成再播放
    audio.addEventListener('canplaythrough', async () => {
      try {
        await audio.play()
      } catch (e) {
        console.error('[WebAudio] audio.play() 出错:', e)
        cleanup()
      }
    })

    // 触发加载
    audio.load()

    function cleanup() {
      try {
        source?.disconnect()
        gainNode?.disconnect()
      } catch (e) {
        console.warn('[WebAudio] disconnect 出错:', e)
      }
      isWebAudioPlaying = false
    }
  } catch (error) {
    console.error('[WebAudio] 播放失败:', error)
    isWebAudioPlaying = false
  }
}

// 播放音频

export const onAudioPlayer = async (url: string, onStart?: (duration: number) => void, onEnded?: () => void) => {
  const audioUrl = url

  const audio = new Audio(audioUrl)

  audio.addEventListener('ended', () => {
    audio.pause()
    audio.currentTime = 0
    onEnded?.()
  })

  try {
    await audio.play()
    onStart?.(audio.duration)
  } catch (error) {
    console.error('Error playing audio:', error)
  }
}