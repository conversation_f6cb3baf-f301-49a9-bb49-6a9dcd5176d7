'use client'
import styles from './index.module.css'

interface Props {
  totalNumber?: number
  onStart: () => void
}

const StartView = (props: Props) => {

  const handleStart = () => {
    props.onStart()
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <img src="/new/appicon-gusto-test.png" alt="start" className="w-[80px] h-[80px] mb-[60px]" />
      <p className="font-['GTVCS-Black'] font-black text-[28px] leading-[100%] tracking-[0px] text-center text-white mb-[10px]">Gusto English</p>
      <p className="font-['GTVCS-Black'] font-black text-[28px] leading-[100%] tracking-[0px] text-center text-white">高拓英语俱乐部水平测试</p>
      
      <div className={styles.startButton} onClick={handleStart}>
        开始测试
      </div>

      <p className={styles.startButtonText}>
        本测试包含 {props.totalNumber} 道题目
      </p>
      <p className={styles.startButtonText}>
        完整测试预计需要 15 分钟
      </p>
    </div>
  )
}

export default StartView
