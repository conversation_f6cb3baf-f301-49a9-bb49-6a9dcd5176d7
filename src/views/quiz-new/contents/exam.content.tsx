import useCutDown from '@/hooks/useCutDown'
import styles from './index.module.css'
import {
  forwardRef, useEffect, useImperativeHandle, useRef, useState
} from 'react'
import {formatTimeCount, hasMediaContent, playAudioWithWebAudio} from './utils'
import {Textarea} from '@/components/ui/textarea'
import useStartRecord from '@/hooks/useStartRecord'
import LottieAnimation from '@/components/business/LottieAnimation'
import {SelectViewEx} from '@/components/business/SelectViewExpand'
import {debounce} from 'lodash'
import {AnimatePresence, motion} from 'framer-motion'
import LottieAnimationStop from '@/components/business/LottieAnimation/stop'
import {playDingMusic} from '@/global/tool'
import ErrHint from '@/components/business/ErrHint'
import useStartNewRecord from '@/hooks/useStartNewRecord'
import {isWeChatMobileOnly} from '@/utils/permission'

interface Props {
  isOnlyView?: boolean
  model: any
  currentNumber: number
  totalNumber: number
  currentAnswer: any[]
  onNextQuestion: () => void

  onChangeText: (value: string) => void
  onMockData?: () => void
  onChangeSelect?(s: {[key: string]: string[]}, isInput?: boolean): void

}

const ExamContentView = (props: Props, ref: any) => {

  const [direction, setDirection] = useState('right')

  const [btnStatus, setBtnStatus] = useState('Continue')

  const currentAnswerRef = useRef<any[]>([])

  const examContentRef = useRef<HTMLDivElement>(null)

  const audioRefs = useRef<{[key: string]: HTMLAudioElement}>({})

  const {model, onChangeText, onChangeSelect} = props

  const playerRef = useRef<{[p: string]: string}>(null)

  const isAnswerVoice = model.answerType === 'voice'

  const [hasAnswer, setHasAnswer] = useState(false)

  useImperativeHandle(ref, () => ({updateDirection: () => setDirection('right')}))
  
  const {
    count, start, isRunning, stop
  } = useCutDown(props.model.answerTime!)
  
  const newRecordHook = useStartNewRecord()

  const oldRecordHook = useStartRecord()
  
  const {
    startRecording, stopRecording, isRecording, cleanup
  } = isWeChatMobileOnly() ? newRecordHook : oldRecordHook

  const selectValueRef = useRef<{[key: string]: string[]}>({})

  const selectInputsRef = useRef<{[key: string]: string}>({})

  const [clickOptions, setClickOptions] = useState<string>('')

  const isRecordingRef = useRef(false)

  const [playCount, setPlayCount] = useState(3)

  const [isPlaying, setIsPlaying] = useState(false)

  const [isPlayingVideo, setIsPlayingVideo] = useState(false)

  const [audioProgressTime, setAudioProgressTime] = useState(0)

  const [errHint, setErrHint] = useState({
    isOpen: false,
    icon: '',
    title: '',
    content: '',
    btnText: '',
    fn: () => {}
  })

  const showRetryToast = () => {
    const today = new Date().toISOString().split('T')[0]

    const storageKey = `recordFailCount_${today}_${model.id}`

    const currentFailCount = Number(localStorage.getItem(storageKey) || 0)

    const newFailCount = currentFailCount + 1

    if (newFailCount >= 3) {
      setErrHint({
        isOpen: true,
        icon: '😮‍💨', 
        title: '录制失败次数过多',
        content: '请尝试使用其他浏览器进行考试',
        btnText: '拷贝考试链接',
        fn: () => {
          navigator.clipboard.writeText(window.location.href)

          // window.location.reload()
          return true
        }
      })

      return
    }
    setErrHint({
      isOpen: true,
      icon: '😮‍💨',
      title: '录制失败',
      content: '点击重试按钮后可以继续测试',
      btnText: '重试',
      fn: () => {
        window.location.reload()
        localStorage.setItem(storageKey, String(newFailCount))
      }
    })
    // 同一场考试 同一天 录制失败的次数超过5次 则提示换浏览器考试 用storage记录

  }

  // 切换题目时，如果有滚动条则滚到顶部
  useEffect(() => {
    if (examContentRef?.current  && window.innerWidth < 568) {
      examContentRef.current.scrollTo({top: 0, behavior: 'smooth'})
    }
  }, [model.id])

  // 一开始就进行读题倒计时
  useEffect(() => {
    setHasAnswer(false)
    setClickOptions('')
    // start()

    return () => {
      setPlayCount(3)
      setAudioProgressTime(0)
    }
  }, [model.id])

  useEffect(() => {
    isRecordingRef.current = isRecording
  }, [isRecording])

  useEffect(() => {
    currentAnswerRef.current = props.currentAnswer
  }, [props.currentAnswer])

  // 切换题目时，如果题目有滚动条，则滚到顶部
  useEffect(() => {
    const examContent = document.getElementById('exam-content' + model.id)

    const examTitle = document.getElementById('exam-title' + model.id)

    const examContentBox = document.getElementById('exam-content-ref' + model.id)

    if (examContent && examTitle) {
      const titleHeight = examTitle.offsetHeight

      // console.log('examTitle height:', titleHeight)
      examContent.style.height = `calc(var(--vh, 1vh) * 100 - ${titleHeight + 30}px)`
    }
    if (examContentBox) {
      const hasScrollbar = examContentBox.scrollHeight > examContentBox.clientHeight

      if (hasScrollbar) {
        examContentBox.style.display = 'flex'
        examContentBox.style.alignItems = 'center'
        examContentBox.style.justifyContent = 'flex-start'
      }
    }
  }, [model.id])

  // 对于题目存在音视频的 一进来就开始播放音视频
  useEffect(() => {
    const hasAudio = hasMediaContent(model.content, 'audio')

    const hasVideo = hasMediaContent(model.content, 'video')

    const hasMedia = hasAudio || hasVideo

    cleanup()
    if (hasMedia) {
      if (hasAudio) {
        setTimeout(() => {
          handleAudioPlay()
        }, 800)
      }
      if (hasVideo) {
        setTimeout(() => {
          handleVideoPlay()
        }, 800)
      }

      return
    }
  }, [model.id])

  useEffect(() => {
    if (count === 0) {
      // 停止倒计时
      if (model.answerType === 'voice' && isRecordingRef.current) {
        checkCurrentAnswer()
        stop()

        return
      }      
      stop()
      props.onNextQuestion()
    }
  }, [count, model.id])

  const handleVideoPlay = () => {
    const video = document.getElementById(`video-${model.id}`) as HTMLVideoElement

    // 开始播放
    try {
      video?.play?.().then(() => {
        setIsPlayingVideo(true)
      }).catch((e) => {
        console.info(e, 'error 视频播放器2错误')
        setIsPlayingVideo(false)
      })
    } catch (error) {
      console.info(error, 'error')
    }
    video.addEventListener('ended', () => {
      setIsPlayingVideo(false)
    })
  }

  const handleAudioPlay = async () => {
    const audio = document.getElementById(`audio-${model.id}`) as HTMLAudioElement

    console.warn(audio.src, 'audio.src')
    // 微信移动端采用其他播放方式。
    // if (isWeChatMobileOnly()) {
    //   await playAudioWithWebAudio(audio.src, 2.0, {
    //     startCb: () => {
    //       setIsPlaying(true)
    //       const duration = audio.duration

    //       setAudioProgressTime(duration)
    //     },
    //     endCb: () => {
    //       setIsPlaying(false)
    //     }
    //   })
    //   setPlayCount(f => f - 1)

    //   return
    // }

    if (!audio?.paused) {
      return
    }
    try {
      audio?.play?.().then(() => {
        setIsPlaying(true)
        // 设置音频进度和倒计时
        // Get audio duration and set progress time
        const duration = audio.duration

        setAudioProgressTime(duration)
      })
      
    } catch (error) {
      console.info(error, 'error')
    }
    audio.addEventListener('ended', () => {
      setIsPlaying(false)
    })
    setPlayCount(f => f - 1)
  } 

  const checkCurrentAnswer = async () => {
    setBtnStatus('Loading')
    await stopRecording()
    cleanup()
    if (props?.isOnlyView || model?.id?.includes?.('_mock')) {
      props.onMockData?.()
      setBtnStatus('Hide')

      return
    }
    let timeoutCount = 0

    const MAX_RETRIES = 3

    const RETRY_INTERVAL = 1000

    const timer = setInterval(() => {

      const hasCurrentAnswer = currentAnswerRef.current.some(
        (item: any) => item.question_id === model.id
      )

      if (hasCurrentAnswer) {
        handleSuccess()

        // handleFailure()
        return
      }

      timeoutCount++
      if (timeoutCount >= MAX_RETRIES) {
        handleFailure()
      }
    }, RETRY_INTERVAL)

    const handleSuccess = () => {
      stop()
      clearInterval(timer)
      props.onNextQuestion()
      setBtnStatus('Continue')
    }

    const handleFailure = () => {
      clearInterval(timer)
      showRetryToast()
    }

  }

  const startRecord = () => {
    console.info('当前音频是否录制中', isRecordingRef.current, model.id)
    if (isRecordingRef.current) return
    setBtnStatus('Recording...')
    startRecording('', model.id)
  }
  
  const renderInlineInput = (item: {value: string}) => {
    const regex = /\{\{key\d+\}\}/g

    const parts = item.value.split(regex)

    const matches = item.value.match(regex) || []
    
    return (
      <div className="text-base leading-relaxed">
        {parts?.map((part, index) => (
          <span key={index}>
            {part}
            {index < matches?.length && (
              <input 
                type="text" 
                onChange={(e) => {
                  const key = matches[index].replace('{{', '').replace('}}', '')

                  const inputValue = e.target.value
                  
                  // Update selectValueRef with the key and value
                  if (!selectInputsRef.current) {
                    selectInputsRef.current = {}
                  }
                  selectInputsRef.current[key] = inputValue!
                  
                  // Call onChangeSelect to update the answer
                  // debugger
                  onChangeSelect?.(selectInputsRef.current as any, true)

                  setHasAnswer(true)
                }}
                className="inline-block mx-1 px-1 py-1 bg-transparent outline-none min-w-[40px] max-w-[80px] border-b border-gray-200 focus:border-gray-400"
              />
            )}
          </span>
        ))}
      </div>
    )
  }

  const renderQuestions = () => {
    // console.info(model.question, 'model.question')
    return model?.question?.map?.((item: any, index: number) => {
      console.info(item, 'item', model.questionType)
      if (model.questionType === 'inline_input' && item.type === 'text') {
        return (
          <div key={index}>
            {renderInlineInput(item)}
          </div>
        )
      }
      if ((model.questionType === 'read_aloud' || model.questionType === 'single_text_choice' || model.questionType === 'true_false') && item.type === 'text') {
        return (
          <div 
            key={item.value + index} 
            className={`relative font-['GTVCS-Bold'] font-[700] ${model.questionType === 'summarize_written_text' ? 'w-[100%] text-[16px]' : 'text-[23.24px]'} leading-[27.5px] tracking-[0px] text-[#070707] ${index < model.question?.length - 1 ? 'mb-[10px]' : ''}`} 
            style={{
              whiteSpace: 'pre-line', 
              wordBreak: 'break-word',
              textAlign: 'center'
            }}
          >
            {item.value}
          </div>
        )
      }
      if (model.questionType === 'summarize_text_with_voice' && item.type === 'text') {
        return (
          <div key={item.value + index} className={`summarize-text-with-voice w-[100%] text-[#070707] relative font-[16px] leading-[27.5px] tracking-[0px] text-[#070707] ${index < model.question?.length - 1 ? 'mb-[10px] font-[\'GTVCS-Book\']' : `${styles.stitle} font-[14px]`}`} 
            style={{
              whiteSpace: 'pre-line', 
              wordBreak: 'break-word',
              textAlign: 'left',
              paddingLeft: window.innerWidth >= 568 ? '30px' : '0'
            }}>
            {item.value}
          </div>
        )
      }
      if (item.type === 'text') {
        return (
          <div 
            key={item.value + index} 
            className={`relative font-['GTVCS-Bold'] font-[700] ${model.questionType === 'summarize_written_text' ? 'w-[100%] text-[16px]' : 'text-[23.24px]'} leading-[27.5px] tracking-[0px] text-[#070707] ${index < model.question?.length - 1 ? 'mb-[10px]' : ''}`} 
            style={{
              whiteSpace: 'pre-line', 
              wordBreak: 'break-word',
              textAlign: item.value.length > 60 ? 'left' : 'center'
            }}
          >
            {item.value}
          </div>
        )
      }
      const isMp3 = item?.value?.endsWith?.('.mp3')

      if (item.type === 'video' && !isMp3) {
        return (
          <div key={item.value + index} className='flex items-center justify-center'>
            <video 
              id={`video-${model.id}`}
              className={`relative text-base leading-[28px] text-[#3d4d5c] font-bold rounded-[15px] max-w-[600px] w-[calc(100vw-64px)] ${index < model?.content?.length - 1 ? 'mb-[10px]' : ''}`} 
              src={item.value} 
              playsInline
              autoPlay
              x5-playsinline={item.value}
              onError={(e) => {
                console.info(e, 'error 视频播放器1错误')
              }}
            />
            {!isPlayingVideo && <img id={`play-${model.id}`} src="/new/play.png" alt="play" className="w-[48px] h-[48px] mr-[5px] absolute " onClick={() => {
              // 录制中无法点击
              if (isRecordingRef.current || btnStatus === 'Recording...' || btnStatus === 'Loading') return
              if (!playerRef.current?.[model.id]) {
                handleVideoPlay()
              }
            }} />}
          </div>
        )
      }
      if (item.type === 'voice' || isMp3) {
        // console.info(audioProgressTime, 'audioProgressTime')
        return (
          <div key={item.value + index} className='flex flex-col items-center justify-center mb-[40px] '>
            <div 
              className={'w-[100px] h-[100px] rounded-[21px] bg-[rgba(237,40,39,0.5)] cursor-pointer flex items-center justify-center relative overflow-hidden'}
              onClick={() => {
                // 录制中无法点击
                console.info(playCount, 'playCount')
                if (isRecordingRef.current || btnStatus === 'Recording...' || btnStatus === 'Loading') return
                if (playCount < 1) return
                handleAudioPlay()
              }}
            >
              {audioProgressTime ? <div 
                className={`absolute top-0 left-0 flex items-center justify-center bg-[#ED2827] ${styles.animationView}`}
                key={audioProgressTime + playCount + model.id} 
                style={{animationDuration: `${audioProgressTime + 1}s`,}}
              /> : null}
              {isPlaying ? (
                <LottieAnimation name="record" size={100} />
              ) : (
                <LottieAnimationStop name="record" size={100} isPause={true} />
              )}
            </div>
            <audio 
              src={item.value} 
              ref={audioRefs.current[item.value] as any}
              id={`audio-${model.id}`} 

              style={{
                width: '1px !important', height: '1px !important', position: 'fixed', top: '-1000px', left: '-1000px'
              }} 
            />

            <div className='mt-[24px] w-[150px] h-[30px] flex-shrink-0 rounded-[9px] bg-[rgba(237,40,39,0.1)] text-[#ED2827] text-center font-["GTVCS-Black"] text-[15px] font-[900] leading-normal flex items-center justify-center'>
            Repalys Left: <span id='click-count'>{playCount}</span>
            </div>
          </div>
        )
      }
      if (item.type === 'image') {
        if (window.innerWidth < 568) {
          return (
            <div key={item.value + index} className='flex items-center justify-center w-[100%] h-[100%]'>
              <img src={item.value} alt="image" className='rounded-[15px] max-w-[calc(100%-32px)] max-h-[calc(100%-32px)]' />
            </div>
          )
        }

        return (
          <div key={item.value + index} className='flex items-center justify-center w-[100%] h-[100%]'>
            <img src={item.value} alt="image" className='rounded-[15px] max-w-[calc(100%-100px)] max-h-[calc(100%-100px)] min-h-[calc(300px)]' />
          </div>
        )
      }
      if (item.type === 'paragraph') {
        console.info(item, 'ooooooooo')
        const selects = item.value as unknown as {type: string, value: string}[]

        return (
          <div key={index + JSON.stringify(selects)} className="rounded-lg font-['GTVCS-Bold']">
            <div className="contents flex-wrap items-center gap-1 text-base leading-8 text-gray-700 whitespace-pre-wrap">
              {selects?.map?.((itm: any, idx: number) => {
                if (itm.type === 'text') {
                  return <span key={`text-${idx}-${item.value}`} className="text-[18px] text-[#070707] font-bold font-['GTVCS-Bold'] ">{itm.value}</span>
                }
                if (itm.type === 'select') {
                  return (
                    <div key={`select-${idx}`} className="inline-block mx-1 text-[18px]">
                      <SelectViewEx 
                        key={itm.type + idx + model.id + index}
                        title={itm.type + idx + model.id + index} 
                        options={[{key: 'Select', value: ' '}]?.concat(itm.value?.map((item: any) => ({key: item, value: item})))} 
                        onSelect={(e) => {
                          selectValueRef.current[itm.key] = [e.value] as any
                          onChangeSelect?.(selectValueRef.current!)
                          setHasAnswer(true)
                        }} />
                    </div>
                  )
                }

                return null
              })}
            </div>
          </div>
        )
      }
      if (item.type === 'option' && Object.keys(item.options)?.length) {
        const isImg = Object.values(item.options as Record<string, string>)?.[0]?.[0]?.startsWith?.('https')

        // console.info(isImg, '**************************', Object.values(item.options as Record<string, string>)?.[0])
        if (isImg) {
          return (
            <div className='grid grid-cols-2 md:grid-cols-4 gap-[10px] w-[100%]' key={index + item.value}                style={{padding: window.innerWidth >= 500 ? '0 40px' : '0'}}>
              {Object.keys(item.options).map((key, index) => {
                return (
                  <div 
                    key={index + key + model.id + index} 
                    className='flex items-center justify-center relative gap-[10px]' 
                    onClick={() => {
                      selectValueRef.current[key] = [item.options[key]]
                      // 只用单选
                      onChangeSelect?.({[key]: item.options[key]})
                      setClickOptions(key)
                      setHasAnswer(true)
                    }}
                  >
                    <img src={item.options[key]} alt="" className='flex-1 rounded-[12px]' />
                    {clickOptions === key && (
                      <img 
                        src="/new/chose.png" 
                        alt="" 
                        className='w-[28px] h-[28px] absolute bottom-[10px] right-[10px]' 
                      />
                    )}
                  </div>
                )
              })}
            </div>
          )
        }

        return <div className='mt-[30px] w-[100%] flex flex-col items-center justify-center gap-[10px]' key={index + item.value + model.id}>
          {
            Object.keys(item.options).map((key, index) => {
              return (
                <div 
                  key={index.toString() + '_' + key + '_' + model.id + item.options[key]?.toString()} 
                  className={`flex p-[12px] items-center relative w-[calc(100%-32px)] h-[50px] rounded-[7.5px] border-[0.5px] border-[#C3C3C3] font-["GTVCS-Bold"] font-[700] text-[15px] leading-[100%] tracking-[0px] ${clickOptions === key ? 'bg-[#FF862F] text-white' : 'bg-white text-[#070707]'}`}
                  onClick={() => {
                    selectValueRef.current[key] = [item.options[key]]
                    onChangeSelect?.({[key]: item.options[key]})
                    setClickOptions(key)
                    setHasAnswer(true)
                  }}
                >
                  {item.options[key]}
                </div>
              )   
            })
          }
        </div>
      }
    })
  }

  const renderAnswer = () => {
    if (model.answerType === 'text') {
      return (
        <Textarea 
          autoCorrect='off' 
          autoComplete='off' 
          key={model.id} 
          placeholder="Please enter the answer. " 
          style={{
            height: 150, marginTop: 24, outline: 'none !important', width: '90%',border: '1px solid #ccc',display: 'block',margin: '10px auto'
          }} 
          onChange={(e) => {
            onChangeText(e.target.value)
            setHasAnswer(true)
          }} 
        />
      )
    }
  }

  // 关闭所有音频 视频
  const closeAllAudioAndVideo = () => {
    const audios = document.getElementsByTagName('audio') || []

    const videos = document.getElementsByTagName('video') || []

    const media = [...audios, ...videos]

    Array.from(media).forEach(media => {
      media.pause()
    })
    setIsPlaying(false)
  } 

  const renderBtn = () => {
    // 回答类型不是语音
    if (!isAnswerVoice) {
      return <div 
        className={`${styles.examBtn} ${styles.examBtnMb1} ${hasAnswer ? 'bg-[#FF862F] hover:bg-[#393939] text-white ' : 'bg-white text-[#070707] opacity-50'}`} 
        onClick={debounce(() => {
          if (hasAnswer) {
            closeAllAudioAndVideo()
            setAudioProgressTime(0)
            setTimeout(() => {
              window.scrollTo(0, 0)
              stop()
              props.onNextQuestion()
            }, 300)
          }
        }, 500, {
          leading: true,  // 在延迟开始前调用
          trailing: false
        })}
        style={{
          transform: 'scale(1)',
          transition: 'transform 0.2s',
          WebkitTapHighlightColor: 'transparent' // 移除点击高亮
        }}
        onTouchStart={(e) => {
          const target = e.currentTarget as HTMLElement

          target.style.transform = 'scale(0.95)'
        }}
        onTouchEnd={(e) => {
          const target = e.currentTarget as HTMLElement

          target.style.transform = 'scale(1)'
        }}
        onTouchCancel={(e) => {
          const target = e.currentTarget as HTMLElement

          target.style.transform = 'scale(1)'
        }}
        onMouseDown={(e) => {
          const target = e.currentTarget as HTMLElement

          target.style.transform = 'scale(0.95)'
        }}
        onMouseUp={(e) => {
          const target = e.currentTarget as HTMLElement

          target.style.transform = 'scale(1)'
        }}
        onMouseLeave={(e) => {
          const target = e.currentTarget as HTMLElement

          target.style.transform = 'scale(1)'
        }}
      >Submit</div>
    }
    // 待开始 - 录制中 - 上传中
    if (btnStatus === 'Continue') {
      return <div className={`w-[60px] h-[60px] ${styles.examBtnMb2} ${styles.examBtn} ${styles.recording}`} 
        onClick={debounce(() => {
          // 关闭正在播放的音频 - 提前录制音频
          closeAllAudioAndVideo()
          startRecord()
          setBtnStatus('Loading')
          setTimeout(() => {
            // 开始录音倒计时
            start()
            setBtnStatus('Recording...')
          }, 400) 
          setTimeout(() => {
            playDingMusic(() => {
              setAudioProgressTime(0)
            })
          }, 500)
        }, 500, {
          leading: true,  // 在延迟开始前调用
          trailing: false
        })}
        style={{
          transform: 'scale(1)',
          transition: 'transform 0.2s',
          WebkitTapHighlightColor: 'transparent' // 移除点击高亮
        }}
        onTouchStart={(e) => {
          const target = e.currentTarget as HTMLElement

          target.style.transform = 'scale(0.95)'
        }}
        onTouchEnd={(e) => {
          const target = e.currentTarget as HTMLElement

          target.style.transform = 'scale(1)'
        }}
        onTouchCancel={(e) => {
          const target = e.currentTarget as HTMLElement
 
          target.style.transform = 'scale(1)'
        }}
        onMouseDown={(e) => {
          const target = e.currentTarget as HTMLElement

          target.style.transform = 'scale(0.95)'
        }}
        onMouseUp={(e) => {
          const target = e.currentTarget as HTMLElement

          target.style.transform = 'scale(1)'
        }}
        onMouseLeave={(e) => {
          const target = e.currentTarget as HTMLElement

          target.style.transform = 'scale(1)'
        }}>
        {/* <img 
          src='/new/recording-button.svg' 
          alt="start" 
          // style={{width: '16px', height: '24px', marginRight: '4px'}}
        />   */}
        {/* Record Now */}
        开始录音
      </div>
    }
    if (btnStatus === 'Recording...') {
      return [
        /**
         * 处理录音按钮点击事件：
         * 1. 关闭所有正在播放的音频/视频
         * 2. 开始录音
         * 3. 设置按钮状态为加载中
         * 4. 400ms后更新按钮状态为"录音中"
         * 5. 500ms后播放提示音并重置音频进度时间
         * 使用500ms防抖避免快速重复点击
         */
        <div className={`absolute left-1/2 transform -translate-x-1/2 ${window.innerWidth >= 768 ? 'bottom-[60px]' : 'bottom-[60px]'}`} key='recording'>
          <LottieAnimation name="recording" size={88} loop={true} />
        </div>,
        <div 
          className={`${styles.examBtn} ${styles.examBtnMb3} ${btnStatus === 'Recording...' ? styles.recording : ''} hover:bg-[#FF9C52]`} 
          onClick={debounce(async() => {
            setTimeout(async() => {
              stop()
              setAudioProgressTime(0)
              setBtnStatus('Loading')
              await checkCurrentAnswer()
            }, 200)
          }, 500, {
            leading: true,  // 在延迟开始前调用
            trailing: false
          })}
          style={{
            transform: 'scale(1)',
            transition: 'transform 0.2s',
            WebkitTapHighlightColor: 'transparent'
          }}
          onTouchStart={(e) => {
            const target = e.currentTarget as HTMLElement

            target.style.transform = 'scale(0.95)'
          }}
          onTouchEnd={(e) => {
            const target = e.currentTarget as HTMLElement

            target.style.transform = 'scale(1)'
          }}
          onTouchCancel={(e) => {
            const target = e.currentTarget as HTMLElement

            target.style.transform = 'scale(1)'
          }}
          onMouseDown={(e) => {
            const target = e.currentTarget as HTMLElement

            target.style.transform = 'scale(0.95)'
          }}
          onMouseUp={(e) => {
            const target = e.currentTarget as HTMLElement

            target.style.transform = 'scale(1)'
          }}
          onMouseLeave={(e) => {
            const target = e.currentTarget as HTMLElement

            target.style.transform = 'scale(1)'
          }}
          key='recording-btn'
        >
          完成录音({count})
        </div>
      ]
    }
    if (btnStatus === 'Loading') {
      return <div className={`${styles.examBtn} ${styles.recording}`}>
        <div className={styles.spinner}></div>
      </div>
    }
  }

  const slideVariants = {
    enter: (direction: string) => ({
      x: direction === 'right' ? 300 : -300,
      opacity: 0
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1
    },
    exit: (direction: string) => ({
      zIndex: 0,
      x: direction === 'right' ? -300 : 300,
      opacity: 0
    })
  }

  // console.info(model, '000000')

  //   console.info(isReadRunning, 'isReadRunning', readCount, 'readCount', isRunning, 'isRunning', count, 'count')
  return (
    <div className={styles.container}>
      <div className={styles.examTitle} id={'exam-title' + model.id}>
        <div className="flex items-center justify-center h-[20px]"> 
          {/* <div className="font-['GTVCS-Black'] font-[900] text-[17.5px] leading-[100%] tracking-[0px] text-center text-[#FFB32F] flex items-center">    
            {isRunning ? <img src="/new/clock.png" alt="time" className="w-[20px] h-[20px] mr-[5px]" /> : null}
            {isRunning ? formatTimeCount(count): ''}
          </div> */}
          <div className="font-['GTVCS-Black'] font-[900] text-[16px] leading-[100%] tracking-[0px] text-center text-white">
            <span className="text-[#FFB32F] font-bold text-[24px]">{props.currentNumber}</span>/{props.totalNumber}
          </div>
        </div>

        <div
          className="font-['GTVCS-Black'] font-[900] text-[24px] leading-[120%] tracking-[0px] text-center text-white mt-[24px] mb-[24px] whitespace-pre-line"
        >
          {model.introduction?.replace(/\\n/g, '\n')}
        </div>
      </div>
      <AnimatePresence initial={false} mode="popLayout" custom={direction}>
        <motion.div
          key={model.id}
          custom={direction}
          variants={slideVariants}
          initial="enter"
          animate="center"
          exit="exit"
          transition={{
            x: {type: 'spring', stiffness: 400, damping: 28},
            opacity: {duration: 0.15}
          }}
        >
          <div className={styles.examContent} id={'exam-content' + model.id}>
            <div ref={examContentRef} id={'exam-content-ref' + model.id} className={`scrollview flex-1 overflow-y-auto flex-col w-[100%] pb-[16px] ${
              ['summarize_written_text'].includes(model.questionType) ? 'block' : 'flex'
            } ${
              (() => {
                const isMobile = window.innerWidth <= 768

                if (model.questionType === 'fill_in_the_blanks') {
                  const paragraphQuestion = model?.question?.find((it: any) => it.type === 'paragraph')?.value?.filter?.((item: any) => item.type === 'text')?.reduce?.((sum: number, item: any) => sum + (item?.value?.length || 0), 0) || 0

                  const contentLength = paragraphQuestion

                  // console.info(contentLength, 'contentLength', model.question)

                  const maxLength = isMobile ? 400 : 1200
                  
                  return contentLength < maxLength ? 'items-center justify-center' : 'items-center justify-start'
                }
                if (model.questionType === 'summarize_text_with_voice') {
                  return isMobile ? 'flex items-center justify-center' : 'flex items-center justify-center'
                }
                if (model.questionType === 'single_text_choice') {
                  return isMobile ? 'flex items-center justify-flex-start' : 'flex items-center justify-center'
                }

                return 'items-center justify-center'
                
              })()
            }`}>
              {renderQuestions()}
              {renderAnswer()}
            </div>
            <div className={styles.examFooter}>
              {renderBtn()}
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
      <ErrHint 
        isOpen={errHint.isOpen} 
        onClose={() => {
          const isPrevent = errHint.fn?.() ?? false

          if (!isPrevent) {
            setErrHint({...errHint, isOpen: false})
          }
        }} 
        icon={errHint.icon} 
        title={errHint.title} 
        content={errHint.content} 
        btnText={errHint.btnText} 
      />
    </div>
  )
}

export default forwardRef(ExamContentView)