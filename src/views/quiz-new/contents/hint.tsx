'use client'
import <PERSON><PERSON><PERSON><PERSON> from '@/components/business/HeaderLogo'
import styles from './index.module.css'

interface Props {
  onClose: () => void
  isOpen: boolean
  introduction: string
  content: {type: string, value: string}[]
}

const HintView = (props: Props) => {
  if (!props.isOpen) return null
  const renderContent = () => {
    return props.content?.map((item, index) => {
      if (item.type === 'text') {
        return (
          <div key={index} className='font-black text-[20px] leading-[100%] tracking-[0px] text-center text-white'>
            {item.value}
          </div>
        )
      }

      return null
    })
  }

  return (
    <div className={styles.hintContainer}>
      <HeaderLogo />
      <div>
        <div className="max-w-[800px] w-[calc(100vw-32px)]">
          {renderContent()}
          <div 
            className="font-['GTVCS-Black'] font-black text-[24px] text-center text-white mt-[30px]" 
            style={{
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word'
            }}
          >
            {props.introduction.replace(/\\n/g, '\n')}
          </div>
        </div>
      </div>
      <div className={`${styles.startButton} ${styles.fixedButton}`} onClick={props.onClose}>
        继续
      </div>
    </div>
  )
}

export default HintView