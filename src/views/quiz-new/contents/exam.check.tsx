import useQuizStore from '@/store/quiz'
import {Button} from '@/components/ui/button'
import useGetAudio from '@/hooks/useGetAudio'
import {useState, useRef, useEffect} from 'react'
import styles from './index.module.css'
import useGetWxAudio from '@/hooks/useGetWxAudio'
import {isWeChatMobileOnly} from '@/utils/permission'
import {examInfo} from '@/global/hint'
import {debounce} from 'lodash'

const CheckContent = ({onNext}: { onNext: () => void }) => {
  const [countdown, setCountdown] = useState(0)

  const [playCountdown, setPlayCountdown] = useState(0)

  const [isRecording, setIsRecording] = useState(false)

  const {deviceInfo} = useQuizStore(state => state)

  const [isPlayingTest, setIsPlayingTest] = useState(false)

  const wxHook = useGetWxAudio()

  const unWxHook = useGetAudio()

  const {startRecording, stopRecording, audioUrl} =  isWeChatMobileOnly() ?   wxHook : unWxHook

  const audioRef = useRef<HTMLAudioElement | null>(null)

  useEffect(() => {
    checkAudioPermission()
    checkPlaybackPermission()
  }, [])

  const [hasAudioPermission, setHasAudioPermission] = useState(false)

  const [hasPlaybackPermission, setPlaybackPermission] = useState(false)

  const checkAudioPermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({audio: true})

      stream.getTracks().forEach(track => track.stop())
      setHasAudioPermission(true)
    } catch {
      setHasAudioPermission(false)
    }
  }
  
  const checkPlaybackPermission = async () => {
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

      const oscillator = audioContext.createOscillator()

      oscillator.connect(audioContext.destination)
      await audioContext.resume()
      oscillator.disconnect()
      await audioContext.close()
      setPlaybackPermission(true)
    } catch (error) {
      console.error('Playback permission error:', error)
      setPlaybackPermission(false)
    }
  }

  const handleStartRecording = () => {
    try {
      setCountdown(10)
      console.info('开始录制')
      startRecording(deviceInfo.audioDeviceId)
      const interval = setInterval(() => {
        setCountdown(prev => {
          if (prev === 1 || prev <= 0) {
            clearInterval(interval)
            setIsRecording(true)
            stopRecording()

            return 0
          }

          return prev - 1
        })
      }, 1000)
    } catch (error) {
      console.error('Error starting recording:', error)
    }
  }

  const startPlay = () => {
    console.log('Playing the recorded audio...')
    try {
      if (audioUrl) {
        const audio = new Audio(audioUrl)

        audioRef.current = audio
        audio.play()
        let t = 10

        const playInterval = setInterval(() => {
          if (t <= 0) {
            clearInterval(playInterval)

            return 0
          }
          setPlayCountdown(t - 1)
          t = t - 1
        }, 1000)
      } else {
        console.warn('No audio URL available to play.')
      }
    } catch (error) {
      console.error('Error playing audio:', error)
    }
  }

  const handleNext = () => {
    try {
      stopRecording()
      if (audioRef.current) {
        audioRef.current.pause()
        audioRef.current.currentTime = 0
      }
    } catch (error) {
      console.error('Failed to stop recording:', error)
    }
    onNext()
  }

  //   console.info(playCountdown, 'playCountdown')

  return (  
    <div className="w-screen h-screen flex flex-col items-center justify-center relative">
      <div className="w-full max-w-[600px] mx-auto bg-transparent rounded-lg relative px-4">
        <div className="w-full rounded-[8px] p-3 md:p-4 min-h-[200px] md:min-h-[250px]">
          <div>
            <h2 className="text-lg md:text-xl lg:text-2xl font-bold mb-3 md:mb-4 text-center text-gray-800 text-[#fff]" style={{color: '#fff'}}>设备检测</h2>
            <p className="mb-4 md:mb-6 text-center text-gray-600 text-sm md:text-base px-2 text-[#fff]" style={{color: '#fff'}}>请确保您的设备正常工作，以便顺利完成考试。</p>
            <div className="flex flex-col gap-4 md:gap-6 justify-center max-w-[500px] mx-auto px-2">
              <div className="bg-white/80 backdrop-blur-sm p-4 md:p-6 rounded-lg border border-gray-200/50 shadow-sm">
                <div className="flex items-center space-x-3 md:space-x-4">
                  <div 
                    className={`flex items-center justify-center rounded-full p-2 md:p-3 flex-shrink-0 ${hasAudioPermission ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}
                  >
                    <svg className="w-5 h-5 md:w-6 md:h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="flex-1 min-w-0 flex flex-col md:flex-row md:items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900 mb-2 text-sm md:text-base">麦克风权限检查</p>
                      <div className="flex items-center gap-2 mb-2">
                        <span className={`inline-block w-2 h-2 rounded-full ${hasAudioPermission ? 'bg-green-500' : 'bg-red-500'}`}></span>
                        <span className={`text-xs md:text-sm ${hasAudioPermission ? 'text-green-600' : 'text-red-600'}`}>
                          {hasAudioPermission ? '已授权' : '未授权'}
                        </span>
                      </div>
                    </div>
                    <div>
                      {!hasAudioPermission ? (
                        <Button 
                          className="bg-red-500 hover:bg-red-600 text-white px-3 md:px-4 py-2 rounded-md text-xs md:text-sm w-full md:w-auto"
                          onClick={checkAudioPermission}
                        >
                          申请权限
                        </Button>
                      ) : (
                        audioUrl ? <Button 
                          className={'bg-green-500 hover:bg-green-600 text-white px-3 md:px-4 py-2 rounded-md text-xs md:text-sm w-full md:w-auto'}
                          onClick={startPlay}
                          disabled={playCountdown > 0}
                        >
                          播放录音 {playCountdown > 0 ? `倒计时${playCountdown}s` : ''}
                        </Button> : <Button 
                          className={'bg-green-500 hover:bg-green-600 text-white px-3 md:px-4 py-2 rounded-md text-xs md:text-sm w-full md:w-auto'}
                          onClick={handleStartRecording}
                          disabled={countdown > 0}
                        >
                          {countdown > 0 ? `录制倒计时${countdown}s` : '开始录制'}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-white/80 backdrop-blur-sm p-4 md:p-6 rounded-lg border border-gray-200/50 shadow-sm">
                <div className="flex items-center space-x-3 md:space-x-4">
                  <div 
                    className={`flex items-center justify-center rounded-full p-2 md:p-3 flex-shrink-0 ${hasPlaybackPermission ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}
                  >
                    <svg className="w-5 h-5 md:w-6 md:h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.794L4.5 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.5l3.883-2.794a1 1 0 011.617.794zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="flex-1 min-w-0 flex flex-col md:flex-row md:items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900 mb-2 text-sm md:text-base">播放权限检查</p>
                      <div className="flex items-center gap-2 mb-2">
                        <span className={`inline-block w-2 h-2 rounded-full ${hasPlaybackPermission ? 'bg-green-500' : 'bg-red-500'}`}></span>
                        <span className={`text-xs md:text-sm ${hasPlaybackPermission ? 'text-green-600' : 'text-red-600'}`}>
                          {hasPlaybackPermission ? '已授权' : '未授权'}
                        </span>
                      </div>
                    </div>
                    <div>
                      {!hasPlaybackPermission ? (
                        <Button 
                          className="bg-red-500 hover:bg-red-600 text-white px-3 md:px-4 py-2 rounded-md text-xs md:text-sm w-full md:w-auto"
                          onClick={checkPlaybackPermission}
                        >
                          申请播放权限
                        </Button>
                      ) : (
                        <Button 
                          onClick={debounce(() => {
                            const audio = new Audio(examInfo.startVideoUrl || '')

                            setIsPlayingTest(true)
                            audio.play().then(() => {
                              setIsPlayingTest(true)
                            }).catch(() => {
                              setIsPlayingTest(false)
                            })
                            audio.addEventListener('ended', () => {
                              setIsPlayingTest(false)
                            })
                            audio.addEventListener('error', () => {
                              setIsPlayingTest(false)
                            })
                          }, 3000, {leading: true, trailing: false})}
                          disabled={isPlayingTest}
                          className="bg-green-500 hover:bg-green-600 text-white px-3 md:px-4 py-2 rounded-md text-xs md:text-sm w-full md:w-auto"
                        >
                          播放试听声音
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-end p-3 md:p-4">
          <div 
            className={`text-white ${styles.startButton}`} 
            onClick={handleNext}
          >
            下一步
          </div>
        </div>
      </div>
    </div>
  )
}

export default CheckContent
