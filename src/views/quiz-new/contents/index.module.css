.startButton {
    box-shadow: 0px 4px 82.6px 0px #FFFF5FCC;
    box-shadow: 0px 0px 37.5px 0px #FFFFFFE5 inset;
    width: calc(100vw - 100px);
    max-width: 320px;
    margin: auto;
    height: 60px;
    border-radius: 15px;
    color: #000000;
    background-color: #FFC640;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: 600;
    color: #000000;
    text-align: center;
    margin-top: 60px;
    margin-bottom: 20px;
}

.startButtonText {
    font-family: GTVCS-Bold;
    font-weight: 700;
    font-size: 15px;
    line-height: 100%;
    letter-spacing: 0px;
    text-align: center;
    color: #FFC641;
}

.fixedButton {
    position: fixed;
    bottom: 100px;
    display: flex;
}
.hintContainer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #080808 18.04%, #EC2726 100%);
    /* background-color: rgba(0, 0, 0, 0.5); */
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
}

.container {
    max-width: 1000px;
    margin: auto;
    width: 100%;
    height: calc(var(--vh, 1vh) * 100);
    display: flex;
    align-items: center;
    /* justify-content: center; */
    flex-direction: column;
}

.examTitle {
    width: calc(100% - 32px);
    max-width: 800px;
    margin: 0 auto;
    /* height: 100px; */
    padding-top: 24px;
}

.examContent {
    max-width: 800px;
    width: calc(100vw - 32px);
    height: calc(var(--vh, 1vh) * 100 - 150px);
    margin: 0 auto;
    background: linear-gradient(145.32deg, rgba(255, 255, 255, 0.95) 19.08%, rgba(255, 255, 255, 0.8) 80.32%);
    border-radius: 24px;
    margin-bottom: 24px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    flex-direction: column;
}

.examFooter {
    height: 148px;
    width: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: flex-end;
}

.examBtn {
    margin-bottom: 10px;
    bottom: 40px;
    width: 200px;
    height: 60px;
    background-color: #070707;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 17px;
    font-size: 23px;
    font-weight: 600;
    color: #fff;

    font-family: GTVCS-Black;
    font-weight: 900;
    font-size: 22.5px;
    line-height: 100%;
    letter-spacing: 0px;

}

.recording {
    background-color: #FF8E39;
}

.stitle {
  font-family: 'GTVCS-Bold';
}


.spinner {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: conic-gradient(#0000 10%, #fff);
    -webkit-mask: radial-gradient(farthest-side,#0000 calc(100% - 7px),#000 0);
    animation: spinner-zp9dbg 1s infinite linear;
 }
 
 @keyframes spinner-zp9dbg {
    to {
       transform: rotate(1turn);
    }
 }

 .animationView {
    height: 100%;
    width: 100%;
    animation: progressAnimation 1s linear;
 }

 @keyframes progressAnimation {
    0% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
 }      

 