.hintContainer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #080808 18.04%, #EC2726 100%);
    /* background-color: rgba(0, 0, 0, 0.5); */
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;

    flex-direction: column;
}
  .levelBars {
    display: flex;
    align-items: flex-end;
    height: 80px;
    width: 210px;
    /* background-color: #222; */
    margin-bottom: 16px;
    overflow: hidden;
    margin: 0 auto;
    margin-bottom: 40px;
    justify-content: center;
  }
  
  .tipText {
    color: #FFF;
    text-align: center;
    font-family: "SF Compact";
    font-size: 16px;
    font-style: normal;
    font-weight: 457;
    line-height: normal;
    margin-top: 54px;
  }
  
  .errorMsg {
    color: #f87171;
    text-align: center;
    margin-top: 8px;
  }
  .startButton {
    box-shadow: 0px 4px 82.6px 0px #FFFF5FCC;
    box-shadow: 0px 0px 37.5px 0px #FFFFFFE5 inset;
    width: calc(100vw - 100px);
    max-width: 320px;
    margin: auto;
    height: 60px;
    border-radius: 15px;
    color: #000000;
    background-color: #FFC640;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: 600;
    color: #000000;
    text-align: center;
    margin-top: 60px;
    margin-bottom: 20px;
}

.startButtonText {
    font-family: GTVCS-Bold;
    font-weight: 700;
    font-size: 15px;
    line-height: 100%;
    letter-spacing: 0px;
    text-align: center;
    color: #FFC641;
}

.fixedButton {
    position: fixed;
    bottom: 100px;
    display: flex;
}
  .disabled {
    opacity: 0.5;
    pointer-events: none;
  }