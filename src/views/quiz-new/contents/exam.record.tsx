'use client'
import <PERSON>er<PERSON><PERSON> from '@/components/business/HeaderLogo'
import styles from './record.module.css'
import {useEffect, useRef, useState} from 'react'

interface Props {
  onNext: () => void
}

const ExamRecordView = (props: Props) => {
  const audioContextRef = useRef<AudioContext | null>(null)

  const analyserRef = useRef<AnalyserNode | null>(null)

  const microphoneRef = useRef<MediaStreamAudioSourceNode | null>(null)

  const dataArrayRef = useRef<Uint8Array | null>(null)

  const animationFrameIdRef = useRef<number | null>(null)

  const streamRef = useRef<MediaStream | null>(null)

  const [errorMsg, setErrorMsg] = useState<string | null>(null)

  const [isListening, setIsListening] = useState(false)

  const [hasUserInteracted, setHasUserInteracted] = useState(false)

  // 仅在用户首次交互后才初始化麦克风，避免首次进入被阻止
  const handleUserStart = async () => {
    setHasUserInteracted(true)
    await startTest()
  }

  useEffect(() => {
    // 只有在用户交互后才启动麦克风
    startTest()

    return () => {
      stopTest()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasUserInteracted])

  const startTest = async () => {
    // setErrorMsg(null)
    try {
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
      }
      const audioContext = audioContextRef.current

      const stream = await navigator.mediaDevices.getUserMedia({audio: true})

      streamRef.current = stream

      const analyser = audioContext.createAnalyser()

      analyser.fftSize = 256
      analyserRef.current = analyser

      const bufferLength = analyser.frequencyBinCount

      const dataArray = new Uint8Array(bufferLength)

      dataArrayRef.current = dataArray

      createLevelBars()

      const microphone = audioContext.createMediaStreamSource(stream)

      microphoneRef.current = microphone
      microphone.connect(analyser)

      setIsListening(true)
      updateLevelDisplay()

      stream.getTracks().forEach(track => {
        track.onended = () => {
          stopTest()
        }
      })
    } catch (err: any) {
      setIsListening(false)
      let msg = '麦克风初始化失败'

      if (err?.name === 'NotAllowedError') {
        msg = '您已拒绝麦克风访问权限，请刷新页面并允许访问。'
      } else if (err?.name === 'NotFoundError') {
        msg = '未检测到麦克风设备，请检查设备连接。'
      } else if (err?.message) {
        msg = err.message
      }
      setErrorMsg(msg)
    }
  }

  const stopTest = () => {
    setIsListening(false)
    if (animationFrameIdRef.current) {
      cancelAnimationFrame(animationFrameIdRef.current)
      animationFrameIdRef.current = null
    }
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
    if (audioContextRef.current) {
      try {
        audioContextRef.current.close()
      } catch {}
      audioContextRef.current = null
    }
    analyserRef.current = null
    microphoneRef.current = null
    dataArrayRef.current = null
  }

  const updateLevelDisplay = () => {
    if (!dataArrayRef.current) return

    analyserRef.current?.getByteFrequencyData(dataArrayRef.current)

    let sum = 0

    dataArrayRef.current.forEach(value => sum += value)
    const average = sum / dataArrayRef.current.length

    const levelBars = document.getElementById('levelBars') as HTMLElement | null

    if (!levelBars) return

    const bars = levelBars.querySelectorAll('.level-bar')

    bars.forEach((bar, index) => {
      const value = dataArrayRef.current![index] || average

      const height = ((value || 10) / 255) * 100

      const el = bar as HTMLElement

      el.style.height = `${height}%`
      if (height > 50) {
        el.className = 'level-bar bg-success'
      } else if (height > 30) {
        el.className = 'level-bar bg-success'
      } else {
        el.className = 'level-bar bg-primary-low'
      }
    })

    animationFrameIdRef.current = requestAnimationFrame(updateLevelDisplay)
  }

  const createLevelBars = () => {
    const levelBars = document.getElementById('levelBars') as HTMLElement | null

    if (!levelBars) return
    levelBars.innerHTML = ''
    for (let i = 0; i < 7; i++) {
      const bar = document.createElement('div')

      bar.className = 'level-bar bg-primary-low'
      bar.style.height = '0%'
      bar.style.width = '8px'
      bar.style.marginRight = '6px'
      bar.style.display = 'inline-block'
      bar.style.verticalAlign = 'bottom'
      bar.style.transition = 'height 0.1s linear'
      bar.style.borderRadius = '4px'
      levelBars.appendChild(bar)
    }
  }

  const onNextFn = async () => {
    stopTest()
    props.onNext()
  }

  return (
    <div className={styles.hintContainer}>
      <HeaderLogo />
      <div>
        <div id="levelBars" className={styles.levelBars} />
        <p className={styles.tipText}>
          请说话或制造一些声音来测试话筒<br />
          如果条形图随着声音变化，则表示麦克风正常工作
        </p>
        {errorMsg && <div className={styles.errorMsg}>{errorMsg}</div>}
      </div>
      <div
        className={`${styles.startButton} ${styles.fixedButton} ${!isListening ? styles.disabled : ''}`}
        onClick={onNextFn}
      >
          录音正常
      </div>
    </div>
  )
}

export default ExamRecordView
