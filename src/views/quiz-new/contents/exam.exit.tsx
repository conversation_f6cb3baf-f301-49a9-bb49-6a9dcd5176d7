'use client'
import Header<PERSON>ogo from '@/components/business/HeaderLogo'
import styles from './index.module.css'

interface Props {
  totalNumber?: number
  onNext: () => void
}

const ExitHintView = (props: Props) => {
  return (
    <div className={styles.hintContainer}>
      <HeaderLogo />  

      <div>
        <img src="/new/exit.png" alt="exit" className="w-[80px] h-[80px] mb-[30px] mx-auto" />
        <p className="font-['SF Compact'] font-[457] text-[16px] leading-[100%] tracking-[0px] text-center text-white mt-[5px] w-[300px]">
            检测到你有正在进行中的考试，请继续完成本次测试
        </p>
      </div>
      <div className={`${styles.startButton} ${styles.fixedButton}`} onClick={props.onNext}>
        继续测试
      </div>

      <p className={`${styles.startButtonText} ${styles.fixedButton}`} style={{bottom: 80}}>
        本测试包含 {props.totalNumber} 道题目
      </p>
      <p className={`${styles.startButtonText} ${styles.fixedButton}`} style={{bottom: 60}}>
      完整测试预计需要 15 分钟
      </p>
    </div>
  )
}

export default ExitHintView

// 本测试包含 20 道题目 完整测试预计需要 15 分钟