'use client'
import <PERSON>er<PERSON><PERSON> from '@/components/business/HeaderLogo'
import styles from './index.module.css'
import {useEffect, useState} from 'react'
import {checkMicrophonePermission, requestMicrophonePermission} from './utils'

interface Props {
  onNext: () => void
}

const ExamRequestView = (props: Props) => {
  const [btnText, setBtnText] = useState('')  

  useEffect(() => {
    const reqPermission = async () => {
      const res = await checkMicrophonePermission()

      if (res) {
        setBtnText('测试话筒')
      } else {
        setBtnText('申请权限')
      }
    }

    reqPermission()
  }, [])  

  const onNextFn = async () => {
    if (btnText === '测试话筒') {
      props.onNext()

      return
    }
    const isAllow = await requestMicrophonePermission()

    if (isAllow) {
      setBtnText('测试话筒')
    } else {
      // 拒绝了权限
    }
  }

  return (
    <div className={styles.hintContainer}>
      <HeaderLogo />  

      <div>
        <img src="/new/re.svg" alt="exit" className="w-[80px] h-[80px] mb-[30px] mx-auto" />
        <p className="font-['SF Compact'] font-[457] text-[16px] leading-[100%] tracking-[0px] text-center text-white mt-[5px] w-[300px]">
            点击下方按钮请求访问话筒权限
        </p>
      </div>
      <div className={`${styles.startButton} ${styles.fixedButton}`} onClick={onNextFn}>
        {btnText}
      </div>
    </div>
  )
}

export default ExamRequestView