'use client'
import {ErrorBoundary} from '@/components/business/ErrBoundary'
import {Avatar, AvatarFallback, AvatarImage} from '@/components/ui/avatar'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {getStorage, getStorageJson} from '@/utils/storage'
import React, {useEffect, useState} from 'react'

const Management = () => {
  const [userInfo, setUserInfo] = useState<any>({})

  const [lists, setLists] = useState<{login_url: string;link_url: string;query_params:{name: string; value: string}[]}[]>([])

  useEffect(() => {
    const token = getStorage('management-token')

    if (!token) {
      // router.push('/login')
    } else {
      const fetchConfigData = async () => {
        try {
          const response = await fetch('https://gusto-english-oss.wemore.com/fe/config.json')

          const data = await response.json()

          console.log('Config Data:', data)
          setLists(data)
        } catch (error) {
          console.error('Error fetching config data:', error)
        }
      }

      fetchConfigData()
    }
    const info = getStorageJson('useInfo')

    setUserInfo(info)
  }, [])

  const renderQuery = (queryLists: {name: string; value: string; key?: string}[]) => {
    return queryLists.map((xx, id) => {
      if (xx.value === 'name') {
        return <p key={100 + id}>{xx.name|| xx.key}: 当前登录用户名</p>
      }
      if (xx.value === 'avatar') {
        return <p key={200 + id}>{xx.name|| xx.key}: 当前用户头像</p>
      }
      if (xx.value === 'id') {
        return <p key={300 + id}>{xx.name|| xx.key}: 当前的用户id</p>
      }
      if (xx.value === 'token') {
        return <p key={400 + id}>{xx.name || xx.key}: 登录token凭证</p>
      }
    })
  }

  const renderRow = () => {
    return (
      lists?.map((item, ndd) => {
        return (
          <TableRow key={ndd}>
            <TableCell className="font-medium border">
              {item.login_url}
            </TableCell>
            <TableCell className="border">
              {item.link_url}
            </TableCell>
            <TableCell className="border">
              {renderQuery(item.query_params)}
            </TableCell>
          </TableRow>
        )
      })
    )
  }

  return (
    <>
      <div className="w-screen h-screen flex flex-col items-center justify-center relative">
        <div className="w-[1200px] h-[800px] mx-auto bg-white p-4">
          <div className="flex items-center text-lg font-bold mb-4">
            <Avatar>
              <AvatarImage src={userInfo?.avatar} />
              <AvatarFallback>CN</AvatarFallback>
            </Avatar>
            {userInfo?.name}
          </div>
          <Table className="border border-e6e6e6 overflow-x-auto">
            <TableHeader className="border overflow-x-auto">
              <TableRow>
                <TableHead className="border w-[400px]">登录路径</TableHead>
                <TableHead className="border">跳转路由</TableHead>
                <TableHead className="border">路由参数</TableHead>
                {/* <TableHead className="border">操作</TableHead> */}
              </TableRow>
            </TableHeader>
            <TableBody>
              {renderRow()}
            </TableBody>
          </Table>
        </div>
      </div>
    </>
  )
}

const ManagementWrapper = () => <ErrorBoundary><Management /></ErrorBoundary>

export default ManagementWrapper
