'use client'
import {getPaymentHistoryApi} from '@/http/http'
import {useEffect, useState} from 'react'
import styles from './index.module.css'

const PayHistoryView = () => {
  const [lists, setLists] = useState<any>([])

  useEffect(() => {
    getHistory()
  }, [])

  const getHistory = async () => {
    getPaymentHistoryApi().then((r) => {
    //   console.warn(r, 'r')
      setLists(r.data.data)
    })
  }

  const renderLists = () => {
    return lists?.map?.((item: any) => {
      return (
        <div
          key={item.season + item.paid_at}
          className="flex items-center justify-between h-[55px] border-b mb-[16px]"
          style={{borderColor: '#E8E8E8', width: '100%'}}
        >
          <div className={styles.left}>
            <div
              className={`${styles.left_top} font-normal text-[14px] text-[#000000]`}
            >
              {item.season}
            </div>
            <div
              className={`${styles.left_bot} font-normal text-[12px] text-[#7A7A7A] mt-[3px]`}
            >
              {new Date(item.paid_at)
                .toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                })
                .replace(/\//g, '-')}
            </div>
          </div>
          <div className={styles.right}>
            <div
              className={`${styles.left_top} font-[600] text-[15px] text-[#FF3333] text-right`}
            >
              ¥{item.amount/ 100}
            </div>
            <div
              className={`${styles.left_bot} font-normal text-[12px] text-[#7A7A7A] mt-[3px]`}
            >
              {item.method}
            </div>
          </div>
        </div>
      )
    })
  }

  return (
    <div className="p-[16px] bg-white h-[100vh] w-[100%]">
      <h2 className="text-black text-[18px] font-semibold mb-[20px]">
        支付记录
      </h2>
      {renderLists()}
    </div>
  )
}

export default PayHistoryView