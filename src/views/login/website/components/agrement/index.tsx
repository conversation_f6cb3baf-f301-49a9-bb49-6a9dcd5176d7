import React from 'react'
import styles from './agreement.module.css'

const Agreement = () => {
  return (
    <div className={styles.agreementContainer}>
      <h1 className={styles.title}>Gusto English 英语社区用户协议</h1>
      <h2 className={styles.sectionTitle}>一、协议双方</h2>
      <p className={styles.paragraph}>1.服务提供方：[北京摸着石头过河科技有限公司]，即Gusto English英语社区服务方（以下简称 “平台”），注册地址为 [北京市朝阳区广顺南大街16号院1号楼14层1601室04]，联系方式：[18616526276/<EMAIL> ]。</p>
      <p className={styles.paragraph}>2.用户：指使用本平台英语社区产品服务并同意接受本协议约束的个人或单位。</p>

      <h2 className={styles.sectionTitle}>二、服务内容</h2>
      <p className={styles.paragraph}>1.平台为付费用户提供英语学习社区服务，包括但不限于在线课程、学习资料分享、互动交流活动、专家讲座、学习打卡监督、学习进度跟踪与反馈等服务，具体服务内容以平台实际提供为准。</p>
      <p className={styles.paragraph}>2.平台有权根据自身运营安排，对服务内容进行调整、优化或更新，但应提前通知用户，确保用户的知情权。</p>

      <h2 className={styles.sectionTitle}>三、付费与订阅</h2>
      <p className={styles.paragraph}>1.用户确认购买平台英语社区产品的付费服务，应按照平台指定的支付方式支付相应费用。支付成功后，用户将获得所购买服务的访问权限，服务期限自付费成功之日起计算，具体时长以用户所购服务套餐为准。</p>
      <p className={styles.paragraph}>2.平台目前提供的付费套餐包括：[200元注册费——用于解锁Gravity 101+ Echo 101 两个应用的无限制观看权限，每月500元社区服务费——获得包括但不限于Gravity 101的每月社区服务]。用户应根据自身需求选择合适的套餐，并在购买时仔细确认所选套餐的内容、价格及服务期限等信息。一旦支付成功，除本协议另有约定外，平台不接受用户以任何理由提出的退款或变更套餐申请。</p>
      <p className={styles.paragraph}>3.用户应确保其支付账户有足够的余额或信用额度完成支付，并承担因支付账户问题导致的一切法律责任和后果。如因用户支付账户问题导致支付失败或延迟，平台不承担任何责任，但应协助用户解决支付问题（如提供必要的支付信息查询和技术支持等）。</p>

      <h2 className={styles.sectionTitle}>四、用户权利与义务</h2>
      <h3 className={styles.subSectionTitle}>1.权利：</h3>
      <ul className={styles.list}>
        <li className={styles.listItem}>在服务期限内，用户有权按照本协议约定使用平台提供的英语社区服务，并享受相应的服务权益。</li>
        <li className={styles.listItem}>用户有权对平台的服务质量提出意见和建议，平台应及时回复并在合理范围内进行改进。</li>
        <li className={styles.listItem}>如平台违反本协议约定，用户有权要求平台承担相应的违约责任，包括但不限于退还未使用的服务费用、赔偿因平台违约给用户造成的直接经济损失等。</li>
      </ul>

      <h3 className={styles.subSectionTitle}>2.义务：</h3>
      <ul className={styles.list}>
        <li className={styles.listItem}>用户应遵守国家法律法规及平台的各项规章制度，不得在平台上发布任何违法、违规、侵权、淫秽、恐怖、暴力、骚扰、虚假、欺诈、传销或其他违反公序良俗的信息或行为，不得利用平台从事任何非法活动。如用户违反上述规定，平台有权立即终止本协议，并保留追究用户法律责任的权利，同时用户已支付的服务费用不予退还。</li>
        <li className={styles.listItem}>用户应妥善保管其在平台注册的账号和密码，不得将账号转借、出租、共享或转让给任何第三方使用。如因用户账号保管不善导致账号被盗用或遭受其他损失，用户应自行承担责任；如因用户账号被盗用等原因给平台或其他用户造成损失的，用户应负责赔偿。</li>
        <li className={styles.listItem}>用户应按照平台的要求提供真实、准确、完整的个人信息，并在信息发生变更时及时更新。如因用户提供虚假信息或未及时更新信息导致平台无法提供服务或服务出现差错的，平台不承担任何责任，同时用户应承担由此给平台造成的一切损失。</li>
        <li className={styles.listItem}>用户应尊重平台的知识产权和其他合法权益，不得对平台提供的服务内容进行复制、修改、传播、销售、出租、反编译、破解或进行其他侵犯平台知识产权的行为。如用户违反上述规定，平台有权要求用户停止侵权行为，并赔偿平台因此遭受的一切损失，包括但不限于直接经济损失、间接经济损失、律师费、诉讼费、公证费等维权费用。</li>
        <li className={styles.listItem}>用户应积极参与平台组织的学习活动，遵守学习社区的规则和秩序，尊重其他用户和授课教师，不得在学习社区内进行任何干扰正常学习秩序或影响其他用户学习体验的行为，如恶意刷屏、发送垃圾邮件、辱骂他人、挑起争端等。如用户违反上述规定，平台有权对其进行警告、禁言、踢出社区等处理措施，情节严重的，平台有权立即终止本协议，并保留追究用户法律责任的权利，同时用户已支付的服务费用不予退还。</li>
      </ul>

      <h2 className={styles.sectionTitle}>五、平台权利与义务</h2>
      <h3 className={styles.subSectionTitle}>1.权利：</h3>
      <ul className={styles.list}>
        <li className={styles.listItem}>平台有权根据本协议约定向用户收取服务费用，并对用户的付费资格进行审核和管理。</li>
        <li className={styles.listItem}>平台有权制定和修改平台的各项规章制度和服务规则，包括但不限于学习社区规则、课程安排、资料使用规则等，并在平台上进行公示。用户应遵守平台制定的各项规章制度和服务规则，如用户不同意修改后的规则，应停止使用平台服务；如用户继续使用平台服务，则视为用户接受修改后的规则。</li>
        <li className={styles.listItem}>平台有权对用户在平台上的行为进行监督和管理，如发现用户存在违反本协议约定或平台规章制度的行为，平台有权采取相应的措施进行处理，包括但不限于警告、限制账号使用权限、终止本协议、追究用户法律责任等，同时平台有权保留相关证据。</li>
        <li className={styles.listItem}>平台有权根据自身运营需要，对平台的服务内容、技术架构、页面设计等进行调整、优化或更新，包括但不限于暂停、终止部分或全部服务，更换授课教师、调整课程安排、更新学习资料等，但平台应提前通知用户，并确保用户的知情权和选择权。如因平台进行上述调整、优化或更新导致用户无法正常使用平台服务或服务质量受到影响的，平台应采取合理措施尽量减少对用户的影响，并在合理范围内对用户进行补偿（如延长服务期限、提供额外的学习资源等）。</li>
      </ul>

      <h3 className={styles.subSectionTitle}>2.义务：</h3>
      <ul className={styles.list}>
        <li className={styles.listItem}>平台应按照本协议约定的服务内容和标准，为用户提供稳定、可靠、高质量的英语社区服务，并确保服务的正常运行和安全性。如因平台原因导致服务中断、故障、数据丢失或其他影响用户正常使用的情况，平台应及时采取措施恢复服务，并对用户因此遭受的直接经济损失承担赔偿责任，但平台对因不可抗力、用户自身原因、第三方原因等导致的服务中断或故障不承担责任。</li>
        <li className={styles.listItem}>平台应保护用户的个人信息安全，采取合理的安全措施防止用户个人信息泄露、篡改、毁损等情况的发生。平台应按照法律法规的要求，对用户个人信息进行保密，并仅在为实现本协议目的所必需的范围内使用用户个人信息。如平台违反上述规定，导致用户个人信息泄露或遭受其他损失的，平台应承担相应的法律责任和赔偿责任。</li>
        <li className={styles.listItem}>平台应建立健全的客户服务体系，及时解答用户在使用平台服务过程中遇到的问题和咨询，处理用户的投诉和建议，并在合理时间内给予用户回复和解决方案。平台客服联系方式：[www.gustoenglish.com]。</li>
      </ul>

      <h2 className={styles.sectionTitle}>六、知识产权与保密条款</h2>
      <ul className={styles.list}>
        <li className={styles.listItem}>平台提供的英语社区服务内容（包括但不限于课程资料、学习文档、音频视频、软件程序、界面设计、商标标识等）均受知识产权法律法规保护，平台享有其知识产权。未经平台书面授权，用户不得对上述服务内容进行任何形式的复制、传播、修改、改编、演绎、翻译、汇编、反向工程、反编译或其他侵犯平台知识产权的行为。</li>
        <li className={styles.listItem}>双方应对在本协议履行过程中知悉的对方商业秘密、技术秘密、用户信息等予以保密，未经对方书面同意，不得向任何第三方披露或使用。本条款的保密义务不因本协议的终止而解除，且保密期限为自本协议生效之日起 [3]年。</li>
      </ul>

      <h2 className={styles.sectionTitle}>七、协议的变更与终止</h2>
      <ul className={styles.list}>
        <li className={styles.listItem}>平台有权根据法律法规的变化、平台运营情况或其他合理因素，对本协议进行修订，并在平台上发布修订后的协议。修订后的协议自发布之日起生效，用户应及时查看并了解协议的变更内容。如用户在协议变更后继续使用平台服务，则视为用户接受修订后的协议；如用户不同意修订后的协议，应停止使用平台服务，并可按照本协议约定的退款条件申请退还未使用的服务费用（如有）。</li>
        <li className={styles.listItem}>在服务期限内，如遇以下情形之一，本协议可提前终止：
          <ul className={styles.list}>
            <li className={styles.listItem}>用户在约定的社区开始7日内提出社区服务费用（500元/月）的退款申请，可以进行全额退款</li>
            <li className={styles.listItem}>注册费用（200元/长期）在社区开始后不接受退款。</li>
            <li className={styles.listItem}>如平台因法律法规变化、政策调整、技术升级、不可抗力等原因无法继续提供本协议约定的服务，平台应提前通知用户，并按照用户实际使用服务的时间和情况，退还用户未使用的服务费用（如有），本协议自通知发出之日起终止。</li>
          </ul>
        </li>
      </ul>

      <h2 className={styles.sectionTitle}>八、违约责任</h2>
      <ul className={styles.list}>
        <li className={styles.listItem}>用户未按照本协议约定支付服务费用的，平台则根据规则自动关闭用户的产品使用权限及终止对用户的服务行为</li>
        <li className={styles.listItem}>您理解并同意，公司提供的“Gravity 101”；“Echo 101”软件及相关社区服务是按照现有技术和条件所能达到的现状提供的。公司会尽最大努力向您提供服务，确保服务的连贯性和安全性。您理解，公司不能随时预见和防范技术以及其他风险，包括但不限于不可抗力、网络原因、第三方服务瑕疵、第三方网站等原因可能导致的服务中断、不能正常使用本软件及服务以及其他的损失和风险。</li>
      </ul>

      <h2 className={styles.sectionTitle}>九、争议解决</h2>
      <ul className={styles.list}>
        <li className={styles.listItem}>本协议的签订、履行、解释及争议解决均适用 [中华人民共和国] 法律。</li>
        <li className={styles.listItem}>双方在履行本协议过程中如发生争议，应首先通过友好协商解决；协商不成的，任何一方均有权向有管辖权的人民法院提起诉讼。</li>
      </ul>

      <h2 className={styles.sectionTitle}>十、其他条款</h2>
      <ul className={styles.list}>
        <li className={styles.listItem}>本协议自用户支付服务费用成功之日起生效，有效期至用户所购服务期限届满之日止。本协议一式两份，双方各执一份，具有同等法律效力。</li>
        <li className={styles.listItem}>本协议未尽事宜，可由双方另行签订补充协议进行约定。补充协议与本协议具有同等法律效力；如补充协议与本协议存在冲突，以补充协议为准。</li>
      </ul>

      <p className={styles.paragraph}>用户确认已仔细阅读并理解本协议的全部内容，同意接受本协议的约束，并自愿支付服务费用购买平台Gusto English英语社区产品服务。</p>

      <p className={styles.paragraph}>用户签字（或电子签名）：__________________</p>
      <p className={styles.paragraph}>日期：______年____月____日</p>
      <p className={styles.paragraph}>平台盖章：____________________</p>
      <p className={styles.paragraph}>日期：______年____月____日</p>
      <div style={{height: '72px'}} id='agreement-end' ></div>
    </div>
  )
}

export default Agreement
