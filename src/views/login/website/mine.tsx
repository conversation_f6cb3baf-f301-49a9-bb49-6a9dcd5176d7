'use client'
import {useEffect, useState} from 'react'
import styles from './index.module.css'
import {getStorage, getStorageJson, setStorage} from '@/utils/storage'
import {useRouter} from 'next/navigation'
import {
  getDeltaStatusApi, getExamMaxLevelApi, getGemTotalApi, getUserMineInfoApi
} from '@/http/http'
import {formatTime, scoreDesc, scoreMap} from './utils'
import {Toaster} from '@/components/ui/toaster'
import {
  Drawer, DrawerContent, DrawerTrigger, DrawerTitle
} from '@/components/ui/drawer'
import GemListView from './gem'
import PayHistoryView from './history'

// const deltaProduct = ['gusto-delta-monthly', 'gusto-delta-monthly-and-registration']

export type LevelInfo = keyof typeof scoreDesc
const WebsiteMineView = () => {
  const router = useRouter()

  const [payUrl, setUpdateUrl] = useState('')

  console.info(payUrl, 'payUrl')
  const [certificate, setCertificate] = useState<any>({})

  const [user, setUser] = useState<any>({})

  const [payInfo, setPayInfo] = useState<any>({})

  const [levelInfo, setLevelInfo] = useState<LevelInfo>()

  const [gemInfo, setGemInfo] = useState<any>({})

  useEffect(() => {
    if (!getStorageJson('userInfo')?.id) {
      // 跳转到登录页面
      router.push('/website/login')

      return
    }
    window.parent.postMessage({data: getStorage('useInfo')}, '*')
    getUserMineInfo()
    getPayInfo()
    getLevel()
    getDeltaStatus()
    setStorage('isMine', true)
    const userInfo = getStorageJson('useInfo')

    const path = 'https://gravity-pay.wemore.com/delta?token=' + userInfo?.token

    setUpdateUrl(path)
    setUser(userInfo || {})
  }, [])

  const getUserMineInfo = async () => {
    const res = await getUserMineInfoApi()

    console.log(res, '000000')
    setCertificate(res.data?.data)
  }

  const getDeltaStatus = async () => {
    const res = await getDeltaStatusApi()

    console.warn(res, 'res')
  }

  useEffect(() => {
    if (user?.id) {
      getGemTotal()
    }
  }, [user])

  const getGemTotal = async () => {
    const res = await getGemTotalApi({user_id: user.id})

    setGemInfo(res.data?.data)
  }

  const getPayInfo = async () => {
    // const res = await getUserPayInfo()

    // console.log(res,'111')
    // const deltaProductLists = res?.data?.data.filter((item: any) => deltaProduct.includes(item.id))

    // const hasPurchase = Boolean(deltaProductLists.filter((i: { was_purchased_or_subscribed: boolean; }) => i.was_purchased_or_subscribed)?.length)

    // console.log(hasPurchase,'hasPurchase', deltaProductLists)
    const res = await getDeltaStatusApi()

    const {is_paid, expire_at} = res?.data?.data || {}

    if (is_paid) {
      setPayInfo({date: formatTime(expire_at), product: 'Delta', text: '续费'})
    } else {
      setPayInfo({product: 'Delta', text: '报名'})
    }
  }

  // 获取用户等级
  const getLevel = async () => {
    const res = await getExamMaxLevelApi()
 
    console.log(res, '当前用户等级')
    setLevelInfo(res.data.data.score?.score)
  }

  // 重新考试
  // const onReset = () => {
  //   // router.push('/quiz/month_assessment')
  //   const user = getStorageJson('userInfo')

  //   console.warn(user, 'user', deltaExamLink + `?sid=${user.token}&uid=${user.id}` )
  //   // return
  //   window.location.href = deltaExamLink + `?sid=${user.token}&uid=${user.id}` 
  // }

  const linkCertificate = () => {
    router.push('/website/certificate')
  }

  // const linkScore = () => {
  //   router.push('/website/score')
  // }

  const linkGem = () => {
    router.push('/website/gem')
  }
  
  const openMenu = () => {
    const sidebar = document.getElementById('sidebar')

    if (sidebar) {
      sidebar.style.width = '250px'
      sidebar.style.transition = 'width 0.3s ease-in-out'
    }
  }

  const linkToMobile = () => {
    router.push('/website/pay-history')
  }

  const onLogout = () => {
    setStorage('x-5e-token', '')
    setStorage('useInfo', '')
    setStorage('userInfo', '')
    window.parent.postMessage({data: 'logout'}, '*')
    router.push('/website/login')
  }

  return (
    <>
      <div className={styles.page}>
        <div className={styles.content}>
          <div className={styles.header}>
            <div className={styles.avatar}>
              <img src={user.avatar} alt="" className={styles.avatar_img} />
            </div>  
            <div className={styles.avatarRight}>
              <p className={styles.name}>{user.name}</p>
              <span className={styles.gamma_level}>{certificate?.gamma_level || '普通用户'}</span>
            </div>

            <div className={styles.logout} onClick={onLogout}>
              退出登录
            </div>
          </div>
          <div className={styles.pc}>
            <div className={styles.pc_content}>
              <div className={styles.pc_content_item_0}>
                <img src="/images/pro.png" alt="" style={{height: '180px'}} />
                <div className={styles.pro_text}>Gusto English 火热报名中</div>
                {payInfo.date && <div className={styles.pro_time}>会员有效期: {payInfo.date}  </div>}
              
                {/* {payInfo.product && payInfo.text && <a className={styles.pro_btn} href={payUrl} target="_blank">{payInfo.text}</a>} */}
                {<Drawer direction="right">
                  <DrawerTrigger asChild>
                    <div className={styles.watch_history} onClick={openMenu}>
                      查看支付记录
                      <img src='/pay/right.png' alt='' />
                    </div>
                  </DrawerTrigger>
                  <DrawerContent style={{width: '500px', marginLeft: 'calc(100vw - 500px)'}}>
                    <DrawerTitle></DrawerTitle>
                    <div className="w-auto h-full">  
                      <PayHistoryView />
                    </div>
                  </DrawerContent>
                </Drawer>}
              </div>
              <div
                className={`${styles.pc_content_item} ${styles.pc_content_item2}`}
                style={{width: '160px !important'}}
                onClick={linkCertificate}
              >
                <img src="/images/zs.png" alt="" className={styles.pc_content_item2_img} />
                <div className={styles.zs_text}>
                证书
                  <img src="/images/zs-right1.png" alt="" />
                </div>
                <p>{certificate.certificates?.length || 0}</p>
              </div>
              <div
                className={`${styles.pc_content_item} ${styles.pc_content_item2}`}
                // onClick={linkCertificate}
                style={{width: '160px !important'}}
              >
                <img src="/images/gem.png" alt="" className={styles.pc_content_item2_img} />
                <Drawer direction="right">
                  <DrawerTrigger asChild>
                    <div className={styles.zs_text}>
                    Gem
                      <img src="/images/zs-right1.png" alt="" />
                    </div>
                  </DrawerTrigger>
                  <DrawerContent style={{width: '500px', marginLeft: 'calc(100vw - 500px)'}}>
                    <DrawerTitle></DrawerTitle>
                    <div className="w-auto h-full">  
                      <GemListView />
                    </div>
                  </DrawerContent>
                </Drawer>
                <p>{gemInfo.gems_count || 0}</p>
              </div>
            </div>

            <div className={styles.pc_content2}>
              <img src="/images/to.png" className={styles.icto} alt="" />
              <div className={styles.p_text} style={{textAlign: 'center'}}>
              根据欧洲语言共同参考框架（CEFR）定义的语言水平标准，您当前是：
              </div>
              <div className={styles.level_text}>{levelInfo || 0}</div>
              <div className={styles.level_name}>{scoreMap[levelInfo || 0]}</div>
              {scoreDesc[levelInfo!]?.map((item, index) => (
                <p key={index} className={styles.p_item}>{item}</p>
              ))}
              {/* <div className={styles.reset} onClick={onReset}>
                {!levelInfo ? '开始测试' : '重新测试'}
              </div> */}
            </div>

          </div>

          <div className={styles.mobile}>
            <div className={styles.mobile_content}>
              <div className={styles.mobile_content_item}>
                <img src="/images/pro.png" alt="" />
                <div className={styles.pro_text}>Gusto English 火热报名中</div>
                {payInfo.date && <div className={styles.pro_time}>会员有效期: {payInfo.date}  </div>}
              
                {/* {payInfo.product && payInfo.text && <a className={styles.pro_btn} href={payUrl} target="_blank">{payInfo.text}</a>} */}

                {<div className={styles.watch_history} onClick={linkToMobile}>
                  查看支付记录
                  <img src='/pay/right.png' alt='' />
                </div>}
              </div>

              <div className={styles.mobile_content_item}>
                <div
                  className={`${styles.pc_content_item} ${styles.pc_content_item2}`}
                  onClick={linkCertificate}
                >
                  <img src="/images/zs.png" alt="" className={styles.pc_content_item2_img} />
                  <div className={styles.zs_text}>
                  证书
                    <img src="/images/zs-right.png" alt="" />
                  </div>
                  <p>{certificate.certificates?.length || 0}</p>
                </div>
                <div
                  className={`${styles.pc_content_item} ${styles.pc_content_item2}`}
                  onClick={linkGem}
                >
                  <img src="/images/gem.png" alt="" className={styles.pc_content_item2_img} />
                  <div className={styles.zs_text}>
                  Gem
                    <img src="/images/zs-right.png" alt="" />
                  </div>
                  <p>{gemInfo.gems_count || 0}</p>
                </div>
              </div>
            </div>
            <div className={styles.pc_content2}>
              <img src="/images/to.png" className={styles.icto} alt="" />
              <div className={styles.p_text}>
              根据欧洲语言共同参考框架（CEFR）定义的语言水平标准，您当前是：
              </div>
              <div className={styles.level_text}>{levelInfo || 0}</div>
              <div className={styles.level_name}>{scoreMap[levelInfo || 0]}</div>
              {scoreDesc[levelInfo!]?.map((item, index) => (
                <p key={index} className={styles.p_item}>{item}</p>
              ))}
              {/* <div className={styles.reset} onClick={onReset}>
                {!levelInfo ? '开始测试' : '重新测试'}
              </div> */}
            </div>
          </div>
        </div>      
      </div>
      <Toaster />
    </>
  )
}

export default WebsiteMineView
