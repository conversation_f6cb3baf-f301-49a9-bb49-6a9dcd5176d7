'use client'
import {useEffect, useState} from 'react'
import styles from './index.module.css'
import {deltaExamLink, scoreDesc, scoreMap} from './utils'
import {LevelInfo} from './mine'
import {getExamMaxLevelApi} from '@/http/http'
import {getStorageJson} from '@/utils/storage'

const ScoreView = () => {
  const [levelInfo, setLevelInfo] = useState<LevelInfo>()

  useEffect(() => {
    getLevel()
  }, [])
  const getLevel = async () => {
    const res = await getExamMaxLevelApi()
 
    console.log(res, '当前用户等级')
    setLevelInfo(res.data?.data?.score?.score || 0)
  }

  const onReset = () => {
    const user = getStorageJson('userInfo')

    window.location.href = deltaExamLink + `?sid=${user.token}&uid=${user.id}` 
  }

  return (
    <div className={styles.page}>
      <div className={styles.content}>
        <div className={styles.pc_content3}>
          <div className={styles.p_text2}>
            根据欧洲语言共同参考框架（CEFR）定义的语言水平标准，您当前是：
          </div>
          <div className={styles.level_text2}>{levelInfo}</div>
          <div className={styles.level_name}>{scoreMap[levelInfo || 0]}</div>
          <div className={styles.level_desc}>
            {scoreDesc[levelInfo!]?.map((item, index) => (
              <p key={index} className={styles.level_desc_item}>
                {item}
              </p>
            ))}
          </div>
        </div>

        <div className={styles.reset} onClick={onReset}>
          {!levelInfo ? '开始测试' : '重新测试'}
        </div>
      </div>
    </div>
  )
}

export default ScoreView
