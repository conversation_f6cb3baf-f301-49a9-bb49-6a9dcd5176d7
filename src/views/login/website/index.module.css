.login {
    display: flex;
    justify-content: center;
    overflow: hidden;
    width: 100vw;
    height: 100vh;
    align-items: center;
    background: url('/images/bg-pc.png') no-repeat;
}


.login_container {
    width: 350px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    padding-top: 200px;
    padding: 32px 32px;
    box-sizing: content-box;
    /* margin-top: 200px; */
    border-radius: 44px;
}
.name {
    font-size: 16px;
    font-weight: 600;
    line-height: 21.22px;
    text-align: center;
    color: #333333;    
}

.gamma_level {
    margin-top: 4px;
    background-color: #F5F5F5;
    font-size: 10px;
    font-weight: 600;
    line-height: 13.26px;
    text-align: left;
    color: #9B9B9B;
    padding: 2px 6px;
}

@media (max-width: 500px) {
    .login_container {
        /* margin-top: 120px; */
    }
}

.login_icon {
    display: block;
    margin: 0 auto !important;
    width: 80px;
    height: 80px;
}
.login_title {
    
    font-size: 30px;
    font-weight: 600;
    line-height: 38px;
    text-align: center;
    margin-top: 24px;
    width: 100%;
    text-align: center;
    color: #101828;
    /* margin-bottom: 32px; */
}
.login_des {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #9B9B9B;
    margin-bottom: 32px; 
}

.ipt_item {
    margin-bottom: 20px;
}

.ipt_before {
    
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    text-align: left;
    color: #344054;
    margin-bottom: 6px;
}
.ipt_text {
    margin-top: 10px;
    
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
    color: #475467;
}

.form_item {
    width: 100%;
    height: 44px;
    border-radius: 50px;
    /* background: #FFFFFF; */
    border: 0;
    outline: none;
    padding: 0 12px;
    box-sizing: border-box;

    
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    text-align: left;
    color: #667085;
    background-color: #F4F4F4;
    /* border: 1px solid #D0D5DD; */
}

.form_item_btn {
    color: #FFF;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    width: 175px;
    margin-left: 20px;

    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    padding: 4px 8px;
    background-color: #F42047;
    border-radius: 50px;
    margin-right: 10px;
}

.ipt_form {
    width: 100%;
    height: 50px;
    display: flex;
    flex-direction: row;
    position: relative;
    background-color: #F4F4F4;
    border-radius: 50px;
    display: flex;
    align-items: center;
}

.form_btn {
    margin-top: 4px;
    width: 100%;
    height: 50px;
    border-radius: 53px;
    /* background: #7F56D9; */
    background: linear-gradient(180deg, #FF7984 0%, #F42047 100%);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 18px;
}

.area_code {
    width: 57px;
    position: absolute;
    height: 100%;
    left: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.down {
    width: 15px;
    height: 20px;
    position: absolute;
    right: 0px;
}

.modal_container {
    max-height: 630px;
    overflow-y: auto;
}

.phone_item {
    width: 432px;
    height: 52px;
    border: 1px solid #EAECF0;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-radius: 8px;
    justify-content: space-between;
    cursor: pointer;
}

.active {
    background: #7F56D9;
    color: #fff;
}

.position_right {
    position: absolute;
    right: 16px;
    top: 20px;
}

.err_text {
    width: 100%;
    height: 20px;
    line-height: 20px;

    color: #EA3434;
    font-size: 14px;
    margin-top: -10px;
    margin-bottom: 10px;
}


@media (max-width: 500px) {
    .login {
        background: url('/images/bg-mobile.png') no-repeat;
        align-items: flex-start;
    }
    .login_container {
        background-color: transparent;
        margin-top: 120px;
    }
    .login_title {
        font-size: 22px;
    }
    .ipt_before {
        display: none;
    }
}


/* -------------------------------- */
.page {
    width: 100vw;
    min-height: 100vh;
    background-color: #fff;
    display: flex;
    justify-content: center;
    padding: 0 16px;
    box-sizing: border-box;
    padding-bottom: 20px;
}
.content {
    width: 685px;
}

.header {
    height: 80px;
    display: flex;
    margin-top: 37px;
    align-items: center;
    position: relative;
}
.logout {
    position: absolute;
    right: 16px;
    font-size: 14px;
    font-weight: 400;
    line-height: 18.56px;
    color: #FF3333;
    cursor: pointer;
}
.avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.avatar_img {
    min-width: 80px;
    min-height: 80px;
    object-fit: cover;
}

.avatarRight {
    margin-left: 10px;
}

.avatarRight p {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
}

.pc_content {
    display: flex;
    gap: 14px;
    margin-top: 40px;
}

.pc_content_item_0 {
    flex: 2;
    background-color: #F4F4F4;
    border-radius: 14px;
    position: relative;
    height: 180px;
}
.pc_content_item {
    flex: 1;
    background-color: #F4F4F4;
    border-radius: 14px;
    position: relative;
    height: 180px;
}

.pro_text {
    position: absolute;
    top: 76px;
    left: 16px;
    font-size: 15px;
    font-weight: 600;
    color: #fff;
}
.pro_btn {
    width: 100px;
    height: 40px;
    border-radius: 40px ;
    /* opacity: 0px; */
    font-size: 15px;
    font-weight: 600;
    line-height: 19.89px;
    text-align: left;
    color: #FE1C82;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 20px;
    bottom: 20px;
    background-color: #fff;
}
.pro_time {
    position: absolute;
    top: 103px;
    left: 16px;
    font-size: 12px;
    color: #fff;
}

.pc_content_item2 {
    padding: 24px 20px;
    font-size: 15px;
    color: #333333;
    font-weight: 600;
}

.pc_content_item2_img {
    width: 33px;
    height: 33px;
    border-radius: 50%;
}
.zs_text {
    display: flex;
    margin-top: 7px;
    margin-bottom: 11px;
    align-items: center;
}

.pc_content2 {
    background-color: #F4F4F4;
    margin-top: 15px;
    border-radius: 14px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 40px;
    padding-bottom: 36px;
    flex-direction: column;
}
.icto {
    width: 60px;
    height: 60px;
}

.p_text {
    font-size: 16px;
    font-weight: 600;
    line-height: 21.22px;
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 0 16px;
}

.level_text {
    font-size: 40px;
    line-height: 54px;
    font-weight: 600;
    color: #F42047;
}

.level_name {
    font-size: 16px;
    line-height: 22px;
    font-weight: 600;
    color: #F42047;
    margin-bottom: 20px;
}

.p_item {
    font-size: 12px;
    font-weight: 400;
    line-height: 24px;
    color: #808080;
    list-style: circle;
    text-align: left;
    padding: 0 16px;
}

.reset {
    margin: 20px auto;
    width: 140px;
    height: 40px;
    gap: 10px;
    border-radius: 53px;
    /* opacity: 0px; */
    background: linear-gradient(180deg, #FF7984 0%, #F42047 100%);

    font-size: 17px;
    font-weight: 500;
    line-height: 23.8px;
    color: #FFF;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.c_item {
    width: 100%;
    border-bottom: 1px solid #E5E5E5;
    padding-bottom: 30px;
    padding-top: 30px;
}

.c_item .img {
    width: 100%;
    height: 385px;
}
.c_bottom {
    display: flex;
    width: 100%;
    margin-top: 40px;

    justify-content: space-between;
}

.c_name {
    font-size: 16px;
    font-weight: 600;
    line-height: 21.22px;
    color: #333333;
}

.c_tag {
    display: flex;
    margin-top: 6px;
}
.c_tag_item {
    font-size: 10px;
    font-weight: 600;
    padding: 2px 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #F5F5F5;
    color: #9B9B9B;
}
.c_bottom_right {
    display: flex;
    gap: 10px;
}
.save {
    width: 80px;
    height: 34px;
    background-color: #E5E5E5;
    display: flex;
    justify-content: center;
    align-items: center;

    font-size: 12px;
    font-weight: 500;
    color: #333333;
    cursor: pointer;
    border-radius: 30px;

}


.copy {
    width: 80px;
    height: 34px;
    background: linear-gradient(180deg, #FF7984 0%, #F42047 100%);

    display: flex;
    justify-content: center;
    align-items: center;

    font-size: 12px;
    font-weight: 500;
    color: #fff;
    cursor: pointer;
    border-radius: 30px;
}

.certificate_page {
    width: 100vw;
    height: 100vh;
    background-color: #fff;
    overflow-y: auto;
}

.certificate_content {
    width: 700px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
}

.mobile {
    display: none;
}

.mobile_content_item {
    margin-top: 20px;
    display: flex;
    gap: 15px;
    position: relative;
}

.pc_content3 {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 67px 0;
}

.p_text2 {
    font-size: 16px;    
    font-weight: 600;
    line-height: 21.22px;
    text-align: center;
    color: #333333;
}
.level_text2 {
    font-size: 40px;
    font-weight: 600;
    line-height: 53.04px;
    text-align: center;
    color: #F42047;
    margin-top: 20px;
}
.level_name2 {
    font-size: 16px;
    font-weight: 600;
    line-height: 21.22px;
    text-align: center;
    color: #F42047;
    margin-bottom: 40px;
}
.level_name3 {
    font-size: 40px;
    font-weight: 600;
    line-height: 21.22px;
    text-align: center;
    color: #F42047;
    margin-bottom: 10px;
}
.level_desc_item {
    font-size: 12px;
    font-weight: 400;
    line-height: 24px;
    text-align: left;
    color: #808080;
    position: relative;
    padding-left: 20px;
}
.level_desc_item::before {
    content: '';
    position: absolute;
    left: 3px;
    top: 9px;
    width: 4px;
    height: 4px;
    background-color: #808080;
    border-radius: 50%;
}
@media (max-width: 500px) {
    .certificate_content {
        width: 100%;
        padding: 0 16px;
        box-sizing: border-box;
    }

    .c_bottom  {
        margin-top: 16px;
    }

    .pc {
        display: none;
    }
    .mobile {
        display: block;
    }
}


.watch_history {
    position: absolute;
    left: 16px;
    bottom: 16px;
    /* z-index: 111; */
    font-size: 12px;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    cursor: pointer;
}



.login_type {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    gap: 24px;
}

.login_type span {
    font-weight: 590;
    font-size: 17px;
    color: #B2A9A1;
    cursor: pointer;
}

.login_type .active2 {
    color: #33312E;
}

.zs_text img {
    width: 17px;
    height: 17px;
}