'use client'
import {getGemListsApi} from '@/http/http'
import {useEffect, useRef, useState} from 'react'
import styles from './index.module.css'
import {getStorageJson} from '@/utils/storage'
import {filterRepeatId} from './utils'

const GemListView = () => {
  const [lists, setLists] = useState<any>([])

  const wrapperRef = useRef<HTMLDivElement>(null)

  const allowOnceRef = useRef(false)

  const nextCursor = useRef<any>('')

  useEffect(() => {
    getGemLists()
  }, [])

  const getGemLists = async () => {
    if (!getStorageJson('userInfo')?.id) return
    getGemListsApi({user_id: getStorageJson('userInfo')?.id, req: {limit: 30} }).then((r) => {
      console.warn(r, 'r')
      setLists(r.data.data?.list)
      nextCursor.current = r.data.data?.cursor
    })
  }

  /**
   * 处理滚动事件，当滚动到页面底部时触发加载更多数据
   */
  const onScroll = () => {
    // 滚动到最底部时加载更多数据
    if (!wrapperRef.current) return
    
    if (allowOnceRef.current) return
    const {scrollTop, scrollHeight, clientHeight} = wrapperRef.current

    // 当滚动到距离底部50px时加载更多
    const bottomThreshold = 50
    
    if (scrollHeight - (scrollTop + clientHeight) < bottomThreshold) {
      console.log('加载更多数据')
      allowOnceRef.current = true
      loadMore()
    }
  }

  const loadMore = () => {
    console.log('加载更多数据')

    if (!nextCursor.current) return
    getGemListsApi({user_id: getStorageJson('userInfo')?.id, req: {limit: 30, cursor: nextCursor.current} }).then((r) => {
      console.warn(r, 'r')
      setLists((t: any) => filterRepeatId([...(t || []), ...r.data.data?.list]))
      nextCursor.current = r.data.data?.cursor
    }).finally(() => {
      allowOnceRef.current = false
    })
  }

  const renderLists = () => {
    return lists?.map?.((item: any) => {
      return (
        <div
          key={item.id}
          className="flex items-center justify-between h-[55px] border-b mb-[16px]"
          style={{borderColor: '#E8E8E8', width: '100%'}}
          id={item.id}
        >
          <div className={styles.left}>
            <div
              className={`${styles.left_top} font-normal text-[14px] text-[#000000] font-semibold`}
            >
              {item?.reason?.replace('Convert', '')} {item.remark}
            </div>
            <div
              className={`${styles.left_bot} font-normal text-[12px] text-[#7A7A7A] mt-[3px]`}
            >
              {new Date(item.created_time)
                .toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                })
                .replace(/\//g, '-')}
            </div>
          </div>
          <div className={styles.right}>
            {item.amount > 0 ? <div
              className={`${styles.left_top} font-[600] text-[15px] text-[#FF3333] text-right`}
            >
              +{item.amount}
            </div> : <div
              className={`${styles.left_top} font-[600] text-[15px] text-[#000000] text-right`}
            >
              {item.amount}
            </div>}
            <div
              className={`${styles.left_bot} font-normal text-[12px] text-[#7A7A7A] mt-[3px]`}
            >
              {item.method}
            </div>
          </div>
        </div>
      )
    })
  }

  return (
    <div className="p-[16px] bg-white h-[100vh] w-[100%] overflow-y-auto" onScroll={onScroll} id='wrapper' ref={wrapperRef}>
      <h2 className="text-black text-[18px] font-semibold mb-[20px]">
        Gem 历史记录
      </h2>
      {renderLists()}
    </div>
  )
}

export default GemListView