'use client'

import {getStorage, getStorageJson, setStorage} from '@/utils/storage'
import styles from './style/pay.module.css'
import {useRouter, useSearchParams} from 'next/navigation'
import {useEffect, useRef, useState} from 'react'
import Agreement from './components/agrement'
import {debounce} from 'lodash'
import {BrowserDetector} from '@/global/devices'
import {
  requestAlipayNativePay, requestJsApiConfig, requestOrderStatus, requestProductInfo, requestWechatCodeSave, requestWechatJsApiPay, requestWechatNativePay
} from '@/http/http'
import {checkHasBuyProduct, linkHref, ProductPayInfo} from './utils'
import {QRCode} from 'react-qrcode-logo'
import {useResizeVh} from './hooks/useResizeVh'
import usePageShow from './hooks/usePageShow'
import useZoom from './hooks/useZoom'

type ProductInfo = {
  original_price?: number;
  additional_price?: number;
  price?: number;
  was_purchased_or_subscribed?: boolean;
  slug?: string;
}

const PayView = () => {

  useResizeVh()
  usePageShow()
  useZoom()

  const router = useRouter()

  const [isLogin, setIsLogin] = useState(false)

  const [isAgreementEnd, setIsAgreementEnd] = useState(false)

  const [productInfo, setProductInfo] = useState<ProductInfo>({})

  const searchParams = useSearchParams()

  const code = searchParams?.get('code')

  const timerRef = useRef<any>(null)

  const [qrInfo, setQrInfo] = useState({
    open: false, url: '', img: '', openImg: false
  })

  const [alipayQrInfo, setAlipayQrInfo] = useState({
    open: false, url: '', img: '', openImg: false
  })

  useEffect(() => {
    // 判断 code 有无被消费
    const baseMap = getStorageJson('baseMap') || {}

    if (code && !baseMap[code]) {
      getProductInfo(true)
    } else {
      getProductInfo()
    }
  }, [code])

  const getProductInfo = async (isPay?: boolean) => {
    const res = await requestProductInfo()

    if (res?.status === 200) {
      console.warn(res?.data?.data, 'res?.data?.data')
      const hasBuyProductIds = checkHasBuyProduct(res?.data?.data) || getStorageJson('userInfo')?.is_beta_user

      const productId = hasBuyProductIds ? ProductPayInfo.old_monthly : ProductPayInfo.new_monthly

      const product = res?.data?.data?.find((item: { id: ProductPayInfo; }) => item.id === productId)

      setProductInfo(product)
      if (isPay) {
        launchJsApiPay(product)
      }
    }
  }

  const launchJsApiPay = async (info?: any) => {
    try {
      await requestWechatCodeSave(code!)
      const baseMap = getStorageJson('baseMap') || {}

      baseMap[code!] = 'hasConsume'
      setStorage('baseMap', baseMap)
      const params = {product_slug: productInfo.slug! || info.slug}

      const res = await requestWechatJsApiPay(params)

      const jsApiRes = res?.data?.data?.jsapi_result

      // console.warn(res,'========')
      startOrderInterval(res?.data?.data?.order_id)
      // 存在订单id 可以开始轮训
      const w = window as any

      const onBridgeReady = () => {
        w.WeixinJSBridge.invoke('getBrandWCPayRequest', {
          'appId': jsApiRes.app_id,   //公众号ID，由商户传入
          'timeStamp': jsApiRes.timestamp.toString(),   //时间戳，自1970年以来的秒数
          'nonceStr': jsApiRes.nonce_str,      //随机串
          'package': jsApiRes.package,
          'signType': jsApiRes.sign_type,     //微信签名方式：
          'paySign': jsApiRes.pay_sign //微信签名
        },
        (res: any) => {
          console.warn('回调res', res)
          if (res.err_msg == 'get_brand_wcpay_request:ok') {
            // 使用以上方式判断前端返回,微信团队郑重提示：
            //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
          }
        })
      }

      if (typeof w.WeixinJSBridge == 'undefined') {
        const doc = document as any

        if (doc.addEventListener) {
          doc.addEventListener('WeixinJSBridgeReady', onBridgeReady, false)
        } else if (doc.attachEvent) {
          doc.attachEvent('WeixinJSBridgeReady', onBridgeReady)
          doc.attachEvent('onWeixinJSBridgeReady', onBridgeReady)
        }
      } else {
        onBridgeReady()
      }
    } catch (error) {
      console.warn(error, 'error')
    }
  }

  const startOrderInterval = (orderId: string, fn?: () => void) => {
    if (!orderId) return
    clearInterval(timerRef.current)
    timerRef.current = setInterval(() => {
      requestOrderStatus(orderId).then((t: any) => {
        console.warn(t, '开始轮训2222')
        if (t.data?.data?.status === 'Success') {
          clearInterval(timerRef.current)
          setProductInfo(f => ({...f, was_purchased_or_subscribed: true}))
          fn?.()
        }
      })
    }, 1000)
  }

  const handlePay = () => {
    if (getStorage('userInfo')) {
      setIsLogin(true)
    } else {
      router.push('/website/login', {scroll: false})
      setStorage('isPay', true)
    }
  }

  const handleScroll = () => {
    const agreementEnd = document.getElementById('agreement-end')

    if (agreementEnd) {
      const rect = agreementEnd.getBoundingClientRect()

      const isVisible = rect.top >= 0 && rect.bottom <= window.innerHeight

      if (isVisible) {
        console.log('agreement-end is visible')
        setIsAgreementEnd(true)
      } else {
        // console.log('agreement-end is not visible');
      }
    }
  }

  const getWechatPayment = async () => {
    const payload = {product_slug: productInfo?.slug}

    const res = await requestWechatNativePay(payload)

    // console.warn(res, 'resssss')
    if (res?.status >= 400) {
      return
    }
    setQrInfo({
      open: true, url: res?.data?.data?.code_url, img: '', openImg: false
    })
    startOrderInterval(res?.data?.data?.order_id, () => {
      setQrInfo(f => ({...f, open: false}))
    })
    setAlipayQrInfo(f => ({...f, open: false}))
    setTimeout(() => {
      const canvas = document.getElementById('react-qrcode-logo') as any

      console.warn(canvas?.toDataURL('image/png'), '111', canvas)
      setQrInfo(f => ({...f, img: canvas?.toDataURL('image/png'), openImg: true}))
    }, 1000)
  }

  const getAlipayPayment = async () => {
    const payload = {product_slug: productInfo?.slug, trade_type: 'web'}

    const res = await requestAlipayNativePay(payload)

    if (res?.status >= 400) {
      return
    }
    setAlipayQrInfo({
      open: true, url: res?.data?.data?.web_pay_str, img: '', openImg: false
    })
    setQrInfo(f => ({...f, open: false}))
    startOrderInterval(res?.data?.data?.order_id, () => {
      setAlipayQrInfo(f => ({...f, open: false}))
    })
  }

  // 打开支付按钮
  const onPayFn = debounce(async () => {
    await payZfbMobile()
    if (!BrowserDetector?.()?.isMobile()) {
      openPcModal()

      return
    }
    await payWxMobile()
  }, 500)

  const payZfbMobile = async () => {
    if (BrowserDetector?.()?.isMobile() && !BrowserDetector?.()?.isWechat()) {
      const payload = {product_slug: productInfo?.slug}

      const res = await requestAlipayNativePay(payload)

      console.warn(res, 'res')
      if (res?.status <= 400) {
        linkHref(res?.data?.data?.wap_pay_str)
      }

      return
    }
  }

  const payWxMobile = async () => {
    requestJsApiConfig().then(r => {
      const appId = r?.data?.data?.wx_app_id

      const redirectUri = encodeURIComponent(window.location.origin + window.location.pathname)

      console.log(appId)
      window.location.href = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' + appId + '&redirect_uri=' + redirectUri + '&response_type=code&scope=snsapi_userinfo#wechat_redirect'
    })
  }

  const openPcModal = () => {
    if (!BrowserDetector?.()?.isMobile()) {
      getWechatPayment()

      return
    }
  }

  const renderPayButton = () => {
    return <div className={styles.payInfo} onClick={handlePay}>
      <div className={styles.payInfoLeft}>
        <div className={styles.payInfoLeftTitle}>
          ¥700
        </div>
        <div className={styles.payInfoLeftPrice}>
          其中包含 200 元注册费+500 元学费
        </div>
      </div>

      <div className={styles.payInfoRight} onClick={onPayFn}>
        支付
      </div>
    </div>
  }

  if (isLogin) {
    return <div className={styles.pay + ' ' + styles.pay2} onScroll={handleScroll}>
      <Agreement />
      <div className={styles.payButtonContainer}>
        {!isAgreementEnd ? <div className={styles.payButton + ' ' + styles.payButton2} style={{opacity: isAgreementEnd ? 1 : 0.5}}>
          继续滑动查看完整用户协议
        </div> : renderPayButton()}

      </div>

      {qrInfo.open ? <div className={styles.modal} onClick={(e) => {
        e.stopPropagation()
        setQrInfo({
          open: false, url: '', img: '', openImg: false
        })
        clearInterval(timerRef.current)
      }}>
        <div className={styles.box} onClick={e => e.stopPropagation()}>
          <QRCode value={qrInfo.url} style={{width: 190, height: 190, padding: 0}} logoImage="/icon.png" />
          <img src="/pay/zf.png" style={{height: 23, marginBottom: 10}} />

          <div style={{display: 'flex', gap: 10}}>
            <div className={styles.textActive}>
              微信
            </div>
            <div className={styles.text} onClick={(f) => {
              f.stopPropagation()
              getAlipayPayment()
            }}>
              支付宝
            </div>
          </div>
        </div>
      </div> : null}

      {alipayQrInfo.open ? <div className={styles.modal} onClick={(e) => {
        // if (!productInfo.was_purchased_or_subscribed) return
        e.stopPropagation()
        setAlipayQrInfo({
          open: false, url: '', img: '', openImg: false
        })
        clearInterval(timerRef.current)
      }}>
        <div className={styles.box} onClick={e => e.stopPropagation()}>
          <div style={{
            width: '190px', height: '190px', display: 'flex', alignItems: 'center', justifyContent: 'center'
          }}>
            <iframe src={alipayQrInfo.url} style={{
              width: 170, height: 175, padding: 0, margin: '0 auto', display: 'flex', justifyContent: 'center', alignItems: 'center'
            }} />
          </div>
          <img src="/pay/zfb.png" style={{height: 23, marginBottom: 10}} />

          <div style={{display: 'flex', gap: 10}}>
            <div className={styles.text} onClick={(f) => {
              f.stopPropagation()
              getWechatPayment()
            }}>
              微信
            </div>
            <div className={styles.textActive}>
              支付宝
            </div>
          </div>
        </div>
      </div> : null}
    </div>
  }

  return (
    <div className={styles.pay}>
      <div className={styles.payButton3} onClick={handlePay}>
        现在报名
      </div>
    </div>
  )
}

export default PayView
