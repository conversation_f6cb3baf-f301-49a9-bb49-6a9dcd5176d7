.pay {
    background: linear-gradient(180deg, #FF0000 0%, #FFF492 100%);
    width: 100vw;
    height: 100vh;

    display: flex;
    justify-content: center;
    align-items: center;
}

.pay2 {
    background: #fff;
    max-width: 700px;
    margin: 0 auto;
    display: flex;
    align-items: flex-start !important;
    overflow-y: auto;
    /* flex-direction: column; */
    /* justify-content: flex-start; */
}
.pay2 {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
}

.pay2::-webkit-scrollbar { 
    display: none;  /* Safari and Chrome */
}

.payButton {
    height: 50px;
    padding: 0px 47px;
    background: #FF3333;
    font-size: 17px;
    font-weight: 500;
    line-height: 23.8px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50px;
    color: #fff;
    width: auto;
    cursor: pointer;
}

.payButton2 {
    opacity: 0.5;
}

.payButton3 {
    height: 50px;
    padding: 0px 47px;
    background: #FF3333;
    font-size: 17px;
    font-weight: 500;
    line-height: 23.8px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50px;
    color: #fff;
    width: auto;
    cursor: pointer;
    position: absolute;
    bottom: 32px;
}

.payButtonContainer {
    background: #fff;
    width: 100vw;
    height: 87px;
    position: absolute;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 700px;
    padding: 0 20px;
}

.payInfo {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.payInfoLeftTitle {
font-size: 18px;
font-weight: 900;
color: #FF3333;
}

.payInfoLeftPrice {
    font-size: 12px;
    font-weight: 400;
    color: #7A7A7A;   
}

.payInfoRight {
    width: auto;
    height: 50px;
    background: #FF3333;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50px;
    color: #fff;
    font-size: 17px;
    font-weight: 500;
    padding: 0 33px;
    cursor: pointer;
}


.modal {
    width: 100vw;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1900;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
}

.box {
    padding: 10px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 12px;
}
.text {
    font-size: 15px;
    font-weight: 600;
    line-height: 20px;
    text-align: center;
    cursor: pointer;
}

.textActive {
    color: #FF3333;
    font-size: 15px;
    font-weight: 600;
    line-height: 20px;
    text-align: center;
    cursor: pointer;
}
