import {useEffect} from 'react'

const usePageShow = () => {
  useEffect(() => {
    const handlePageShow = (e: any) => {
      if (e.persisted || (window.performance && window.performance.navigation.type === 2)) {
        window.location.reload()
      }
    }

    window.addEventListener('pageshow', handlePageShow)

    // Cleanup event listener on unmount
    return () => {
      window.removeEventListener('pageshow', handlePageShow)
    }
  }, [])
}

export default usePageShow