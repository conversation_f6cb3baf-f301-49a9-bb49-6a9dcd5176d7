export const scoreMap = {
  0: '待测试',
  A1: '初学者',
  A2: '初级',
  B1: '中级',
  B2: '中高级',
}

export const scoreDesc = {
  A1: [
    '能理解和使用非常简单的日常用语和基本句子，满足具体需求（例如介绍自己、询问基础信息）。',
    '能与对方缓慢、清晰地交谈，但需要对方重复或用更简单的方式表达。',
  ],
  A2: [
    '能理解涉及直接个人相关领域的句子和常用表达（如购物、家庭、当地地理环境等）。',
    '能在简单和例行的任务中进行交流，内容需直接相关且常见。',
    '能用简短的词语描述背景、家庭情况或某些需求。',
  ],
  B1: [
    '能理解清晰表达的内容，尤其是与工作、学校或日常生活相关的内容。',
    '能应对旅行中可能遇到的大多数情况。',
    '能以简单的方式讨论兴趣爱好、事件、愿望或解释观点。',
  ],
  B2: [
    '能理解较复杂文章的主旨，特别是关于具体或抽象主题的文章，包括技术讨论。',
    '能与母语者自然互动，无需太大压力。',
    '能清晰、详细地表达观点，解释优缺点，或参与深入讨论。',
  ],
}

export const deltaExamLink = 'https://gusto-english-exam.wemore.com/quiz/gamma_completion'

export const linkHref = (hre: string) => {
  location.href = hre
}

export enum ProductPayInfo {
  beta_user = 'english-learning-group-for-old-user',
  new_user = 'english-learning-group-for-new-user',
  enroll_user = 'gusto-enroll-gamma-awesome',
  register_user = 'gusto-post-gamma-registration',
  delta_sign_up_product = 'gusto-registration',
  // 500 的产品
  old_monthly = 'gusto-delta-monthly',
  // 700 的产品
  new_monthly = 'gusto-delta-monthly-and-registration'
}

export const HasBuyProductIds = [
  'english-learning-group-for-old-user', 
  'english-learning-group-for-new-user',
  'gusto-enroll-gamma-awesome',
  'gusto-post-gamma-registration',
  'gusto-registration'
]

export const checkHasBuyProduct = (productInfos: {id: string, was_purchased_or_subscribed: boolean}[]) => {
  if (!productInfos?.length) return false
  const hasBuyProductIds = productInfos?.filter(item => item.was_purchased_or_subscribed)?.map(item => item.id)

  const isHasBuyProductIds = HasBuyProductIds?.some(item => hasBuyProductIds?.includes(item))

  if (isHasBuyProductIds) {
    return true
  }

  return false
}

export const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp)

  const year = date.getFullYear()

  const month = String(date.getMonth() + 1).padStart(2, '0')

  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

interface Item {
  id: string;
  [key: string]: any;
}

export const  filterRepeatId = (arr: Item[]): Item[] => {
  const seenIds = new Set<string>()

  return arr.filter(item => {
    if (seenIds.has(item.id)) {
      return false
    }
    seenIds.add(item.id)

    return true
  })
}