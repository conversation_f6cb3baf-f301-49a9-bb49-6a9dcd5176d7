'use client'

import React, {useState} from 'react'
import styles from './index.module.css'
import {
  loginApi, loginWithEmail, requestEmailCode, sendCode<PERSON>pi
} from '@/http/http'
import {setStorage} from '@/utils/storage'
import {useRouter} from 'next/navigation'
import {allCountry} from '../countries'
import {Toaster} from '@/components/ui/toaster'
import {useToast} from '@/hooks/use-toast'
import {
  AlertDialog, AlertDialogAction, AlertDialogContent, AlertDialogFooter, AlertDialogTitle
} from '@/components/ui/alert-dialog'
import {ErrorBoundary} from '@/components/business/ErrBoundary'

let isClick = false

const ExLogin = () => {
  const {toast} = useToast()

  const [phone, setPhone] = useState('')

  const [msgCode, setCode] = useState('')

  const [loginType, setLoginType] = useState('phone')

  const [email, setEmail] = useState('')

  const [count, setCount] = useState(60)

  const [errCodeText, setErrCodeText] = useState('')

  const [countryInfo, setCountryInfo] = useState<any>({
    'name': 'China',
    'nationality': 'Chinese',
    'alpha_2_code': 'CN',
    'alpha_3_code': 'CHN',
    'phone_code': '+86'
  })

  const [isModalOpen, setIsModalOpen] = useState(false)

  const router = useRouter()

  const changePhone = (e: { target: { value: string } }) => {
    const inputValue = e.target.value

    const numericValue = inputValue.replace(/[^0-9]/g, '')

    if (numericValue?.length <= 11) {
      setPhone(numericValue)
    }
  }

  const changeCode = (e: { target: { value: React.SetStateAction<string>; }; }) => {
    setErrCodeText('')
    setCode(e.target.value)
  }

  const changeEmail = (e: { target: { value: string } }) => {
    const inputValue = e.target.value

    setEmail(inputValue)
  }

  const sendMsg = async () => {
    if (isClick) return
    if (!phone && loginType === 'phone') {
      toast({variant: 'destructive',title: '请输入手机号',})

      return
    }
    if (!email && loginType === 'email') {
      toast({variant: 'destructive',title: '请输入邮箱',})

      return
    }
    isClick = true
    const isEmail = loginType === 'email'

    const res = isEmail ? await requestEmailCode({email}) :  await sendCodeApi({phone: countryInfo.phone_code + phone})

    let k = 1

    if (res?.data.code === 'Success') {
      toast({title: '验证码发送成功'})
      isClick = false
      setCount(59)
      const timer = setInterval(() => {
        if (k >= 60) {
          clearInterval(timer)
          k = 1
          setCount(60)

          return
        }
        setCount(count - k)
        k++
      }, 1000)
    } else {
      toast({variant: 'destructive',title: '验证码发送失败，请重试!',})
      isClick = false
    }
  }

  const login = async () => {
    if (typeof window !== undefined) {
      window.scrollTo(0, 0)
    }
    if (!phone && loginType === 'phone') {
      toast({variant: 'destructive',title: '请输入手机号',})

      return
    }
    if (!email && loginType === 'email') {
      toast({variant: 'destructive',title: '请输入邮箱',})

      return
    }
    if (!msgCode) {
      toast({variant: 'destructive',title: '请输入验证码',})

      return
    }

    const isEmail = loginType === 'email'

    const res = isEmail ? await loginWithEmail({email, code: msgCode}) :  await loginApi({phone, code: msgCode})

    // console.info(res, 'res')
    if (res.data.code === 'Success') {
      setStorage('x-5e-token', res.data.data.token)
      router.push('/website/mine')
      // if (getStorage('isPay')) {
      //   router.push('/website/pay')
      //   setStorage('isPay', false)
      // } else {
      //   router.push('/website/mine')
      // }
      setStorage('useInfo', res.data.data)
      setStorage('userInfo', res.data.data)
    } else {
      setErrCodeText('error')
    }
  }

  const renderBtn = () => {
    return count === 60 ? (
      <div className={styles.form_item_btn} onClick={sendMsg}>
            发送验证码
      </div>
    ) : (
      <div className={`${styles.form_item_btn} ${styles.formItemSendBtn}`}>{count}s</div>
    )
  }
    
  const openModal = () => {
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
  }

  const clickCountry = (m: any) => () => {
    setCountryInfo(m)
    setIsModalOpen(false)
  }

  const renderCountry = () => {
    return allCountry.countries.map((item, index) => {
      const isActive = countryInfo.alpha_2_code === item.alpha_2_code

      return <div className={`${isActive ? styles.active : ''} ${styles.phone_item}`} onClick={clickCountry(item)} key={index}><span>{item.name}</span> <span>{item.phone_code}</span></div>
    })
  }

  const onBlur = () => {
    window.scrollTo(0, 0)
  }

  return (
    <div className={styles.login} onClick={onBlur}>
      <div className={styles.position_right}>
        {/* <ChangeLanguage /> */}
      </div>
      <div className={styles.login_container}>
        <div className={styles.login_type}>
          <span className={loginType === 'phone' ? styles.active2 : ''} onClick={() => setLoginType('phone')}>手机</span>
          <span  className={loginType === 'email' ? styles.active2 : ''} onClick={() => setLoginType('email')}>邮箱</span>
        </div>
        <img className={styles.login_icon} src={'/images/icon.png'} alt={'app'} />
        <div className={styles.login_title}>GustoEnglish高拓英语</div>
        <div className={styles.login_des}>成年人的线上英语学习社群</div>

        {loginType === 'phone' && <div className={styles.ipt_item} onClick={e => e.stopPropagation()}>
          <div className={styles.ipt_before}>手机号</div>
          <div className={styles.ipt_form}>
            <input
              autoFocus
              className={styles.form_item}
              onChange={changePhone}
              type="text"
              style={{paddingLeft: 80}}
              value={phone}
            />

            <div className={styles.area_code} onClick={openModal}>
              {countryInfo.alpha_2_code || 'CN'}  <img src='/images/down.png' className={styles.down} />
            </div>
          </div>
        </div>}

        {loginType === 'email' && <div className={styles.ipt_item} onClick={e => e.stopPropagation()}>
          <div className={styles.ipt_before}>邮箱</div>
          <div className={styles.ipt_form}>
            <input
              autoFocus
              className={styles.form_item}
              onChange={changeEmail}
              type="email"
              style={{paddingLeft: 16}}
              value={email}
            />
          </div>
        </div>}

        <div className={styles.ipt_item} onClick={e => e.stopPropagation()}>
          <div className={styles.ipt_before}>验证码</div>
          <div className={styles.ipt_form}>
            <input
              className={styles.form_item}
              maxLength={4}
              onChange={changeCode}
              type="text"
              style={{paddingLeft: 30}}
            />
            {renderBtn()}
          </div>
        </div>

        <div className={styles.err_text}>
          {errCodeText ? '验证码错误' : ''}   
        </div>

        <div className={styles.form_btn} onClick={login}>登录</div>
        {/* <p className={styles.ipt_text}>使用账号登录</p> */}
      </div>
      <AlertDialog open={isModalOpen}>
        <AlertDialogContent>
          <AlertDialogTitle>选择区号</AlertDialogTitle>
          <div className={styles.modal_container}>
            {renderCountry()}
          </div>
          <AlertDialogFooter>
            <AlertDialogAction onClick={closeModal} className='bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mt-[14px]'>Next</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <Toaster />
    </div>
  )
}

const LoginWebsiteView = () => <ErrorBoundary><ExLogin /></ErrorBoundary>

export default LoginWebsiteView
