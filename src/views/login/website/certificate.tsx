'use client'
import {useEffect, useState} from 'react'
import styles from './index.module.css'
import {getStorageJson} from '@/utils/storage'
import {useToast} from '@/hooks/use-toast'
import {Toaster} from '@/components/ui/toaster'
import {getUserMineInfoApi} from '@/http/http'

const WebsiteCertificateView = () => {
  const {toast} = useToast()

  const [certificate, setCertificate] = useState<any>({})

  const [user, setUser] = useState<any>({})

  useEffect(() => {
    const userInfo = getStorageJson('useInfo')

    getUserMineInfo()
    setUser(userInfo)
  }, [])

  const getUserMineInfo = async () => {
    const res = await getUserMineInfoApi()

    console.log(res)
    setCertificate(res.data?.data)
    // setCertificate({certificates: [{full_image: 'https://gusto-english-exam.wemore.com/images/gamma.png'},{full_image: 'https://gusto-english-exam.wemore.com/images/gamma.png'}, {full_image: 'https://gusto-english-exam.wemore.com/images/gamma.png'}]})
  }

  const fileUrlHandled = ({url, filename, target}: { url: string, filename: string, target: boolean }) => {
    const downloadElement = document.createElement('a')

    downloadElement.style.display = 'none'
    downloadElement.href = url
    if (target) {
      downloadElement.target = '_blank'
    }
    downloadElement.rel = 'noopener noreferrer'
    if (filename) {
      downloadElement.download = filename
    }
    document.body.appendChild(downloadElement)
    downloadElement.click()
    document.body.removeChild(downloadElement)
  }

  const renderLists = () => {
    return certificate?.certificates?.map((item: any, index: number) => {
      return (
        <div key={index} className={styles.c_item}>
          <img src={item.full_image} alt="" />
          <div className={styles.c_bottom}>
            <div className={styles.c_bottom_left}>
              <div className={styles.c_name}>{item?.season}</div>
              <div className={styles.c_tag}>
                {item.tag?.map?.((tagItem: any, index: number) => {
                  return (
                    <div key={index} className={styles.c_tag_item}>
                      {tagItem}
                    </div>
                  )
                })}
              </div>
            </div>
            <div className={styles.c_bottom_right}>
              <div
                className={styles.save}
                onClick={() => {
                  fileUrlHandled({url: item.full_image, filename: `GustoEnglish_Gamma_${user.name}.png`, target: false})
                  // require('downloadjs')(item.full, `GustoEnglish_Gamma_${user.name}.png`, "text/plain")
                }}
              >
                保存图片
              </div>
              <div
                className={styles.copy}
                onClick={() => {
                  navigator.clipboard.writeText(item.full_image)
                  toast({
                    title: '复制成功',
                    description: '链接已复制到剪贴板',
                    duration: 3000,
                  })
                }}
              >
                复制链接
              </div>
            </div>
          </div>
        </div>
      )
    })
  }

  return (
    <div className={styles.certificate_page}>
      <div className={styles.certificate_content}>{renderLists()}</div>
      <Toaster />
    </div>
  )
}

export default WebsiteCertificateView