'use client'

import React, {useEffect, useState} from 'react'
import styles from './index.module.css'
import {
  loginApi, loginWithEmail, requestEmailCode, sendCodeApi
} from '@/http/http'
import {getSessionStorage, setStorage} from '@/utils/storage'
import {useRouter} from 'next/navigation'
import {allCountry} from './countries'
import {Toaster} from '@/components/ui/toaster'
import {useToast} from '@/hooks/use-toast'
import {
  AlertDialog, AlertDialogAction, AlertDialogContent, AlertDialogFooter, AlertDialogTitle
} from '@/components/ui/alert-dialog'
import {ErrorBoundary} from '@/components/business/ErrBoundary'
import {isProd} from '@/global/consts'
import HeaderLogo from '@/components/business/HeaderLogo'

let isClick = false

const ExLogin = () => {
  const {toast} = useToast()
  
  const [phone, setPhone] = useState('')

  const [email, setEmail] = useState('')

  const [loginType2, setLoginType2] = useState('phone')

  const [msgCode, setCode] = useState('')

  const [count, setCount] = useState(60)

  const [errCodeText, setErrCodeText] = useState('')

  const [loginType, setLoginType] = useState('')

  const [loginInfo, setLoginInfo] = useState<{query_params?:{name: string; value: string; key?: string}[], link_url: string}>({link_url: ''})

  const [countryInfo, setCountryInfo] = useState<any>({
    'name': 'China',
    'nationality': 'Chinese',
    'alpha_2_code': 'CN',
    'alpha_3_code': 'CHN',
    'phone_code': '+86'
  })

  const [isModalOpen, setIsModalOpen] = useState(false)

  const changeEmail = (e: { target: { value: string } }) => {
    const inputValue = e.target.value

    setEmail(inputValue)
  }

  useEffect(() => {
    const currentUrl = window.location.href

    const url = new URL(currentUrl)

    const alternativePayForPath = url.pathname.split('/').pop()// This will also get the last segment of the URL

    if (alternativePayForPath === 'login') return
    const pathSegments = alternativePayForPath?.replace('login-', '')

    if (pathSegments!.length > 1) {
      setLoginType(pathSegments!)
    }
  }, [])

  useEffect(() => {
    if (loginType) {
      const fetchConfigData = async () => {
        try {
          const response = await fetch('https://gusto-english-oss.wemore.com/fe/config.json')

          const data = (await response.json()) as any[]

          console.log('Config Data:', data)
          const temp = data.find(item => item.key === loginType)

          setLoginInfo(temp)
        } catch (error) {
          console.error('Error fetching config data:', error)
        }
      }

      fetchConfigData()
    }
  }, [loginType])

  const router = useRouter()

  const changePhone = (e: { target: { value: string } }) => {
    const inputValue = e.target.value

    const numericValue = inputValue.replace(/[^0-9]/g, '')

    if (numericValue?.length <= 11) {
      setPhone(numericValue)
    }
  }

  const changeCode = (e: { target: { value: React.SetStateAction<string>; }; }) => {
    setErrCodeText('')
    setCode(e.target.value)
  }

  const sendMsg = async () => {
    if (isClick) return
    if (!phone && loginType2 === 'phone') {
      toast({variant: 'destructive',title: '请输入手机号',})

      return
    }
    if (!email && loginType2 === 'email') {
      toast({variant: 'destructive',title: '请输入邮箱',})

      return
    }
    const isEmail = loginType2 === 'email'

    isClick = true
    const res = isEmail ? await requestEmailCode({email}) :  await sendCodeApi({phone: countryInfo.phone_code + phone})

    let k = 1

    if (res?.data.code === 'Success') {
      toast({title: '验证码发送成功'})
      isClick = false
      setCount(59)
      const timer = setInterval(() => {
        if (k >= 60) {
          clearInterval(timer)
          k = 1
          setCount(60)

          return
        }
        setCount(count - k)
        k++
      }, 1000)
    } else {
      toast({variant: 'destructive',title: '验证码发送失败，请重试!',})
      isClick = false
    }
  }

  const login = async () => {
    if (!phone && loginType2 === 'phone') {
      toast({variant: 'destructive',title: '请输入手机号',})

      return
    }

    if (!email && loginType2 === 'email') {
      toast({variant: 'destructive',title: '请输入邮箱',})

      return
    }

    if (!msgCode) {
      toast({variant: 'destructive',title: '请输入验证码',})

      return
    }
    const isEmail = loginType2 === 'email'
  
    const res = isEmail ? await loginWithEmail({email, code: msgCode}) :  await loginApi({phone: countryInfo.phone_code + phone, code: msgCode})

    const queryString = window.location.search

    const urlParams = new URLSearchParams(queryString)

    // console.info(res, 'res')
    if (res.data.code === 'Success') {
      // 只触发一次
      if (getSessionStorage('answerUrl')) {
        // router.push(getSessionStorage('answerUrl') || '/')
        setStorage('userInfo', res.data.data)
        setStorage('x-5e-token', res.data.data.token)
        location.href = getSessionStorage('answerUrl') || '/'

        return
      }
      if (loginType !== '') {
        try {
          let temp = ''

          loginInfo.query_params?.forEach((t, idx) => {
            temp += `${t.name || t.key}=${res?.data?.data?.[t.value]}` + (idx < loginInfo?.query_params!.length - 1 ? '&' : '')
          })
          location.href =  !isProd ? window.location.origin + '/quiz' + '?' + temp : loginInfo.link_url + '?' + temp + '&redirectGustoUrl=' + urlParams.get('redirectGustoUrl')
        } catch (error) {
          toast({
            variant: 'destructive',
            title: '登录过程中发生错误，请稍后重试!' + error,
          })
        }

        return
      }
      setStorage('management-token', res.data.data.token)
      router.push('/login/management')
      setStorage('useInfo', res.data.data)
    } else {
      setErrCodeText('error')
    }
  }

  const renderBtn = () => {
    return count === 60 ? (
      <div className={styles.form_item_btn} onClick={sendMsg}>
            获取验证码
      </div>
    ) : (
      <div className={`${styles.form_item_btn} ${styles.formItemSendBtn}`}>{count}s</div>
    )
  }
    
  const openModal = () => {
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
  }

  const clickCountry = (m: any) => () => {
    setCountryInfo(m)
    setIsModalOpen(false)
  }

  const renderCountry = () => {
    return allCountry.countries.map((item, index) => {
      const isActive = countryInfo.alpha_2_code === item.alpha_2_code

      return <div className={`${isActive ? styles.active : ''} ${styles.phone_item}`} onClick={clickCountry(item)} key={index}><span>{item.name}</span> <span>{item.phone_code}</span></div>
    })
  }

  return (
    <div className={styles.login}>
      <HeaderLogo />
      <div className={styles.position_right}>
        {/* <ChangeLanguage /> */}
      </div>
      <div className={styles.login_container}>
        <div className={styles.login_type} style={{
          fontFamily: 'GTVCS-Black',
          fontWeight: 900,
          fontSize: '19.31px',
          lineHeight: '100%',
          letterSpacing: '0px',
          textAlign: 'center',
          color: '#fff'
        }}>
          <span className={loginType2 === 'phone' ? styles.active2 : ''} onClick={() => setLoginType2('phone')}>手机号登录</span>
          <span  className={loginType2 === 'email' ? styles.active2 : ''} onClick={() => setLoginType2('email')}>邮箱登录</span>
        </div>

        {loginType2 === 'phone' && <div className={styles.ipt_item}>
          <div className={styles.ipt_form}>
            <input
              autoFocus
              className={styles.form_item}
              onChange={changePhone}
              type="text"
              style={{paddingLeft: 60}}
              value={phone}
              placeholder='手机号'
            />

            <div className={styles.area_code} onClick={openModal}>
              {countryInfo.alpha_2_code || 'CN'}  <img src='images/down.png' className={styles.down} />
            </div>
          </div>
        </div>}

        {loginType2 === 'email' && <div className={styles.ipt_item} onClick={e => e.stopPropagation()}>
          <div className={styles.ipt_before}>邮箱</div>
          <div className={styles.ipt_form}>
            <input
              autoFocus
              className={styles.form_item}
              onChange={changeEmail}
              type="email"
              style={{paddingLeft: 16}}
              value={email}
            />
          </div>
        </div>}

        <div className={styles.ipt_item}>
          <div className={styles.ipt_form}>
            <input
              className={styles.form_item}
              maxLength={4}
              onChange={changeCode}
              type="text"
              placeholder='验证码'
            />
            {renderBtn()}
          </div>
        </div>

        <div className={styles.err_text}>
          {errCodeText ? '验证码错误' : ''}   
        </div>

        <div className={styles.form_btn} onClick={login}>登录</div>
        {/* <p className={styles.ipt_text}>使用账号登录</p> */}
      </div>
      <AlertDialog open={isModalOpen}>
        <AlertDialogContent>
          <AlertDialogTitle>选择区号</AlertDialogTitle>
          <div className={styles.modal_container}>
            {renderCountry()}
          </div>
          <AlertDialogFooter>
            <AlertDialogAction onClick={closeModal} className='bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mt-[14px]'>Next</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <Toaster />
    </div>
  )
}

const ExLoginWrapper = () => <ErrorBoundary><ExLogin /></ErrorBoundary>

export default ExLoginWrapper
