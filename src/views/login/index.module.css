.login {
    display: flex;
    justify-content: center;
    overflow: hidden;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(180deg, #080808 18.04%, #EC2726 100%);

}

.login_container {
    width: 350px;
    /* background-color: #fff; */
    display: flex;
    flex-direction: column;
    justify-content: center;
    /* padding-top: 200px; */
    padding: 32px 32px;
    box-sizing: content-box;
    /* margin-top: 200px; */
    border-radius: 16px;
}

@media (max-width: 500px) {
    .login_container {
        margin-top: 120px;
    }
}

.login_icon {
    display: block;
    margin: 0 auto !important;
    width: 48px;
    height: 48px;
}
.login_title {
    
    font-size: 30px;
    font-weight: 600;
    line-height: 38px;
    text-align: center;
    margin-top: 24px;
    width: 100%;
    text-align: center;
    color: #101828;
    margin-bottom: 32px;
}
.ipt_item {
    margin-bottom: 20px;
}

.ipt_before {
    
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    text-align: left;
    color: #344054;
    margin-bottom: 6px;
}
.ipt_text {
    margin-top: 10px;
    
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
    color: #475467;
}

.form_item {
    width: 100%;
    height: 62px;
    border-radius: 8px;
    /* background: #FFFFFF; */
    border: 0;
    outline: none;
    padding: 0 12px;
    box-sizing: border-box;

    background: transparent;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    text-align: left;
    color: #fff;

    border: 1.03px solid #FFFFFF;
    font-family: GTVCS-Black;
}

.form_item::placeholder {
    font-family: GTVCS-Black;
    font-weight: 900;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0px;
    opacity: 0.5;
    color: #fff;
}


.form_item_btn {
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    width: 205px;
    margin-left: 20px;

    display: flex;
    align-items: center;
    justify-content: center;
    /* border: 1px solid #D6BBFB; */
    border-radius: 8px;
    padding: 0 8px;
    background-color: rgba(255,255,255, 0.2);
}

.form_item_btn::placeholder {
    font-family: GTVCS-Black;
    font-weight: 900;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0px;
    opacity: 0.5;
    color: #fff;
}

.ipt_form {
    width: 100%;
    display: flex;
    flex-direction: row;
    position: relative;
}

.form_btn {
    margin-top: 4px;
    width: 100%;
    height: 72px;
    border-radius: 8px;
    box-shadow: 0px 2.07px 42.74px 0px #FFFF5FCC;
    background: #FFC640;
    box-shadow: 0px 0px 19.4px 0px #FFFFFFE5 inset;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 18px;
    color: #000;

    font-family: 'GTVCS-Black';
    font-weight: 900;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0px;
    text-align: center;
    color: #000;
    margin-top: 100px;
}

.area_code {
    width: 57px;
    position: absolute;
    height: 100%;
    left: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    font-family: GTVCS-Black;
    font-weight: 900;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0px;
    color: #fff;
}

.down {
    width: 15px;
    height: 20px;
    position: absolute;
    right: 0px;
}

.modal_container {
    max-height: 630px;
    overflow-y: auto;
}

.phone_item {
    width: 432px;
    height: 52px;
    border: 1px solid #EAECF0;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-radius: 8px;
    justify-content: space-between;
    cursor: pointer;
}

.active {
    background: #7F56D9;
    color: #fff;
}

.position_right {
    position: absolute;
    right: 16px;
    top: 20px;
}

.err_text {
    width: 100%;
    height: 20px;
    line-height: 20px;

    color: #EA3434;
    font-size: 14px;
    margin-top: -10px;
    margin-bottom: 10px;
}

.login_type {
    display: flex;
    align-items: center;
    /* justify-content: center; */
    margin-bottom: 24px;
    gap: 24px;
}

.login_type span {
    font-weight: 590;
    font-size: 20px;
    /* color: #B2A9A1; */
    cursor: pointer;
    opacity: 0.5;
}

.login_type .active2 {
    /* color: #33312E; */
    opacity: 1;
}

.zs_text img {
    width: 17px;
    height: 17px;
}

.login_des {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    color: #9B9B9B;
    margin-bottom: 32px; 
}