import React, {useState, useRef, useEffect} from 'react'
import {Card} from '@/components/ui/card'
import {Button} from '@/components/ui/button'
import RenderScore from './render-score'

interface QuickQAProps {
  model: any
  questionNumber: number
}

const QuickQA = (props: QuickQAProps) => {
  const {model, questionNumber} = props

  const [mediaPlaying, setMediaPlaying] = useState<boolean>(false)

  const [userAudioPlaying, setUserAudioPlaying] = useState<boolean>(false)

  const [hoveredWord, setHoveredWord] = useState<number | null>(null)

  const mediaRef = useRef<HTMLAudioElement | HTMLVideoElement | null>(null)

  const userAudioRef = useRef<HTMLAudioElement | null>(null)

  const videoContainerRef = useRef<HTMLDivElement | null>(null)

  // Get media type and URL from the question
  const mediaItem = model.question?.find((item: any) => item.type === 'video' || item.type === 'voice')

  const mediaType = mediaItem?.type

  const mediaUrl = mediaItem?.value

  // Get standard answer and user answer
  const standardAnswer = model.ref_answer?.[0]?.value || ''

  const userAnswer = model.user_answer?.[0]?.value || ''

  // Format time in MM:SS format
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)

    const secs = Math.floor(seconds % 60)

    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // Clean up when component unmounts
  useEffect(() => {
    return () => {
      if (mediaRef.current) {
        mediaRef.current.pause()
      }
      if (userAudioRef.current) {
        userAudioRef.current.pause()
      }
    }
  }, [])

  const toggleMedia = () => {
    if (mediaUrl) {
      if (!mediaRef.current) {
        if (mediaType === 'voice') {
          mediaRef.current = new Audio(mediaUrl)
        } else if (mediaType === 'video' && videoContainerRef.current) {
          const video = document.createElement('video')

          video.src = mediaUrl
          video.controls = false
          video.style.width = '100%'
          video.style.borderRadius = '0.375rem'
          videoContainerRef.current.innerHTML = ''
          videoContainerRef.current.appendChild(video)
          mediaRef.current = video
        }
        
        // Reset playing state when media ends
        if (mediaRef.current) {
          mediaRef.current.onended = () => {
            setMediaPlaying(false)
          }
        }
      }
      
      if (mediaPlaying) {
        // If media is currently playing, pause it
        mediaRef.current?.pause()
        setMediaPlaying(false)
      } else {
        // If media is not playing, play it
        mediaRef.current?.play().catch(error => {
          console.error('Error playing media:', error)
          setMediaPlaying(false)
        })
        setMediaPlaying(true)
      }
    } else {
      console.warn('No media URL available')
      setMediaPlaying(false)
    }
  }

  const toggleUserAudio = () => {
    // Get user answer audio URL
    const audioUrl = model.user_answer?.find((item: any) => item.type === 'voice')?.value
    
    if (audioUrl) {
      if (!userAudioRef.current) {
        userAudioRef.current = new Audio(audioUrl)
        userAudioRef.current.onended = () => setUserAudioPlaying(false)
      }
      
      if (userAudioPlaying) {
        userAudioRef.current.pause()
        setUserAudioPlaying(false)
      } else {
        userAudioRef.current.play().catch((error: Error) => {
          console.error('Error playing audio:', error)
          setUserAudioPlaying(false)
        })
        setUserAudioPlaying(true)
      }
    }
  }

  const wordDetails = model?.rate_result?.words || []

  const hasAnswered = !!userAnswer

  // Helper function to get score color
  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600'
    if (score >= 6) return 'text-yellow-600'

    return 'text-red-600'
  }

  // Helper function to get error type text
  const getErrorTypeText = (errorType: string) => {
    switch (errorType) {
    case 'Mispronunciation':
      return '发音错误'
    case 'Omission':
      return '遗漏'
    case 'Insertion':
      return '插入'
    case 'Stress':
      return '重音错误'
    default:
      return '正确'
    }
  }

  return (
    <Card className="p-3 md:p-6 mb-4 md:mb-8 shadow-sm border-blue-100">
      <div className="flex justify-between items-start mb-3 md:mb-4">
        <div className="flex items-center">
          <div className="bg-green-500 text-white rounded-md px-2 md:px-3 py-1 text-sm font-medium">
            {String(questionNumber).padStart(2, '0')}
          </div>
          <span className="ml-2 text-gray-600">快速问答</span>
        </div>
        <div className="text-gray-600">
          得分: {model.score}
        </div>
      </div>
      <div className="mb-3 md:mb-6">
        <p className="text-lg font-medium mb-3 md:mb-4">
          请{mediaType === 'video' ? '观看视频' : '听录音'}并用英语回答问题:
        </p>
        <div className="mb-3 md:mb-6">
          <div className="flex items-center mb-3 md:mb-4">
            <Button 
              className="!rounded-button whitespace-nowrap flex items-center mr-3 bg-blue-600 hover:bg-blue-700 cursor-pointer"
              onClick={toggleMedia}
            >
              <i className={'fas mr-2'}>
                <img src="https://gusto-english-oss.wemore.com/exam/icon-listen-3%403x.png" alt="" width="12" height="12" />
              </i>
              {mediaPlaying ? '暂停' : '播放'}{mediaType === 'video' ? '视频' : '录音'}
            </Button>
            {mediaItem?.duration && (
              <div className="text-sm text-gray-500">
                {formatTime(mediaItem.duration)}
              </div>
            )}
          </div>
          
          {mediaType === 'video' && (
            <div 
              ref={videoContainerRef} 
              className="mb-3 md:mb-4 bg-gray-100 rounded-md border border-gray-200 aspect-video flex items-center justify-center"
            >
              <div className="text-gray-500">点击播放按钮查看视频</div>
            </div>
          )}
          
          {model.question?.find((item: any) => item.type === 'text')?.value && (
            <div className="p-3 md:p-4 bg-gray-50 rounded-md border border-gray-200">
              <p className="text-gray-800">
                {model.question.find((item: any) => item.type === 'text').value}
              </p>
            </div>
          )}
        </div>
        
        <div className="space-y-3 md:space-y-4">
          {standardAnswer && (
            <div>
              <h4 className="font-medium text-gray-700 mb-2">标准答案:</h4>
              <div className="p-3 bg-gray-50 rounded-md border border-gray-200">
                <p className="text-gray-800">
                  {standardAnswer}
                </p>
              </div>
            </div>
          )}
          
          <div>
            <h4 className="font-medium text-gray-700 mb-2">您的答案:</h4>
            <div className="p-3 bg-gray-50 rounded-md border border-gray-200">
              {userAnswer ? (
                <div className="flex items-center mb-2">
                  <Button 
                    className="!rounded-button whitespace-nowrap flex items-center mr-3 bg-blue-600 hover:bg-blue-700 cursor-pointer"
                    onClick={toggleUserAudio}
                  >
                    <i className={'fas mr-2'}>
                      <img src="https://gusto-english-oss.wemore.com/exam/icon-listen-3%403x.png" alt="" width="12" height="12" />
                    </i>
                    {userAudioPlaying ? '暂停播放' : '播放答案'}
                  </Button>
                </div>
              ) : (
                <p className="text-gray-800">未作答</p>
              )}
            </div>
          </div>
        </div>
      </div>
      
      {hasAnswered  && (
        <div className="bg-blue-50 p-3 md:p-4 rounded-md border border-blue-200">
          <h3 className="text-lg font-medium text-blue-800 mb-3 md:mb-4">
            评分结果
          </h3>
          <div>
            {/* 评分维度渲染 */}
            <RenderScore rateResult={model.rate_result} />
          </div>
          {wordDetails.length > 0 && (
            <>
              <h4 className="font-medium text-blue-800 mb-2 mt-3 md:mt-4">单词评估详情</h4>
              <div className="relative p-2 md:p-3 bg-white rounded-md border border-gray-200 leading-relaxed">
                {wordDetails?.map((detail: any, index: number) => {
                  const score = detail.accuracy_score || 0

                  const colorClass = getScoreColor(score)

                  const errorType = detail.error_type || 'None'
                      
                  return (
                    <span 
                      key={index} 
                      className={`${colorClass} font-medium text-base md:text-lg mr-2 cursor-pointer hover:underline relative inline-block`}
                      onMouseEnter={() => setHoveredWord(index)}
                      onMouseLeave={() => setHoveredWord(null)}
                    >
                      {detail.word}
                      {hoveredWord === index && (
                        <div className="absolute bottom-full left-0 mb-2 p-2 bg-gray-800 text-white text-xs rounded shadow-lg whitespace-nowrap z-10">
                          <div>错误类型: {getErrorTypeText(errorType)}</div>
                          <div>准确度: {score}</div>
                          {detail.phonemes && detail.phonemes.length > 0 && (
                            <div>
                              音素详情: {detail.phonemes.map((phoneme: any, pIndex: number) => (
                                <span key={pIndex} className="mr-1">
                                  {phoneme.phoneme}({phoneme.accuracy_score})
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </span>
                  )
                })}
              </div>
              <div className="flex flex-wrap gap-3 md:gap-4 mt-3 md:mt-4 text-xs md:text-sm">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-600 rounded-full mr-1"></div>
                  <span>发音正确</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-yellow-600 rounded-full mr-1"></div>
                  <span>轻微问题</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-600 rounded-full mr-1"></div>
                  <span>发音错误</span>
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </Card>
  )
}

export default QuickQA
