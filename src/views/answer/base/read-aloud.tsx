import React, {useState, useRef, useEffect} from 'react'
import {Card} from '@/components/ui/card'
import {Button} from '@/components/ui/button'
import RenderScore from './render-score'

interface ReadingAloudProps {
  model: any
  questionNumber: number
}

const ReadingAloud = (props: ReadingAloudProps) => {
  const {model, questionNumber} = props

  const [audioPlaying, setAudioPlaying] = useState<boolean>(false)

  const [hoveredWord, setHoveredWord] = useState<number | null>(null)

  const audioRef = useRef<HTMLAudioElement | null>(null)

  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // Clean up timer when component unmounts
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
      if (audioRef.current) {
        audioRef.current.pause()
      }
    }
  }, [])

  const toggleAudioPlaying = () => {
    // Get audio URL from the model
    const audioUrl = model.user_answer?.[0]?.value
    
    if (audioUrl) {
      if (!audioRef.current) {
        audioRef.current = new Audio(audioUrl)
        
        // Set up time update event
        audioRef.current.ontimeupdate = () => {
          if (audioRef.current) {
            // setCurrentTime(formatTime(audioRef.current.currentTime))
          }
        }
        
        // Reset playing state when audio ends
        audioRef.current.onended = () => {
          setAudioPlaying(false)
          //   setCurrentTime(formatTime(0))
          if (timerRef.current) {
            clearInterval(timerRef.current)
            timerRef.current = null
          }
        }
      }
      
      if (audioPlaying) {
        // If audio is currently playing, pause it
        audioRef.current.pause()
        if (timerRef.current) {
          clearInterval(timerRef.current)
          timerRef.current = null
        }
        setAudioPlaying(false)
      } else {
        // If audio is not playing, play it
        audioRef.current.play().catch(error => {
          console.error('Error playing audio:', error)
          setAudioPlaying(false)
        })
        setAudioPlaying(true)
      }
    } else {
      console.warn('No audio URL available')
      setAudioPlaying(false)
    }
  }

  // Find the text content from the question
  const textContent = model.question.find((item: any) => item.type === 'text')?.value || ''
  
  // Check if user has answered
  const hasAnswered = model.user_answer?.length > 0

  const wordDetails = model?.rate_result?.words || []

  // Function to get error type display text
  const getErrorTypeText = (errorType: string) => {
    const errorTypeMap: { [key: string]: string } = {
      'Omission': '遗漏',
      'None': '正确'
    }

    return errorTypeMap[errorType] || errorType
  }

  // Function to get score color based on accuracy score
  const getScoreColor = (score: number) => {
    if (score < 50) {
      return 'text-red-600'
    } else if (score >= 50 && score < 80) {
      return 'text-yellow-600'
    } else {
      return 'text-green-600'
    }
  }

  return (
    <Card className="p-3 md:p-6 mb-4 md:mb-8 shadow-sm border-blue-100">
      <div className="flex justify-between items-start mb-3 md:mb-4">
        <div className="flex items-center">
          <div className="bg-green-500 text-white rounded-md px-2 md:px-3 py-1 text-sm font-medium">
            {questionNumber}
          </div>
          <span className="ml-2 text-gray-600">朗读文本</span>
        </div>
        <div className="text-gray-600">得分: {model.score}</div>
      </div>
      <div className="mb-4 md:mb-6">
        <p className="text-lg font-medium mb-3 md:mb-4">请朗读以下文本:</p>
        <div className="p-3 md:p-4 bg-gray-50 rounded-md border border-gray-200 mb-3 md:mb-4">
          <p className="text-gray-800 leading-relaxed">{textContent}</p>
        </div>
        {hasAnswered ? <div className="flex items-center mb-4 md:mb-6">
          <Button 
            onClick={toggleAudioPlaying}
            className="!rounded-button whitespace-nowrap flex items-center mr-3 bg-blue-600 hover:bg-blue-700 cursor-pointer"
          >
            <i className={'fas mr-2'}>
              <img src="https://gusto-english-oss.wemore.com/exam/icon-listen-3%403x.png" alt="" width="12" height="12" />
            </i>            
            {audioPlaying ? '暂停录音' : '播放录音'}
          </Button>
          {/* <div className="text-sm text-gray-500">
            {audioPlaying ? currentTime : model.user_answer?.[0]?.duration || '00:00'}
          </div> */}
        </div> : <div className="text-gray-600">未回答</div>}
        {hasAnswered && (
          <div className="bg-blue-50 p-3 md:p-4 rounded-md border border-blue-200">
            <h3 className="text-lg font-medium text-blue-800 mb-3 md:mb-4">
              评分结果
            </h3>
            <div>
              <RenderScore rateResult={model.rate_result} />
            </div>
            <h4 className="font-medium text-blue-800 mb-2 mt-3 md:mt-4">单词评估详情</h4>
            <div className="relative p-2 md:p-3 bg-white rounded-md border border-gray-200 leading-relaxed">
              {wordDetails?.map((detail: any, index: number) => {
                const score = detail.accuracy_score || 0

                const colorClass = getScoreColor(score)

                const errorType = detail.error_type || 'None'
                    
                return (
                  <span 
                    key={index} 
                    className={`${colorClass} font-medium text-base md:text-lg mr-2 cursor-pointer hover:underline relative inline-block`}
                    onMouseEnter={() => setHoveredWord(index)}
                    onMouseLeave={() => setHoveredWord(null)}
                  >
                    {detail.word}
                    {hoveredWord === index && (
                      <div className="absolute bottom-full left-0 mb-2 p-2 bg-gray-800 text-white text-xs rounded shadow-lg whitespace-nowrap z-10">
                        <div>错误类型: {getErrorTypeText(errorType)}</div>
                        <div>准确度: {score}</div>
                        {detail.phonemes && detail.phonemes.length > 0 && (
                          <div>
                            音素详情: {detail.phonemes.map((phoneme: any, pIndex: number) => (
                              <span key={pIndex} className="mr-1">
                                {phoneme.phoneme}({phoneme.accuracy_score})
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    )}
                  </span>
                )
              })}
            </div>
            <div className="flex flex-wrap gap-3 md:gap-4 mt-3 md:mt-4 text-xs md:text-sm">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-600 rounded-full mr-1"></div>
                <span>发音正确</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-orange-600 rounded-full mr-1"></div>
                <span>遗漏</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </Card>
  )
}

export default ReadingAloud
