import React, {useState, useRef, useEffect} from 'react'
import {Card} from '@/components/ui/card'
import {Button} from '@/components/ui/button'
import RenderScore from './render-score'

interface CommonQuestionProps {
  model: any
  questionNumber: number
  children?: React.ReactNode
}

const CommonQuestion: React.FC<CommonQuestionProps> = ({model, questionNumber}) => {
  const {
    question_type, question, score, user_answer, ref_answer
  } = model

  const [audioPlaying, setAudioPlaying] = useState<boolean>(false)

  const [videoPlaying, setVideoPlaying] = useState<boolean>(false)

  const audioRef = useRef<HTMLAudioElement | null>(null)

  const videoRef = useRef<HTMLVideoElement | null>(null)

  const [hoveredWord, setHoveredWord] = useState<number | null>(null)

  // Clean up audio and video when component unmounts
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause()
      }
      if (videoRef.current) {
        videoRef.current.pause()
      }
    }
  }, [])

  const toggleAudioPlaying = (audioUrl: string) => {
    if (audioUrl) {
      if (!audioRef.current) {
        audioRef.current = new Audio(audioUrl)
        
        // Reset playing state when audio ends
        audioRef.current.onended = () => {
          setAudioPlaying(false)
        }
      }
      
      if (audioPlaying) {
        // If audio is currently playing, pause it
        audioRef.current.pause()
        setAudioPlaying(false)
      } else {
        // If audio is not playing, play it
        audioRef.current.play().catch(error => {
          console.error('Error playing audio:', error)
          setAudioPlaying(false)
        })
        setAudioPlaying(true)
      }
    }
  }

  const toggleVideoPlaying = (videoUrl: string) => {
    if (videoUrl) {
      if (!videoRef.current) {
        videoRef.current = document.createElement('video')
        videoRef.current.src = videoUrl
        
        // Reset playing state when video ends
        videoRef.current.onended = () => {
          setVideoPlaying(false)
        }
      }
      
      if (videoPlaying) {
        // If video is currently playing, pause it
        videoRef.current.pause()
        setVideoPlaying(false)
      } else {
        // If video is not playing, play it
        videoRef.current.play().catch(error => {
          console.error('Error playing video:', error)
          setVideoPlaying(false)
        })
        setVideoPlaying(true)
      }
    }
  }

  const renderQuestionContent = () => {
    return question?.map((item: any) => {
      if (item.type === 'text' && model.question_type === 'inline_input') {
        return <div key={item.type + item.value} className="mb-2.5" dangerouslySetInnerHTML={{__html: item.value.replace(/\{\{(key\d+)\}\}/g, '<span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded font-medium">$1</span>')}} />
      }
      if (item.type === 'text') {
        return <div key={item.type + item.value} className="mb-2.5 whitespace-pre-wrap">{item.value}</div>
      }
      if (item.type === 'image') {
        return <img key={item.type + item.value} src={item.value} alt="Question image" className="w-full max-h-[300px] h-auto object-contain object-center rounded-lg mb-2.5" />
      }
      if (item.type === 'voice') {
        return (
          <div key={item.type + item.value} className="flex items-center mb-2.5">
            <Button 
              onClick={() => toggleAudioPlaying(item.value)}
              className="!rounded-button whitespace-nowrap flex items-center mr-3 bg-blue-600 hover:bg-blue-700 cursor-pointer"
            >
              <i className="fas mr-2">
                <img src="https://gusto-english-oss.wemore.com/exam/icon-listen-3%403x.png" alt="listen icon" width="12" height="12" />
              </i>
              {audioPlaying ? '暂停录音' : '播放录音'}
            </Button>
          </div>
        )
      }
      if (item.type === 'video') {
        return (
          <div key={item.type + item.value} className="relative mb-2.5">
            <Button 
              onClick={() => toggleVideoPlaying(item.value)}
              className="!rounded-button whitespace-nowrap flex items-center mr-3 bg-blue-600 hover:bg-blue-700 cursor-pointer"
            >
              <i className="fas mr-2">
                <img src="https://gusto-english-oss.wemore.com/exam/icon-listen-3%403x.png" alt="play icon" width="12" height="12" />
              </i>
              {videoPlaying ? '暂停视频' : '播放视频'}
            </Button>
            <video 
              ref={videoRef}
              src={item.value} 
              className="w-full max-h-[300px] h-auto object-contain object-center rounded-lg"
            />
          </div>
        )
      }
      if (item.type === 'option') {
        return (
          <div key={item.type + item.value} className="grid grid-cols-2 gap-4 mt-4 px-2">
            {Object.entries(item.options).map(([key, value]: [string, any]) => {
              return (
                <div 
                  key={key}
                  className="flex flex-col items-center p-4 border-2 rounded-lg border-gray-300 bg-white hover:bg-gray-50 hover:border-blue-400 hover:shadow-md cursor-pointer transition-all duration-300"
                >
                  <div className="w-full mb-2 flex justify-center flex-1">
                    <div className="text-center p-2 min-h-[80px] flex items-center justify-center w-full">
                      <span className="text-sm font-medium text-gray-700">{value}</span>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )
      }

      return null
    })
  }

  const renderAnswer = () => {
    const hasAnswer = user_answer?.length > 0

    if (!hasAnswer) {
      return <div>未作答</div>
    }

    return user_answer?.map((item: any) => {
      if (item.type === 'text') {
        return <div key={item.type + item.value}>{item.value}</div>
      }
      if (item.type === 'image') {
        return <img key={item.type + item.value} src={item.value} alt="Question image" className="w-full max-h-[300px] h-auto object-contain object-center rounded-lg" />
      }
      if (item.type === 'voice') {
        return (
          <div key={item.type + item.value} className="flex items-center">
            <Button 
              onClick={() => toggleAudioPlaying(item.value)}
              className="!rounded-button whitespace-nowrap flex items-center mr-3 bg-blue-600 hover:bg-blue-700 cursor-pointer"
            >
              <i className={'fas mr-2'}>
                <img src="https://gusto-english-oss.wemore.com/exam/icon-listen-3%403x.png" alt="" width="12" height="12" />
              </i>
              {audioPlaying ? '暂停录音' : '播放录音'}
            </Button>
          </div>
        )
      }
      if (item.type === 'input') {
        return <div key={item.type + item.value}>
          {Object.entries(item.inputs).map(([key, value]: [string, any]) => (
            <div key={key} className="flex items-center">
              {key}:
              <div className="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded font-medium mx-1">
                {value}
              </div>
            </div>
          ))}
        </div>
      }
      if (item.type === 'option') {
        // return <div key={item.type + item.value}>
        //   {Object.entries(item.options).map(([key, value]: [string, any]) => {
        //     const options = question.find((x: { type: string }) => x.type === 'option')?.options

        //     return (
        //       <div key={key} className="inline-block bg-green-100 text-green-800 px-2 py-1 rounded font-medium mx-1">
        //         {options[value] || ''}
        //       </div>
        //     )
        //   })}
        // </div>
        return <div key={item.type + item.value}>
          {Object.entries(item.options).map(([key, value]: [string, any]) => (
            <div key={key} className="inline-block text-green-800 px-2 py-1 rounded font-medium mx-1">{value}</div>
          ))}
        </div>
      }

      return null
    })
  }

  const renderCurrentAnswer = () => {
    return ref_answer?.map((item: any) => {
      if (item.type === 'input') {
        return <div key={item.type + item.value}>
          {Object.entries(item.inputs).map(([key, value]: [string, any]) => (
            <div key={key} className="flex items-center">
              {key}:
              <div className="inline-block bg-green-100 text-green-800 px-2 py-1 rounded font-medium mx-1">
                {value}
              </div>
            </div>
          ))}
        </div>
      }
      if (item.type === 'option') {
        return <div key={item.type + item.value}>
          {Object.entries(item.options).map(([key, value]: [string, any]) => (
            <div key={key} className="inline-block bg-green-100 text-green-800 px-2 py-1 rounded font-medium mx-1">{value}</div>
          ))}
        </div>
      }

      return null
    })
  }

  const correctAnswer = ref_answer?.length > 0
  
  // Get user's recorded answer text if available
  const wordDetails = model?.rate_result?.words || []

  // Helper function to get error type text
  const getErrorTypeText = (errorType: string) => {
    switch (errorType) {
    case 'Mispronunciation':
      return '发音错误'
    case 'Omission':
      return '遗漏'
    case 'Insertion':
      return '插入'
    case 'Stress':
      return '重音错误'
    default:
      return '正确'
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600'
    if (score >= 6) return 'text-yellow-600'

    return 'text-red-600'
  }

  return (
    <Card className="p-2 md:p-6 mb-3 md:mb-8 shadow-sm border-blue-100">
      <div className="flex justify-between items-start mb-2 md:mb-4">
        <div className="flex items-center">
          <div className="bg-green-500 text-white rounded-md px-2 md:px-3 py-1 text-sm font-medium">
            {questionNumber}
          </div>
          <span className="ml-2 text-gray-600">{question_type?.replaceAll('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()) || '未知题型'}</span>
        </div>
        <div className="text-gray-600">得分: {score}</div>
      </div>

      <div className="mb-3 md:mb-6">
        <p className="text-lg font-medium mb-3 md:mb-4">题目详情:</p>
        <div className="mb-3 md:mb-4 rounded-lg overflow-hidden">
          {renderQuestionContent()}
        </div>
      </div>
      {correctAnswer && <div>
        <h4 className="font-medium text-gray-700 mb-2">正确答案:</h4>
        <div className="p-3 bg-gray-50 rounded-md border border-gray-200 mb-3 md:mb-6">
          {renderCurrentAnswer()}
        </div>
      </div>}
      <div>
        <h4 className="font-medium text-gray-700 mb-2">您的答案:</h4>
        <div className="p-3 bg-gray-50 rounded-md border border-gray-200">
          {renderAnswer()}
        </div>
      </div>

      {['describe_image_with_voice', 'summarize_audio', 'summarize_text_with_voice'].includes(model.question_type) && (
        <div className="bg-blue-50 p-3 md:p-4 rounded-md border border-blue-200 mt-4">
          <h3 className="text-lg font-medium text-blue-800 mb-3 md:mb-4">
            评分结果
          </h3>
          <div>
            <RenderScore rateResult={model.rate_result} />
          </div>
          {wordDetails.length > 0 && (
            <>
              <h4 className="font-medium text-blue-800 mb-2 mt-3 md:mt-4">单词评估详情</h4>
              <div className="relative p-2 md:p-3 bg-white rounded-md border border-gray-200 leading-relaxed">
                {wordDetails?.map((detail: any, index: number) => {
                  const score = detail.accuracy_score || 0

                  const colorClass = getScoreColor(score)

                  const errorType = detail.error_type || 'None'
                      
                  return (
                    <span 
                      key={index} 
                      className={`${colorClass} font-medium text-base md:text-lg mr-2 cursor-pointer hover:underline relative inline-block`}
                      onMouseEnter={() => setHoveredWord(index)}
                      onMouseLeave={() => setHoveredWord(null)}
                    >
                      {detail.word}
                      {hoveredWord === index && (
                        <div className="absolute bottom-full left-0 mb-2 p-2 bg-gray-800 text-white text-xs rounded shadow-lg whitespace-nowrap z-10">
                          <div>错误类型: {getErrorTypeText(errorType)}</div>
                          <div>准确度: {score}</div>
                          {detail.phonemes && detail.phonemes.length > 0 && (
                            <div>
                              音素详情: {detail.phonemes.map((phoneme: any, pIndex: number) => (
                                <span key={pIndex} className="mr-1">
                                  {phoneme.phoneme}({phoneme.accuracy_score})
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </span>
                  )
                })}
              </div>
              <div className="flex flex-wrap gap-3 md:gap-4 mt-3 md:mt-4 text-xs md:text-sm">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-600 rounded-full mr-1"></div>
                  <span>发音正确</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-yellow-600 rounded-full mr-1"></div>
                  <span>轻微问题</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-600 rounded-full mr-1"></div>
                  <span>发音错误</span>
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </Card>
  )
}

export default CommonQuestion
