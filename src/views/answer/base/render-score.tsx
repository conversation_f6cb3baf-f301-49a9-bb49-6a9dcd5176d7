import React from 'react'

interface ScoreDimension {
  key: string
  label: string
  value: any
  show: boolean
}

interface RenderScoreProps {
  rateResult: any
}

// 分数颜色区分
const getScoreColor = (score: number) => {
  if (score >= 10) return 'text-green-700'
  if (score >= 8) return 'text-green-600'
  if (score >= 6) return 'text-yellow-600'
  if (score >= 3) return 'text-orange-600'

  return 'text-red-600'
}

// 分数渐变区分 0-3, 3-6, 6-8, 8-9, 10
const getBgGradient = (score: number) => {
  if (score >= 10) {
    // 满分
    return 'bg-gradient-to-br from-green-200 via-green-100 to-green-300 border-green-400'
  } else if (score >= 8) {
    // 8-9
    return 'bg-gradient-to-br from-green-100 via-white to-green-200 border-green-200'
  } else if (score >= 6) {
    // 6-8
    return 'bg-gradient-to-br from-yellow-50 via-white to-yellow-200 border-yellow-200'
  } else if (score >= 3) {
    // 3-6
    return 'bg-gradient-to-br from-orange-100 via-white to-orange-200 border-orange-200'
  } else {
    // 0-3
    return 'bg-gradient-to-br from-red-100 via-white to-red-200 border-red-200'
  }
}

// 标题颜色区分
const getTitleClass = (score: number) => {
  if (score >= 10) {
    return 'text-green-800'
  } else if (score >= 8) {
    return 'text-green-700'
  } else if (score >= 6) {
    return 'text-yellow-700'
  } else if (score >= 3) {
    return 'text-orange-700'
  } else {
    return 'text-red-700'
  }
}

const RenderScore: React.FC<RenderScoreProps> = ({rateResult}) => {
  // 评分维度 - 语音
  const pronAccuracy = rateResult?.pron_accuracy

  const pronClarity = rateResult?.pron_clarity

  const pronFluency = rateResult?.pron_fluency

  // 评分维度 - 内容
  const vocabulary = rateResult?.vocabulary

  const grammar = rateResult?.grammar

  const organization = rateResult?.organization

  const topicRelevance = rateResult?.topic_relevance

  // 语音评分维度
  const speechScoreDimensions: ScoreDimension[] = [
    {
      key: 'pronClarity',
      label: '发音清晰度',
      value: pronClarity,
      show: !!pronClarity
    },
    {
      key: 'pronFluency',
      label: '发音流利度',
      value: pronFluency,
      show: !!pronFluency
    },
    {
      key: 'contentAccuracy',
      label: '内容准确度',
      value: pronAccuracy,
      show: !!pronAccuracy
    },
  ]
  
  // 内容评分维度
  const contentScoreDimensions: ScoreDimension[] = [
    {
      key: 'vocabulary',
      label: '词汇运用',
      value: vocabulary,
      show: !!vocabulary
    },
    {
      key: 'grammar',
      label: '语法正确性',
      value: grammar,
      show: !!grammar
    },
    {
      key: 'organization',
      label: '组织结构',
      value: organization,
      show: !!organization
    },
    {
      key: 'topicRelevance',
      label: '主题相关性',
      value: topicRelevance,
      show: !!topicRelevance
    }
  ]
  
  // 判断语音评分是否有至少一个显示
  const hasSpeechScore = speechScoreDimensions.some(dim => dim.show)

  const hasContentScore = contentScoreDimensions.some(dim => dim.show)

  // 如果没有任何评分数据，显示提示信息
  if (!hasSpeechScore && !hasContentScore) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-12">
        <div className="text-gray-400 text-lg mb-2">暂无评分数据</div>
        <div className="text-gray-500 text-sm">请完成答题后查看评分结果</div>
      </div>
    )
  }

  return (
    <div className="w-full flex flex-col gap-6">
      {hasSpeechScore && (
        <div>
          <div className="font-semibold text-base mb-4 text-blue-700 flex items-center gap-2">
            <div className="w-1 h-5 bg-blue-700 rounded-full"></div>
            语音评分
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {speechScoreDimensions.filter(dim => dim.show).map((dim) => {
              const score = typeof dim.value?.score === 'number' ? dim.value.score : undefined

              return (
                <div
                  key={dim.key}
                  className={`${getBgGradient(score ?? 0)} rounded-2xl p-5 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-200 border`}
                >
                  <span className={`${getTitleClass(score ?? 0)} font-bold mb-2 text-base md:text-lg tracking-wide`}>
                    {dim.label}
                  </span>
                  <span
                    className={`text-4xl font-black mb-2 leading-none ${
                      typeof score === 'number' ?
                        getScoreColor(score) :
                        'text-gray-300'
                    }`}
                    style={{textShadow: '0 2px 8px rgba(30,64,175,0.08), 0 1px 0 #fff'}}
                  >
                    {typeof score === 'number' ? score.toFixed(1) : '-'}
                  </span>
                  <span className="text-gray-500 text-xs md:text-sm text-center min-h-[1.5em] px-2 leading-relaxed">
                    {dim.value?.comment || <span className="opacity-60">暂无评语</span>}
                  </span>
                </div>
              )
            })}
          </div>
        </div>
      )}
      
      {hasContentScore && (
        <div>
          <div className="font-semibold text-base mb-4 text-yellow-700 flex items-center gap-2">
            <div className="w-1 h-5 bg-yellow-700 rounded-full"></div>
            内容评分
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {contentScoreDimensions.filter(dim => dim.show).map((dim) => {
              const score = typeof dim.value?.score === 'number' ? dim.value.score : undefined

              return (
                <div
                  key={dim.key}
                  className={`${getBgGradient(score ?? 0)} rounded-2xl p-5 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-200 border`}
                >
                  <span className={`${getTitleClass(score ?? 0)} font-bold mb-2 text-base md:text-lg tracking-wide`}>
                    {dim.label}
                  </span>
                  <span
                    className={`text-4xl font-black mb-2 leading-none ${
                      typeof score === 'number' ?
                        getScoreColor(score) :
                        'text-gray-300'
                    }`}
                    style={{textShadow: '0 2px 8px rgba(251,191,36,0.08), 0 1px 0 #fff'}}
                  >
                    {typeof score === 'number' ? score.toFixed(1) : '-'}
                  </span>
                  <span className="text-gray-500 text-xs md:text-sm text-center min-h-[1.5em] px-2 leading-relaxed">
                    {dim.value?.comment || <span className="opacity-60">暂无评语</span>}
                  </span>
                </div>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}

export default RenderScore
