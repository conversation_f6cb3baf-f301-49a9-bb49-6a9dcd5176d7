import React from 'react'
import {Card} from '@/components/ui/card'

interface SummarizeWrittenTextProps {
  model: any
  questionNumber: number
}

const SummarizeWrittenText = (props: SummarizeWrittenTextProps) => {
  const {model, questionNumber} = props
  
  // Get the passage text from the question
  const passageText = model.question?.find((item: any) => item.type === 'text')?.value || ''
  
  // Get standard answer and user answer
  const userAnswer = model.user_answer?.[0]?.value || ''
  
  // Calculate word count for user answer
  const wordCount = userAnswer ? userAnswer.trim().split(/\s+/).length : 0
  
  return (
    <Card className="p-3 md:p-6 mb-4 md:mb-8 shadow-sm border-blue-100">
      <div className="flex justify-between items-start mb-3 md:mb-4">
        <div className="flex items-center">
          <div className="bg-green-500 text-white rounded-md px-2 md:px-3 py-1 text-sm font-medium">
            {String(questionNumber).padStart(2, '0')}
          </div>
          <span className="ml-2 text-gray-600">总结题</span>
        </div>
        <div className="text-gray-600">
          得分: {model.score}
        </div>
      </div>
      <div className="mb-3 md:mb-6">
        <p className="text-lg font-medium mb-3 md:mb-4">
          请用一句话（5-75个单词）总结以下文章的要点:
        </p>
        <div className="p-3 md:p-4 bg-gray-50 rounded-md border border-gray-200 mb-3 md:mb-6">
          <p className="text-gray-800 whitespace-pre-line">
            {passageText}
          </p>
        </div>
        
        <div className="space-y-4">
          {/* <div>
            <h4 className="font-medium text-gray-700 mb-2">标准答案:</h4>
            <div className="p-3 bg-gray-50 rounded-md border border-gray-200">
              <p className="text-gray-800">
                {standardAnswer}
              </p>
            </div>
          </div> */}
          
          {userAnswer && (
            <div>
              <h4 className="font-medium text-gray-700 mb-2">您的答案:</h4>
              <div className="p-3 bg-gray-50 rounded-md border border-gray-200">
                <p className="text-gray-800">
                  {userAnswer}
                </p>
              </div>
              <div className="text-sm text-gray-500 mt-2">字数: {wordCount}</div>
            </div>
          )}
          
          {!userAnswer && (
            <div className="p-3 bg-gray-50 rounded-md border border-gray-200">
              <p className="text-gray-500">
                未作答
              </p>
            </div>
          )}
        </div>
      </div>
      
      {model.score && (
        <div className="bg-blue-50 p-3 md:p-4 rounded-md border border-blue-200">
          <h3 className="text-lg font-medium text-blue-800 mb-3 md:mb-4">
            评分结果
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="text-center p-3 bg-white rounded-lg border border-blue-100">
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {model.score.content || 0}
              </div>
              <div className="text-sm text-gray-600">内容得分</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg border border-blue-100">
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {model.score.form || 0}
              </div>
              <div className="text-sm text-gray-600">形式得分</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg border border-blue-100">
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {model.score.total || 0}
              </div>
              <div className="text-sm text-gray-600">总分</div>
            </div>
          </div>
          
          {model.score.feedback && (
            <div className="mt-4">
              <h4 className="font-medium text-blue-800 mb-2">反馈意见</h4>
              <div className="p-3 bg-white rounded-md">
                <p className="text-gray-800">
                  {model.score.feedback}
                </p>
              </div>
            </div>
          )}
        </div>
      )}
    </Card>
  )
}

export default SummarizeWrittenText