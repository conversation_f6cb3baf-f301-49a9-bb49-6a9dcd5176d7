import React, {useEffect, useRef} from 'react'
import {Card} from '@/components/ui/card'

interface RadarProps {
  model: any
}

const Radar = (props: RadarProps) => {
  const {model} = props

  const chartRef = useRef<HTMLDivElement>(null)

  const chartInstance = useRef<any>(null)
  
  // Get radar chart data from the model - specifically for pronunciation scores
  const radarData = [
    {
      name: '语法准确度',
      value: model?.grammar ?? 0,
      max: 10
    },
    {
      name: '组织条理性',
      value: model?.organization ?? 0,
      max: 10
    },
    {
      name: '发音准确度',
      value: model?.pron_accuracy ?? 0,
      max: 10
    },
    {
      name: '发音清晰度',
      value: model?.pron_clarity ?? 0,
      max: 10
    },
    {
      name: '语言流畅度',
      value: model?.pron_fluency ?? 0,
      max: 10
    },
    {
      name: '话题相关度',
      value: model?.topic_relevance ?? 0,
      max: 10
    },
    {
      name: '词汇丰富度',
      value: model?.vocabulary ?? 0,
      max: 10
    }
  ]
  
  useEffect(() => {
    if (chartRef.current && radarData.length > 0) {
      // Dynamically import echarts
      import('echarts').then((echarts) => {
        // Initialize chart
        chartInstance.current = echarts.init(chartRef.current!)
        
        // Prepare data for radar chart
        const indicators = radarData.map((item: any) => ({
          name: item.name+':'+item.value,
          max: item.max || 100
        }))
        
        const seriesData = [{
          value: radarData.map((item: any) => item.value || 0),
          name: '发音评估'
        }]
        
        const option = {
          title: {
            text: '',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold',
              color: '#fff'
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: function(params: any) {
              return radarData.map((item: any) => `${item.name}: ${item.value}`).join('<br/>')
            }
          },
          radar: {
            indicator: indicators,
            center: ['50%', '55%'],
            radius: '70%',
            splitNumber: 3,
            axisName: {
              fontSize: 14,
              color: '#fff',
              fontWeight: 'bold'
            },
            splitLine: {lineStyle: {color: 'rgba(255, 255, 255, 0.24)'} },
            axisLine: {lineStyle: {color: 'rgba(255, 255, 255, 0.24)'} }
          },
          series: [{
            name: '发音评估',
            type: 'radar',
            data: seriesData,
            itemStyle: {color: '#FF8F2D'},
            areaStyle: {
              opacity: 0.3,
              color: '#FF8F2D'
            },
            lineStyle: {
              width: 3,
              color: '#FF8F2D'
            },
            symbol: 'circle',
            symbolSize: 1
          }]
        }
        
        chartInstance.current.setOption(option)
        
        // Handle resize
        const handleResize = () => {
          if (chartInstance.current) {
            chartInstance.current.resize()
          }
        }
        
        window.addEventListener('resize', handleResize)
        
        return () => {
          window.removeEventListener('resize', handleResize)
        }
      })
    }
    
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose()
      }
    }
  }, [model?.accuracy_score, model?.fluency_score, model?.prosody_score])

  return (   
    <div className="w-full h-full flex flex-col items-center justify-center mt-[70px]">
      <div 
        ref={chartRef} 
        style={{width: '100%', height: '250px'}}
      />  
    </div>
  )
}

export default Radar
