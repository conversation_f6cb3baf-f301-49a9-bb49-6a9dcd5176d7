import React from 'react'
import {Card} from '@/components/ui/card'
import {Badge} from '@/components/ui/badge'

interface FillInTheBlanksProps {
  model: any
  questionNumber: number
}

const FillInTheBlanks = (props: FillInTheBlanksProps) => {
  const {model, questionNumber} = props

  // Find the text content from the question
  const textContent = model.question.find((item: any) => item.type === 'paragraph')?.value?.replace(/\{\{(key\d+)\}\}/g, '<span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded font-medium">$1</span>')
  
  // Find the options for the blanks
  const options = model.question.find((item: any) => item.type === 'option')?.options || {}
  
  // Check if user has answered
  // const hasAnswered = model.user_answer?.length > 0

  const renderBlanks = () => {
    // If there are no options, return null
    if (!options || Object.keys(options).length === 0) return null

    const sortedKeys = Object.keys(options).sort((a,b) => {
      const numA = parseInt(a.replace('key', ''), 10)

      const numB = parseInt(b.replace('key', ''), 10)

      return numA - numB
    })

    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {sortedKeys.map((key: string, index: number) => {
          // Get the correct answer for this blank
          const correctAnswer = model.ref_answer?.[0]?.options?.[key]?.[0]
          
          // Get the user's answer for this blank
          const userAnswer = model.user_answer?.[0]?.options?.[key]?.[0]
          
          // Check if the user's answer is correct
          const isCorrect = userAnswer === correctAnswer
          
          return (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-2 md:p-4">
              <div className="flex items-center mb-2 md:mb-3">
                <Badge className="bg-blue-500 text-white mr-2 md:mr-3 text-sm font-medium px-2 md:px-3 py-1 pointer-events-none">
                  {key}
                </Badge>
                <span className="text-gray-700 font-medium text-sm">选择正确答案</span>
              </div>
              
              <div className="grid grid-cols-1 gap-2 mb-2 md:mb-4">
                {options[key].map((option: string, optIndex: number) => {
                  const isCorrectOption = option === correctAnswer

                  const isUserSelected = option === userAnswer
                  
                  let badgeClass = 'bg-gray-100 text-gray-700 border border-gray-300'
                  
                  if (isCorrectOption) {
                    badgeClass = 'bg-green-100 text-green-800 border border-green-300'
                  }
                  
                  if (isUserSelected && !isCorrectOption) {
                    badgeClass = 'bg-red-100 text-red-800 border border-red-300'
                  }
                  
                  return (
                    <Badge 
                      key={optIndex} 
                      className={`${badgeClass} cursor-pointer text-center py-1 md:py-2 px-2 md:px-3 text-sm font-medium w-full`}
                    >
                      {option}
                    </Badge>
                  )
                })}
              </div>
              
              <div className="space-y-2 pt-2 md:pt-3 border-t border-gray-100">
                <div className="flex items-center">
                  <span className="text-xs text-gray-600 mr-2">您的答案:</span>
                  <Badge className={`text-xs ${isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {userAnswer || '未作答'}
                  </Badge>
                </div>
                <div className="flex items-center">
                  <span className="text-xs text-gray-600 mr-2">正确答案:</span>
                  <Badge className="bg-green-100 text-green-800 text-xs">
                    {correctAnswer}
                  </Badge>
                  <div className={`ml-2 ${isCorrect ? 'text-green-600' : 'text-red-600'}`}>
                    <i className={`fas fa-${isCorrect ? 'check' : 'times'} text-xs`}></i>
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  // Calculate score
  // const calculateScore = () => {
  //   if (!hasAnswered || !model.ref_answer || !model.user_answer) return '0/0'
    
  //   let correct = 0

  //   let total = 0
    
  //   Object.keys(model.ref_answer[0]?.options || {}).forEach(key => {
  //     total++
  //     const correctAnswer = model.ref_answer[0]?.options?.[key]?.[0]

  //     const userAnswer = model.user_answer[0]?.options?.[key]?.[0]

  //     if (correctAnswer === userAnswer) correct++
  //   })
    
  //   return `${correct}/${total}`
  // }

  return (
    <Card className="p-3 md:p-6 mb-4 md:mb-8 shadow-sm border-blue-100">
      <div className="flex justify-between items-start mb-3 md:mb-4">
        <div className="flex items-center">
          <div className="bg-green-500 text-white rounded-md px-2 md:px-3 py-1 text-sm font-medium">
            {questionNumber}
          </div>
          <span className="ml-2 text-gray-600">完形填空题</span>
        </div>
        <div className="text-gray-600">得分: {model.score}</div>
      </div>
      <div className="mb-4 md:mb-6">
        <p className="text-lg font-medium mb-3 md:mb-4">请填写空缺部分:</p>
        <div className="p-3 md:p-4 bg-gray-50 rounded-md border border-gray-200 mb-4 md:mb-6">
          <p className="text-gray-700 leading-loose text-base font-normal tracking-wide" dangerouslySetInnerHTML={{__html: textContent}}></p>
        </div>
        <div>
          {renderBlanks()}
        </div>
      </div>
      <div className="mt-3 md:mt-4 pt-3 md:pt-4 border-t border-gray-100">
        <div className="flex items-center">
          <Badge className="bg-green-100 text-green-800 mr-2">
            正确答案
          </Badge>
          <span className="text-green-600 text-sm font-bold">绿色高亮显示</span>
        </div>
        <div className="flex items-center mt-2">
          <Badge className="bg-red-100 text-red-800 mr-2">
            错误答案
          </Badge>
          <span className="text-red-400 text-sm font-bold">红色高亮显示</span>
        </div>
      </div>
    </Card>
  )
}

export default FillInTheBlanks
