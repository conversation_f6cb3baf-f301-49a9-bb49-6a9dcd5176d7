import React, {useState, useRef, useEffect} from 'react'
import {Card} from '@/components/ui/card'
import {Button} from '@/components/ui/button'
import RenderScore from './render-score'

interface RepeatSentenceProps {
  model: any
  questionNumber: number
}

const RepeatSentence = (props: RepeatSentenceProps) => {
  const {model, questionNumber} = props

  const [originalAudioPlaying, setOriginalAudioPlaying] = useState<boolean>(false)

  const [userAudioPlaying, setUserAudioPlaying] = useState<boolean>(false)

  const [hoveredWord, setHoveredWord] = useState<number | null>(null)

  const originalAudioRef = useRef<HTMLAudioElement | null>(null)

  const userAudioRef = useRef<HTMLAudioElement | null>(null)

  // Clean up when component unmounts
  useEffect(() => {
    return () => {
      if (originalAudioRef.current) {
        originalAudioRef.current.pause()
      }
      if (userAudioRef.current) {
        userAudioRef.current.pause()
      }
    }
  }, [])

  const toggleOriginalAudio = () => {
    // Get original audio URL from the model
    const audioUrl = model.question.find((item: any) => item.type === 'voice')?.value
    
    if (audioUrl) {
      if (!originalAudioRef.current) {
        originalAudioRef.current = new Audio(audioUrl)
        
        // Reset playing state when audio ends
        originalAudioRef.current.onended = () => {
          setOriginalAudioPlaying(false)
        }
      }
      
      if (originalAudioPlaying) {
        // If audio is currently playing, pause it
        originalAudioRef.current.pause()
        setOriginalAudioPlaying(false)
      } else {
        // If audio is not playing, play it
        // Pause user audio if it's playing
        if (userAudioPlaying && userAudioRef.current) {
          userAudioRef.current.pause()
          setUserAudioPlaying(false)
        }
        
        originalAudioRef.current.play().catch(error => {
          console.error('Error playing original audio:', error)
          setOriginalAudioPlaying(false)
        })
        setOriginalAudioPlaying(true)
      }
    } else {
      console.warn('No original audio URL available')
      setOriginalAudioPlaying(false)
    }
  }

  const toggleUserAudio = () => {
    // Get user audio URL from the model
    const audioUrl = model.user_answer?.[0]?.value
    
    if (audioUrl) {
      if (!userAudioRef.current) {
        userAudioRef.current = new Audio(audioUrl)
        
        // Reset playing state when audio ends
        userAudioRef.current.onended = () => {
          setUserAudioPlaying(false)
        }
      }
      
      if (userAudioPlaying) {
        // If audio is currently playing, pause it
        userAudioRef.current.pause()
        setUserAudioPlaying(false)
      } else {
        // If audio is not playing, play it
        // Pause original audio if it's playing
        if (originalAudioPlaying && originalAudioRef.current) {
          originalAudioRef.current.pause()
          setOriginalAudioPlaying(false)
        }
        
        userAudioRef.current.play().catch(error => {
          console.error('Error playing user audio:', error)
          setUserAudioPlaying(false)
        })
        setUserAudioPlaying(true)
      }
    } else {
      console.warn('No user audio URL available')
      setUserAudioPlaying(false)
    }
  }

  // Find the text content from the question
  const textContent = model.ref_answer.find((item: any) => item.type === 'text')?.value || ''
  
  // Check if user has answered
  const hasAnswered = model.user_answer?.length > 0

  const wordDetails = model?.rate_result?.words || []

  // Helper function to get score color
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'

    return 'text-red-600'
  }

  // Helper function to get error type text
  const getErrorTypeText = (errorType: string) => {
    switch (errorType) {
    case 'Mispronunciation':
      return '发音错误'
    case 'Omission':
      return '遗漏'
    case 'Insertion':
      return '插入'
    case 'Stress':
      return '重音错误'
    default:
      return '正确'
    }
  }

  return (
    <Card className="p-3 md:p-6 mb-4 md:mb-8 shadow-sm border-blue-100">
      <div className="flex justify-between items-start mb-3 md:mb-4">
        <div className="flex items-center">
          <div className="bg-green-500 text-white rounded-md px-2 md:px-3 py-1 text-sm font-medium">
            {questionNumber}
          </div>
          <span className="ml-2 text-gray-600">听读</span>
        </div>
        <div className="text-gray-600">得分: {model.score}</div>
      </div>
      <div className="mb-3 md:mb-6">
        <p className="text-lg font-medium mb-3 md:mb-4">请听录音并重复句子:</p>
        <div className="flex items-center mb-3 md:mb-4">
          <Button 
            onClick={toggleOriginalAudio}
            className="!rounded-button whitespace-nowrap flex items-center mr-3 bg-blue-600 hover:bg-blue-700 cursor-pointer"
          >
            <i className={'fas mr-2'}>
              <img src="https://gusto-english-oss.wemore.com/exam/icon-listen-3%403x.png" alt="" width="12" height="12" />
            </i>                 {originalAudioPlaying ? '暂停播放' : '播放原句'}
          </Button>
        </div>
        <div className="p-3 md:p-4 bg-gray-50 rounded-md border border-gray-200 mb-3 md:mb-4">
          <p className="text-gray-800">
            "{textContent}"
          </p>
        </div>
        {hasAnswered ? (
          <div className="flex items-center mb-3 md:mb-6">
            <Button 
              onClick={toggleUserAudio}
              className="!rounded-button whitespace-nowrap flex items-center mr-3 bg-blue-600 hover:bg-blue-700 cursor-pointer"
            >
              <i className={'fas mr-2'}>
                <img src="https://gusto-english-oss.wemore.com/exam/icon-listen-3%403x.png" alt="" width="12" height="12" />
              </i>                   {userAudioPlaying ? '暂停播放' : '播放录音'}
            </Button>
            
          </div>
        ) : (
          <div className="text-gray-600 mb-3 md:mb-6">未回答</div>
        )}
        {hasAnswered && (
          <div className="bg-blue-50 p-3 md:p-4 rounded-md border border-blue-200">
            <h3 className="text-lg font-medium text-blue-800 mb-3 md:mb-4">
              评分结果
            </h3>
            <div>
              <RenderScore rateResult={model.rate_result} />
            </div>
            <h4 className="font-medium text-blue-800 mb-2 mt-3 md:mt-4">单词评估详情</h4>
            <div className="relative p-2 md:p-3 bg-white rounded-md border border-gray-200 leading-relaxed">
              {wordDetails?.map((detail: any, index: number) => {
                const score = detail.accuracy_score || 0

                const colorClass = getScoreColor(score)

                const errorType = detail.error_type || 'None'
                    
                return (
                  <span 
                    key={index} 
                    className={`${colorClass} font-medium text-base md:text-lg mr-2 cursor-pointer hover:underline relative inline-block`}
                    onMouseEnter={() => setHoveredWord(index)}
                    onMouseLeave={() => setHoveredWord(null)}
                  >
                    {detail.word}
                    {hoveredWord === index && (
                      <div className="absolute bottom-full left-0 mb-2 p-2 bg-gray-800 text-white text-xs rounded shadow-lg whitespace-nowrap z-10">
                        <div>错误类型: {getErrorTypeText(errorType)}</div>
                        <div>准确度: {score}</div>
                        {detail.phonemes && detail.phonemes.length > 0 && (
                          <div>
                            音素详情: {detail.phonemes.map((phoneme: any, pIndex: number) => (
                              <span key={pIndex} className="mr-1">
                                {phoneme.phoneme}({phoneme.accuracy_score})
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    )}
                  </span>
                )
              })}
            </div>
            <div className="flex flex-wrap gap-3 md:gap-4 mt-3 md:mt-4 text-xs md:text-sm">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-600 rounded-full mr-1"></div>
                <span>发音正确</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-yellow-600 rounded-full mr-1"></div>
                <span>轻微问题</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-600 rounded-full mr-1"></div>
                <span>发音错误</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </Card>
  )
}

export default RepeatSentence
