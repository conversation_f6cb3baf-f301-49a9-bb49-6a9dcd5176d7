import React from 'react'
import {Card} from '@/components/ui/card'
import {Badge} from '@/components/ui/badge'

interface SingleImageChoiceProps {
  model: any
  questionNumber: number
}

const SingleImageChoice: React.FC<SingleImageChoiceProps> = ({
  model,
  questionNumber,
}) => {
  // 获取题干
  const title =
    model.question.find((item: any) => item.type === 'text')?.value || ''

  // 获取选项
  const options =
    model.question.find((item: any) => item.type === 'option')?.options || {}

  // 用户是否作答
  const hasResult =
    Array.isArray(model.user_answer) && model.user_answer.length > 0

  // 用户选择的key
  const userSelectedKey = (() => {
    const userOptions = model.user_answer?.[0]?.options

    if (!userOptions) return null
    
    // 找到第一个有值的key
    const selectedKey = Object.keys(userOptions).find(key => 
      userOptions[key] && userOptions[key].length > 0
    )
    
    return selectedKey || null
  })()

  // 正确答案key集合
  const correctKeys = Object.keys(model.ref_answer?.[0]?.options || {}).filter(
    (key) => model.ref_answer?.[0]?.options?.[key]?.length > 0
  )

  if (questionNumber === 11) {
    console.info(correctKeys, 'correctKeys')
    console.info(userSelectedKey, 'userSelectedKey')
    console.info(questionNumber, 'questionNumber')
  }
  // 用户是否答对
  const isAnswerCorrect = hasResult && userSelectedKey && correctKeys.includes(userSelectedKey)

  // 渲染选项
  const renderOptions = () => {
    return Object.keys(options).map((key) => {
      const optionImg = options[key]?.[0] || options[key]

      // 该选项是否为正确答案
      const isCorrect = correctKeys.includes(key)

      // 该选项是否为用户选择
      const isUserSelected = userSelectedKey === key

      let bgClass = 'bg-white'

      if (isUserSelected && !isCorrect) {
        bgClass = 'bg-red-300'
      }
      if (isCorrect) {
        bgClass = 'bg-green-300'
      }

      return (
        <div
          key={key}
          className={`p-1 md:p-3 rounded-md border border-gray-200 flex-1 ${bgClass}`}
        >
          <div className="flex items-start">
            <div className="flex-grow">
              <div className="rounded-md overflow-hidden">
                <img
                  src={optionImg}
                  alt={`option-${key}`}
                  className="w-full h-auto object-cover object-top"
                />
              </div>
            </div>
          </div>
        </div>
      )
    })
  }

  return (
    <Card className="p-2 md:p-6 mb-3 md:mb-8 shadow-sm border-blue-100">
      <div className="flex justify-between items-start mb-2 md:mb-4">
        <div className="flex items-center">
          <div className="bg-green-500 text-white rounded-md px-2 md:px-3 py-1 text-sm font-medium">
            {questionNumber}
          </div>
          <span className="ml-2 text-gray-600">单选题</span>
        </div>
        <div className="text-gray-600">得分: {model.score}</div>
      </div>
      <div className="mb-2 md:mb-4">
        <p className="text-lg font-medium mb-2 md:mb-4">{title}</p>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-[8px]">{renderOptions()}</div>
      </div>
      <div className="mt-2 md:mt-4 pt-2 md:pt-4 border-t border-gray-100">
        <div className="flex items-center">
          <Badge className="bg-green-300 text-green-800 mr-2">正确答案</Badge>
          <span className="text-green-300 text-sm font-bold">绿色背景</span>
        </div>
        <div className="flex items-center mt-2">
          <Badge className="bg-red-300 text-red-800 mr-2">错误答案</Badge>
          <span className="text-red-300 text-sm font-bold">红色背景</span>
        </div>
        <div className="flex items-center mt-2 text-sm font-bold">
          {hasResult ? (isAnswerCorrect ? '回答正确' : '回答错误') : '无回答'}
        </div>
      </div>
    </Card>
  )
}

export default SingleImageChoice
