import React, {useState, useRef, useEffect} from 'react'
import {Card} from '@/components/ui/card'
import {Button} from '@/components/ui/button'

interface WriteFromDictationProps {
  model: any
  questionNumber: number
}

const WriteFromDictation = (props: WriteFromDictationProps) => {
  const {model, questionNumber} = props

  const [audioPlaying, setAudioPlaying] = useState<boolean>(false)

  const audioRef = useRef<HTMLAudioElement | null>(null)

  // Clean up when component unmounts
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause()
      }
    }
  }, [])

  const toggleAudio = () => {
    // Get audio URL from the model
    const audioUrl = model.question.find((item: any) => item.type === 'voice')?.value
    
    if (audioUrl) {
      if (!audioRef.current) {
        audioRef.current = new Audio(audioUrl)
        
        // Reset playing state when audio ends
        audioRef.current.onended = () => {
          setAudioPlaying(false)
        }
      }
      
      if (audioPlaying) {
        // If audio is currently playing, pause it
        audioRef.current.pause()
        setAudioPlaying(false)
      } else {
        // If audio is not playing, play it
        audioRef.current.play().catch(error => {
          console.error('Error playing audio:', error)
          setAudioPlaying(false)
        })
        setAudioPlaying(true)
      }
    } else {
      console.warn('No audio URL available')
      setAudioPlaying(false)
    }
  }

  // Get standard answer and user answer
  const standardAnswer = model.ref_answer?.[0]?.value || ''

  const userAnswer = model.user_answer?.[0]?.value || ''
  
  // Find differences between standard answer and user answer
  const findDifferences = () => {
    if (!standardAnswer || !userAnswer) return null
    
    const standardWords = standardAnswer.replace(/[^\w\s]/g, '').split(/\s+/)

    const userWords = userAnswer.replace(/[^\w\s]/g, '').split(/\s+/)
    
    // Create a comparison text highlighting differences
    const comparisonElements = []

    const maxLength = Math.max(standardWords.length, userWords.length)
    
    for (let i = 0; i < maxLength; i++) {
      if (i > 0) comparisonElements.push(' ')
      
      const standardWord = standardWords[i] || ''

      const userWord = userWords[i] || ''
      
      if (standardWord.toLowerCase() !== userWord.toLowerCase()) {
        comparisonElements.push(
          <span key={`diff-${i}`} className="bg-yellow-100 px-1">
            {standardWord}
          </span>
        )
        if (userWord) {
          comparisonElements.push('/')
          comparisonElements.push(
            <span key={`user-${i}`} className="bg-yellow-100 px-1">
              {userWord}
            </span>
          )
        }
      } else {
        comparisonElements.push(standardWord)
      }
    }
    
    return comparisonElements
  }

  return (
    <Card className="p-3 md:p-6 mb-4 md:mb-8 shadow-sm border-blue-100">
      <div className="flex justify-between items-start mb-3 md:mb-4">
        <div className="flex items-center">
          <div className="bg-green-500 text-white rounded-md px-2 md:px-3 py-1 text-sm font-medium">
            {String(questionNumber).padStart(2, '0')}
          </div>
          <span className="ml-2 text-gray-600">听写题</span>
        </div>
        <div className="text-gray-600">
          得分: {model.score}
        </div>
      </div>
      <div className="mb-3 md:mb-6">
        <p className="text-lg font-medium mb-3 md:mb-4">
          请听录音并写下您听到的内容:
        </p>
        <div className="flex items-center mb-3 md:mb-6">
          <Button 
            className="!rounded-button whitespace-nowrap flex items-center mr-3 bg-blue-600 hover:bg-blue-700 cursor-pointer"
            onClick={toggleAudio}
          >
            <i className={'fas mr-2'}>
              <img src="https://gusto-english-oss.wemore.com/exam/icon-listen-3%403x.png" alt="" width="12" height="12" />
            </i>                 {audioPlaying ? '暂停播放' : '播放录音'}
          </Button>
          {/* <div className="text-sm text-gray-500">
            {model.question.find((item: any) => item.type === 'voice')?.duration ? 
              formatTime(model.question.find((item: any) => item.type === 'voice')?.duration) : 
              '00:00'}
          </div> */}
        </div>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">标准答案:</h4>
            <div className="p-3 bg-gray-50 rounded-md border border-gray-200">
              <p className="text-gray-800">
                {standardAnswer}
              </p>
            </div>
          </div>
          {(
            <div>
              <h4 className="font-medium text-gray-700 mb-2">您的答案:</h4>
              <div className="p-3 bg-gray-50 rounded-md border border-gray-200">
                <p className="text-gray-800">
                  {userAnswer ? userAnswer : '未作答'}
                </p>
              </div>
            </div>
          )}
          {standardAnswer && userAnswer && (
            <div>
              <h4 className="font-medium text-gray-700 mb-2">对比:</h4>
              <div className="p-3 bg-white rounded-md border border-gray-200">
                <p className="text-gray-800">
                  "{findDifferences()}"
                </p>
                {standardAnswer.toLowerCase() !== userAnswer.toLowerCase() && (
                  <div className="mt-2 text-sm text-gray-600">
                    <p>
                      注: 标准答案与您的答案存在差异，请注意对比。
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </Card>
  )
}

export default WriteFromDictation
