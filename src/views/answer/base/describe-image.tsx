import React from 'react'
import {Card} from '@/components/ui/card'
import RenderScore from './render-score'

interface DescribeImageProps {
  model: any
  questionNumber: number
}

const DescribeImage: React.FC<DescribeImageProps> = ({model, questionNumber}) => {
  // Find the image URL from the question
  const imageUrl = model.question.find((item: any) => item.type === 'image')?.value || ''
  
  // Check if user has answered
  const hasAnswered = model.user_answer?.length > 0

  const userAnswer = model.user_answer?.[0]?.value || ''

  return (
    <Card className="p-3 md:p-6 mb-4 md:mb-8 shadow-sm border-blue-100">
      <div className="flex justify-between items-start mb-3 md:mb-4">
        <div className="flex items-center">
          <div className="bg-green-500 text-white rounded-md px-2 md:px-3 py-1 text-sm font-medium">
            {questionNumber}
          </div>
          <span className="ml-2 text-gray-600">图片描述题</span>
        </div>
        <div className="text-gray-600">得分: {model.score}</div>
      </div>
      <div className="mb-3 md:mb-6">
        <p className="text-lg font-medium mb-3 md:mb-4">请用英语描述下面的图片:</p>
        <div className="mb-3 md:mb-4 rounded-lg overflow-hidden">
          <img
            src={imageUrl}
            alt="Description image"
            className="w-full max-h-[300px] h-auto object-contain object-center"
          />
        </div>
      </div>
      <div>
        <h4 className="font-medium text-gray-700 mb-2">您的答案:</h4>
        <div className="p-3 bg-gray-50 rounded-md border border-gray-200">
          <div className="flex items-center mb-2">
            {userAnswer}
          </div>
          <p className="text-gray-800">
            {hasAnswered ? '' : '未作答'}
          </p>
        </div>
      </div>

      <div className="bg-blue-50 p-3 md:p-4 rounded-md border border-blue-200 mt-[20px]">
        <h3 className="text-lg font-medium text-blue-800 mb-3 md:mb-4">
            评分结果
        </h3>
        <div>
          <RenderScore rateResult={model.rate_result} />
        </div>
      </div>
    </Card>
  )
}

export default DescribeImage
