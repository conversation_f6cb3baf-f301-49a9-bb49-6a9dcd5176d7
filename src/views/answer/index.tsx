'use client'
// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, {useEffect, useState} from 'react'
import Image from 'next/image'
import {getStorage} from '@/utils/storage'
import {getAnswerDetailApi} from '@/http/http'
import SingleImageChoice from './base/single-image-choice'
import FillInTheBlanks from './base/fill-in-blank'
import ReadingAloud from './base/read-aloud'
import RepeatSentence from './base/repeat-sentence'
import WriteFromDictation from './base/write-from-dictation'
import SummarizeWrittenText from './base/summarize-written-text'
import QuickQA from './base/quick-qa'
import DescribeImage from './base/describe-image'
import {Button} from '@/components/ui/button'
import CommonQuestion from './base/common-question'
import styles from './index.module.css'
import Radar from './base/radar'

const AnswerView = ({exId}: {exId: string}) => {

  const [examInfo, setExamInfo] = useState<any>({})

  const [errStatus, setErrorStatus] = useState('init')

  useEffect(() => {
    getAnswerDetailApi(Number(exId || '576')).then((res: any) => {
      // console.log(res, 'res')
      setExamInfo(res.data?.data)
      setErrorStatus('finish')
    }).catch(err => {
      console.log(err, 'err1111')
      setErrorStatus('error')
    })
  }, [exId])

  const renderQuestionLists = () => {
    return examInfo?.question_answers?.map((item: any, index: number) => {
      if (item.question_type === 'single_image_choice') {
        return <SingleImageChoice model={item} questionNumber={index + 1} key={index + item.question_id} />
      }
      if (item.question_type === 'fill_in_the_blanks') {
        return <FillInTheBlanks model={item} questionNumber={index + 1} key={index + item.question_id} />
      }
      if (item.question_type === 'read_aloud') {
        return <ReadingAloud model={item} questionNumber={index + 1} key={index + item.question_id} />
      }
      if (item.question_type === 'repeat_sentence') {
        return <RepeatSentence model={item} questionNumber={index + 1} key={index + item.question_id} />
      }
      if (item.question_type === 'write_from_dictation') {
        return <WriteFromDictation model={item} questionNumber={index + 1} key={index + item.question_id} />
      }
      if (item.question_type === 'summarize_written_text') {
        return <SummarizeWrittenText model={item} questionNumber={index + 1} key={index + item.question_id} />
      }
      if (item.question_type === 'quick_qa') {
        return <QuickQA model={item} questionNumber={index + 1} key={index + item.question_id} />
      }
      if (item.question_type === 'describe_image') {
        return <DescribeImage model={item} questionNumber={index + 1} key={index + item.question_id} />
      }

      return <CommonQuestion model={item} questionNumber={index + 1} key={index + item.question_id} />
    })
  }

  if (errStatus === 'error') {
    return (
      <div className={`${styles.page} min-h-screen bg-gradient-to-br from-blue-100 via-white to-gray-100 flex items-center justify-center px-4 py-8`}>
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full flex flex-col items-center">
          <div className="flex flex-col items-center mb-6">
            <img src="/new/appicon-gusto-test.png" alt="Gusto English" width={60} height={60} className="mb-3 rounded-full shadow" />
            <h1 className="text-2xl font-extrabold text-blue-700 mb-2 tracking-tight">
              无法获取考试信息
            </h1>
            <p className="text-gray-500 text-base mb-1">
              当前用户不在考试内或考试信息已过期
            </p>
          </div>
          <Button 
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 rounded-lg shadow transition"
            onClick={() => {
              // 需要保存path
              if (!getStorage('x-5e-token')) {
                window.location.href = '/login-quiz?redirectGustoUrl=' + window.location.href

                return
              }
              window.location.reload()
            }}
          >
            刷新页面
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div>
      <div className={`${styles.page} min-h-screen bg-gray-50 py-2 px-4 md:px-8`}>
        <div className='flex items-center gap-[10px] cursor-pointer z-9999 mt-2'>
          <Image
            src="/new/appicon-gusto-test.png"
            alt="Gusto English"
            width={45}
            height={45}
            priority
          />
          <div className="flex flex-col gap-[2px] justify-center">
            <p className="font-['GTVCS-Black'] font-black text-[15.9px] leading-[100%] tracking-[0px] text-white">
          Gusto English
            </p>
            <p className="font-['GTVCS-Black'] font-black text-[13.8px] leading-[100%] tracking-[0px] text-white mt-[2px]">
          高拓英语水平测试
            </p>
          </div>
        </div>
        <div className={`${styles.resultContainer} max-w-4xl mx-auto`} >
          {examInfo?.avg_rate_result && <Radar model={examInfo?.avg_rate_result || {}} />}
        </div>
        <div className={`${styles.resultTitle} max-w-4xl mx-auto`}>
          {examInfo?.exam_glevel}
        </div>
        <div className={`${styles.resultDesc} max-w-4xl mx-auto`}>
      很棒，你当前的英语水平是
        </div>
        <div className={`${styles.resultDesc2} max-w-4xl mx-auto`}>
          {examInfo?.exam_glevel_desc}

        </div>
        <div className="max-w-4xl mx-auto">
          <div className={styles.questionList}>
            <div className={styles.questionListTitle}>
          测试结果详情
            </div>
            {renderQuestionLists()}
          </div>
          <div className={styles.footer}>
            <img src="/new/bottom.svg" alt="" />
          </div>
        </div>
      </div>
    </div>
  )
}

export default AnswerView
