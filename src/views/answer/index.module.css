.page {
    width: 100vw;
    height: calc(var(--vh, 1vh) * 100);
    background: linear-gradient(180deg, #080808 18.04%, #EC2726 100%);
    overflow-y: auto;
}   

.questionList {
    width: 100%;
    height: 100%;
    border-radius: 24px;
    background: #fff;
    backdrop-filter: blur(3.684210777282715px);
    margin-top: 40px;
    padding: 4px;
}

.questionListTitle {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #060000;
    text-align: center;
    font-size: 18.87px;
    font-style: normal;
    font-weight: 943;
    line-height: normal;
    padding: 30px 0;
}

.footer {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40px;
    margin-bottom: 40px;
}

.resultTitle {
    color: #FFC641;
    text-align: center;
    font-size: 41.235px;
    font-style: normal;
    font-weight: 943;
    line-height: normal;
}

.resultDesc {
    color: #FFF;
    text-align: center;
    font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", Helvetica, Tahoma, Arial, "Hiragino Sans GB", "Microsoft YaHei", 微软雅黑, SimSun, 宋体, Heiti, 黑体, sans-serif;
    font-size: 18.87px;
    font-style: normal;
    font-weight: 943;
    line-height: normal;
    margin-top: 6px;
    margin-bottom: 14px;
}

.resultDesc2 {
    color: #FFF;
    font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", Helvetica, Tahoma, Arial, "Hiragino Sans GB", "Microsoft YaHei", 微软雅黑, SimSun, 宋体, Heiti, 黑体, sans-serif;
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 457;
    line-height: 20px; /* 142.857% */
}

.resultContainer {
    height: 250px;
    width: 100%;
    position: relative;
    z-index: 10;
}