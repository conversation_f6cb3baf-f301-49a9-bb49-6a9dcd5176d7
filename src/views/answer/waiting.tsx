'use client'

import {useState} from 'react'
import {Button} from '@/components/ui/button'

const WaitingPage = () => {
  const [isLoading, setIsLoading] = useState(false)

  const handleGoToWebsite = () => {
    setIsLoading(true)
    // 跳转到官网
    window.open('https://www.gustoenglish.com', '_blank')
    setIsLoading(false)
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mb-6">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">
            您已完成 GustoEnglish 水平评估
          </h1>
          <p className="text-gray-600">
            我们正在分析您的答案，请稍候...
          </p>
        </div>

        <div className="mb-6">
          <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
            <p className="text-blue-800 text-sm">
              评估结果将在24小时内通过 GustoEnglish 微信服务号发给您
            </p>
          </div>
        </div>

        <Button 
          onClick={handleGoToWebsite}
          disabled={isLoading}
          className="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
        >
          {isLoading ? '跳转中...' : '查看更多精彩内容'}
        </Button>

        <p className="text-gray-500 text-sm mt-4">
          感谢您参与 Gusto English 水平评估
        </p>
      </div>
    </div>
  )
}

export default WaitingPage
