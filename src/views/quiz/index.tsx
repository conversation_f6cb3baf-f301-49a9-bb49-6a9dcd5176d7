'use client'
import HintModal from '@/components/business/HintModal'
import {Toaster} from '@/components/ui/toaster'
import {useCheckAllowExam, useCheckUrlQuery, useMediaPermission} from '@/hooks/root'
import ExamTitle from './exam/title'
import ExamContent from './exam/content'
import ExamBtn from './exam/btn'
import useInitQuestion from '@/hooks/useInitQuestion'
import useQuizStore from '@/store/quiz'
import {useEffect, useState} from 'react'
import FinishView from './exam/finish'
import useStartRecord from '@/hooks/useStartRecord'
import {publishAnswerApi} from '@/http/http'
import ToolTipBox from '@/components/business/ToolTipBox'
import useRefreshHint from '@/hooks/useRefreshHint'
import {Button} from '@/components/ui/button'
import {getStorage, getStorageJson, setStorage} from '@/utils/storage'
import CheckContent from '@/components/business/CheckContent'
import useBehavioralLimit from '@/hooks/useBehavioralLimit'
import {LoginUrl} from '@/global/consts'
import {useToast} from '@/hooks/use-toast'
import {useRouter} from 'next/navigation'
import useWechatAuth from '@/hooks/useWechatAuth'
const initMedia = {
  type: '', isShow: false, isAutoplay: false, isInit: false, isCheckContent: false
}

const QuizView = ({quizType}: {quizType: string}) => {

  // 检查当前路由
  const [initToken] = useCheckUrlQuery()

  const [allowNext] = useWechatAuth(initToken)

  // 检查刷新提示
  useRefreshHint()
  // 检查行为限制
  useBehavioralLimit()
  // 检查是否允许考试
  useCheckAllowExam(initToken)

  const {isRequestMediaPermission} = useMediaPermission({allowNext})

  const [nextTypeInfo, setNextTypeInfo] = useState({type: 'quiz'})

  const [examMediaInfo, setExamMediaInfo] = useState(initMedia)

  const [btnStatus, setBtnStatus] = useState('')

  const [loading, setLoading] = useState(true)

  const router = useRouter()

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const VConsole = require('vconsole')

      if (window.location.href.includes('debug')) {
        new VConsole()
      }
    }
  }, [])

  const updateFn = (isOnlyCloseLoading: boolean = false) => {
    // console.warn('我执行了嘛', isOnlyCloseLoading)
    if (isOnlyCloseLoading) {
      setLoading(false)
    } else {
      setLoading(false)
      setExamMediaInfo(f => ({
        ...f, isInit: true, isCheckContent: false, isAutoplay: true
      }))
    }
  }

  // console.warn(quizType, 'quizType')
  const {updateQuestionNumber} = useInitQuestion({quizType, fn: updateFn, allowNext})

  const [isSubmitting, setIsSubmitting] = useState(false)

  const {toast} = useToast()

  const {
    currentQuestionNumber, 
    totalNumber, 
    currentQuestion = {} as any, 
    currentAnswer, 
    deviceInfo, 
    currentExamId,
    questionLists
  } = useQuizStore(state => state)

  const {setCurrentAnswer, setCurrentQuestion} = useQuizStore.getState()

  const {startRecording, stopRecording} = useStartRecord()

  // 检查是否需要请求媒体权限
  if (!isRequestMediaPermission) return <div />

  const handleNext = () => {
    // console.log('Next question')
    const ans = {question_id: currentQuestion.id} as any

    updateQuestionNumber(currentQuestionNumber + 1)

    const temp = currentAnswer || []

    const index = temp.findIndex(item => item.question_id === currentQuestion.id)

    // console.warn(index, '--')
    // if (temp.length === 0) {
    //   temp.push(ans)
    //   setCurrentAnswer(temp)
    // }
    if (index === -1) {
      temp.push(ans)
      setCurrentAnswer(temp)
    }
    setStorage(currentExamId + '___cacheAnswer', {number: temp.length, answer: temp, id: currentExamId})
    // console.warn(temp, 'temp')
    setBtnStatus('')
  }

  const handleSubmit = async () => {
    console.log('Submit quiz',currentAnswer, currentQuestion)
    const cacheAnswer = getStorageJson(currentExamId + '___cacheAnswer')?.answer || []

    let temp = [...(currentAnswer || [])]

    if (temp.length < cacheAnswer.length) {
      temp = cacheAnswer
    }

    const ans = {question_id: currentQuestion.id} as any

    const index = temp?.findIndex(item => item.question_id === currentQuestion.id)

    console.info(temp, 'temp')
    if (index === -1) {
      temp.push(ans)
    }

    setIsSubmitting(true)
    publishAnswerApi(currentExamId, temp).then(() => {
      setIsSubmitting(false)
      setNextTypeInfo({type: 'finish'})
      setStorage(currentExamId + '___cacheAnswer', {})
    }).catch(err => {
      // console.error('Failed to submit quiz:', err)
      toast({
        variant: 'destructive',
        title: 'Failed to submit quiz',
        description: err.data.msg,
      })
      setIsSubmitting(false)
    })
  }

  const onStartRecord = () => {
    if (currentQuestion.questionType === 'video') {
      // 视频题 需要隐藏当前题目 然后显示回答状态
      setExamMediaInfo(f => ({...f, isShow: true, type: 'voice'}))
    }
    // console.warn(deviceInfo, 'deviceInfo')
    if (currentQuestion.answerType === 'voice') { 
      startRecording(deviceInfo.audioDeviceId, currentQuestion.id!)
    }
  }

  const onUpdateReply = (t: string) => {
    console.warn(t, 'onUpdateReply')
    setBtnStatus(t)
    // 如果是最后一题则需要进行提交
    if (currentQuestionNumber === totalNumber && t === 'end') {
      handleSubmit()

      return
    }
    if (t === 'end') {
      // 进入到下一题
      handleNext()
    }
  }

  const onStopRecord = async (isMobile?: boolean) => {
    if (currentQuestion.answerType === 'voice') {
      await stopRecording?.()
      if (isMobile) {
        return
      }
    }
    if (currentQuestionNumber === totalNumber) {
      // 需要检查最后一题如果是音频题是否上传成功 暂时delay
      let count = 0

      const timer = setInterval(() => {
        const cacheAnswer = getStorageJson(currentExamId + '___cacheAnswer')?.answer || currentAnswer

        console.info(cacheAnswer, 'currentAnswer', totalNumber, currentAnswer)
        if (cacheAnswer?.[totalNumber - 1]?.answer?.length) {
          handleSubmit()
          clearInterval(timer)  
          setBtnStatus('')        
        }
        // 这个case用来执行5次后提交
        if (currentAnswer.length < totalNumber) {
          count++
          if (count >= 100) {
            handleSubmit()
            clearInterval(timer)  
            setBtnStatus('')        
          }
        }
      }, 1000)

      return
    } else {
      updateQuestionNumber(currentQuestionNumber + 1)
      // setExamMediaInfo(initMedia)
    }
    setBtnStatus('')
    // const temp = currentAnswer || []

    // const ans = {question_id: currentQuestion.id, answer: []} as any

    // const index = temp.findIndex(item => item.question_id === currentQuestion.id)

    // if (index === -1 ) {
    //   temp.push(ans)
    //   setCurrentAnswer(temp)
    // }
  }

  const onChangeText = (text: string, isInput?: boolean) => {
    const ans = {question_id: currentQuestion.id, answer: [{type: isInput ? 'input' : 'text', value: text || ' '}]}

    let fin = currentAnswer || []

    if (currentAnswer?.length) {
      const findIndex = currentAnswer.findIndex(item => item.question_id === currentQuestion.id)

      if (findIndex !== -1) {
        fin = currentAnswer.map(item => {
          if (item.question_id === currentQuestion.id) {
            return ans
          }

          return item
        })
      } else {
        fin = fin.concat([ans])
      }
    } else {
      fin = (fin || []).concat([ans])
    }
    // console.warn(fin, 'fin')
    setCurrentAnswer(fin!)
  }

  const onChangeSelect = (s: {[key: string]: string[]}, isInput?: boolean) => {
    // console.warn(s, 'onChangeSelect')
    const ans = {question_id: currentQuestion.id, answer: isInput ? [{type: 'input', inputs: s}] : [{type: 'option', value: '', options: s}]} as any

    let fin = currentAnswer || []

    if (currentAnswer?.length) {
      const findIndex = currentAnswer.findIndex(item => item.question_id === currentQuestion.id)

      if (findIndex !== -1) {
        fin = currentAnswer.map(item => {
          if (item.question_id === currentQuestion.id) {
            return ans
          }

          return item
        })
      } else {
        fin = fin.concat([ans])
      }
    } else {
      fin = (fin || []).concat([ans])
    }
    console.warn(fin, 'fin')
    setCurrentAnswer(fin!)
  }

  const quitFn = () => {
    if (getStorage('isMine')) {
      router.replace('/website/mine')
      setStorage('isMine', false)

      return
    }
    setStorage('x-5t-token', '')
    setStorage('x-5e-token', '')
    setStorage('x-5e-uid', '')
    
    window.location.href = LoginUrl
  }

  const isQuiz = nextTypeInfo.type === 'quiz'

  const isFinish = nextTypeInfo.type === 'finish'

  const onNext = () => {
    setExamMediaInfo(f => ({...f, isCheckContent: false}))
  }

  // console.warn('当前答案', currentAnswer, examMediaInfo)

  if (loading) {
    return (
      <div className="w-screen h-screen flex flex-col items-center justify-center bg-green-100">
        <div className="relative w-20 h-20">
          <div className="absolute w-full h-full border-4 border-t-4 border-t-transparent border-green-800 rounded-full animate-spin"></div>
        </div>
        <div className="mt-4 text-lg font-semibold text-green-800 animate-pulse">资源加载中...</div>
        <Toaster />
      </div>
    )
  }

  if (examMediaInfo.isCheckContent) {
    return (
      <CheckContent onNext={onNext} />
    )
  }

  if (!examMediaInfo.isInit) {
    if (!quizType.includes('ai_generated') && !quizType.includes('gusto_review')) {
      return (
        <div className="w-full max-w-[600px] mx-auto pt-[32px] mb-[32px]">
          <HintModal isAllowClick={Boolean(questionLists?.length)} onClose={() => {
            if (!questionLists.length) return
            setExamMediaInfo(f => ({
              ...f, isAutoplay: true, isInit: true, isCheckContent: true
            }))} 
          }/>
        </div> 
      )
    } else {
      if (questionLists.length > 0) {
        setExamMediaInfo(f => ({
          ...f, isAutoplay: true, isInit: true, isCheckContent: true
        }))
      }
    }
  }

  // console.info(currentAnswer, 'currentQuestion', currentQuestion)

  return (
    <div className="w-screen h-screen flex flex-col items-center justify-center relative">
      <Button 
        className="fixed top-4 right-4 bg-[#1e88e5] text-white px-4 py-2 rounded" 
        onClick={quitFn}
      >
        Logout
      </Button>
      <div className="w-full max-w-[800px] mx-auto pt-[32px] pb-[1px] mb-[10px] bg-white rounded-lg border border-e6e6e6 shadow-[0_0_15px_0_#cfcfcf] relative">
        <ToolTipBox 
          isOpen={currentQuestion?.tipInfo?.isShow!} 
          onClose={() => {
            setCurrentQuestion({...currentQuestion, tipInfo: {...currentQuestion.tipInfo!, isShow: false} })
          }} 
          title="Gusto English高拓英语线上评估测试，温馨提示" 
          content={currentQuestion?.tipInfo?.content!} 
        />  
        <ExamTitle 
          title={isQuiz ? currentQuestion.introduction! : 'Test Completed 🎉🎉🎉'} 
          isQuiz={isQuiz} 
          currentQuestion={currentQuestionNumber} 
          totalQuestions={totalNumber} 
        />
        {isQuiz && 
          <ExamContent
            key={currentQuestionNumber}
            model={currentQuestion} 
            examMediaInfo={examMediaInfo} 
            onStartRecord={onStartRecord} 
            onStopRecord={onStopRecord} 
            onChangeText={onChangeText}
            onChangeSelect={onChangeSelect}
            onUpdateReply={onUpdateReply}
          />}
        {isFinish && <FinishView />}
      </div>
      {isQuiz && <div className="w-[750px]">
        <ExamBtn
          isLastQuestion={currentQuestionNumber === totalNumber}
          isSubmitting={isSubmitting}
          showButton={(btnStatus === 'start' && currentQuestion.answerType !== 'voice') || currentQuestion.questionType === 'single_image_choice'}
          onNext={handleNext}
          onSubmit={handleSubmit}
        />
      </div>}
      <Toaster />
    </div>
  )
}

export default QuizView