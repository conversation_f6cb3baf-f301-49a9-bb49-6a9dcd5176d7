'use client'

import {But<PERSON>} from '@/components/ui/button'

type Props = {
  isLastQuestion: boolean
  isSubmitting: boolean
  showButton: boolean // Added to control button visibility
  onNext: () => void
  onSubmit: () => void
  disabled?: boolean
}

const ExamBtn = (props: Props) => {
  const {
    isLastQuestion,
    isSubmitting,
    showButton,
    onNext,
    onSubmit,
    disabled = false,
  } = props

  if (!showButton) {
    return null
  }

  if (isSubmitting) {
    return (
      <Button className="w-full" disabled={true}>
        提交中...
      </Button>
    )
  }

  if (isLastQuestion) {
    return (
      <Button className="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded" onClick={onSubmit} disabled={disabled}>
        Submit
      </Button>
    )
  }

  return (
    <Button className="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded" onClick={onNext} disabled={disabled}>
      Next
    </Button>
  )
}

export default ExamBtn
