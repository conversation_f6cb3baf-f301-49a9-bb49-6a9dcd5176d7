'use client'

interface ExamTitleProps {
  title: string
  currentQuestion: number
  totalQuestions: number
  isQuiz?: boolean
}

const ExamTitle = ({
  currentQuestion, totalQuestions, isQuiz, title
}: ExamTitleProps) => {

  const renderContents = () => {
    if (isQuiz) {
      return (
        <div className="flex items-center gap-1 ml-4">
          <span className="text-2xl font-bold text-green-500 relative top-[-2px]">{currentQuestion}</span>
          <span className="text-base font-semibold text-gray-500">/ {totalQuestions}</span>
        </div>
      )
    }

    return <div />
  }

  return (
    <div className="relative h-[auto] flex flex-col justify-center">
      {renderContents()}

      <h3 className={`text-[#333] px-4 mt-[16px] ${
        isQuiz ? 'text-[16px]' : 'text-[24px] text-center'}`}>
        {title}
      </h3>
    </div>
  )
}

export default ExamTitle
