'use client'

import {CutDownRight} from '@/components/business/cutDown'
import ProgressBar from '@/components/business/progress'
import {SelectViewEx} from '@/components/business/SelectViewExpand'
import {Button} from '@/components/ui/button'
import {Textarea} from '@/components/ui/textarea'
import {countSegments, playDingMusic} from '@/global/tool'
import {Question} from '@/store/quiz'
import Image from 'next/image'

type Option = string[] | {
  image?: string
  text?: string
  value: string
}

interface ContentItem {
  type: string;
  value: string;
  options?: Option[];
}

interface ExamContentProps {
  model: Question
  examMediaInfo: {
    type: string
    isShow: boolean
    isAutoplay: boolean
  }
  onStartRecord?(): void
  onStopRecord?(isMobile?: boolean): void
  onChangeText?(s: string, isInput?: boolean): void
  onUpdateReply?(t: string): void
  onChangeSelect?(s: {[key: string]: string[]}, isInput?: boolean): void
}

import {useEffect, useRef, useState} from 'react'

const ExamContent = ({
  model, examMediaInfo, onStartRecord, onStopRecord, onChangeText, onUpdateReply, onChangeSelect
}: ExamContentProps) => {
  // 渲染内容区
  const videoRefs = useRef<{[key: string]: HTMLVideoElement}>({})

  const [isMobile, setIsMobile] = useState(false)

  // const [hasPlayed, setHasPlayed] = useState(false)

  const audioRefs = useRef<{[key: string]: HTMLAudioElement}>({})

  const selectValueRef = useRef<{[key: string]: string[]}>({})

  const selectInputsRef = useRef<{[key: string]: string}>({})

  const [mediaDuration, setMediaDuration] = useState(0)

  const [textValue, setTextValue] = useState('')

  const [isShowAnswerContent, setIsShowAnswerContent] = useState(false)

  const [selectedOption, setSelectedOption] = useState<string | null>(null)

  const handleOptionClick = (option: Option) => {
    if (onUpdateReply) {
      if (Array.isArray(option)) {
        onUpdateReply(option.join(','))
      } else {
        onUpdateReply(option.value)
      }
    }
  }

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsMobile(/iPhone|iPad|iPod|Android/i.test(window.navigator.userAgent))
    }
  }, [])

  useEffect(() => {
    setTextValue('')
    setTimeout(() => {
      setIsShowAnswerContent(false)
    }, 10)
  }, [model.title])

  useEffect(() => {
    const handleVideoPlayback = () => {
      if (model.tipInfo ) {
        // console.warn('我执行了嘛2')
        if (!model.tipInfo?.isShow) {
          Object.values(videoRefs.current).forEach(video => {
            if (video) {
              video.play().catch(error => {
                console.info('Error playing video:', error)
              })
            }
          })
        }
      } else if (examMediaInfo.isAutoplay) {
        // console.warn('我执行了嘛')
        Object.values(videoRefs.current).forEach(video => {
          if (video) {
            video.play().catch(error => {
              console.info('Error playing video:', error)
            })
          }
        })
      }
    }

    if (examMediaInfo.isAutoplay) {
      handleVideoPlayback()
    }
  }, [examMediaInfo.isAutoplay, model])

  useEffect(() => {
    autoPlayAudio()
  }, [model.tipInfo,examMediaInfo, model.title])

  useEffect(() => {
    // console.warn(model.questionType, 'model', model)
    if (model.questionType !== 'audio' && model.questionType !== 'self_intro' && model.questionType !== 'repeat_sentence' && model.questionType !== 'write_from_dictation') {
      // setIsShowAnswerContent(true)
      // console.error('我触发了吗1')
      // onUpdateReply?.('start') 
    }
    const hasAudio = model?.content?.find(item => item.type === 'audio')?.type

    if (model.answerType === 'text' && hasAudio) {
      setTimeout(() => {
        setIsShowAnswerContent(true)
        if (onUpdateReply) {
          onUpdateReply('start')
        }
      }, 100)
    } 
  }, [model])

  const autoPlayAudio = () => {
    // if (hasPlayed) return  // If already played, don't play again
    // console.info('我在自动播放', model, hasPlayed)
    const hasAudio = model?.content?.find(item => item.type === 'audio')?.type
  
    if (hasAudio && model.tipInfo) {
      if (!model.tipInfo?.isShow) {
        Object.values(audioRefs.current).forEach(audio => {
          if (audio) {
            audio.play().catch(error => {
              console.info('Error playing audio:', error)
            })
          }
        })
        // setHasPlayed(true)  // Mark as played
      }
    } else if (examMediaInfo.isAutoplay) {
      Object.values(audioRefs.current).forEach(audio => {
        if (audio) {
          audio.play().catch(error => {
            console.info('Error playing audio:', error)
          })
        }
      })
      // setHasPlayed(true)  // Mark as played
    }
  }

  const onChangeVal = (e: any) => {
    // console.warn(e.target.value, '000e')
    setTextValue(e.target.value)
    onChangeText?.(e.target.value)
  }

  const renderProgressBar = (item: ContentItem) => {
    // console.error(mediaDuration, 'mediaDuration')
    if (!mediaDuration) return
    if (item.type === 'audio') {
      if (model.tipInfo) {
        if (!model.tipInfo.isShow) {
          return <ProgressBar duration={mediaDuration} key={model.title} />
        }

        return null
      }

      return <ProgressBar duration={mediaDuration} key={model.title} />
    }

    return null
  }

  const renderInlineInput = (item: ContentItem) => {
    const regex = /\{\{key\d+\}\}/g

    const parts = item.value.split(regex)

    const matches = item.value.match(regex) || []
    
    return (
      <div className="text-base leading-relaxed">
        {parts.map((part, index) => (
          <span key={index}>
            {part}
            {index < matches.length && (
              <input 
                type="text" 
                onChange={(e) => {
                  const key = matches[index].replace('{{', '').replace('}}', '')

                  const inputValue = e.target.value
                  
                  // Update selectValueRef with the key and value
                  if (!selectInputsRef.current) {
                    selectInputsRef.current = {}
                  }
                  selectInputsRef.current[key] = inputValue!
                  
                  // Call onChangeSelect to update the answer
                  // debugger
                  onChangeSelect?.(selectInputsRef.current as any, true)
                }}
                className="inline-block mx-1 px-1 py-1 bg-transparent outline-none min-w-[40px] max-w-[80px] border-b border-gray-200 focus:border-gray-400"
              />
            )}
          </span>
        ))}
      </div>
    )
  }

  const renderContents = () => {
    // console.warn(model.content, 'model.content')
    return Array.isArray(model.content) ?
      model.content.map((item: ContentItem, index: number) => {
        if (model.questionType === 'inline_input' && item.type === 'text') {
          return (
            <div key={index}>
              {renderInlineInput(item)}
            </div>
          )
        }
        if (item.type === 'video') {
          return (
            <div className='' key={item.value + index}>
              <video
                ref={(el: HTMLVideoElement | null) => {
                  if (el) {
                    videoRefs.current[item.value] = el
                  }
                }}
                playsInline
                className="max-w-full object-contain rounded-lg h-200px mx-auto"
                x5-playsinline={item.value}
                src={item.value}
                onLoadedMetadata={() => {
                  // console.warn(audioRefs.current[item.value], 'audioRefs.current[item.value]')
                  if (videoRefs.current[item.value]) {
                    setMediaDuration(Math.floor(videoRefs.current[item.value].duration))
                  }
                }}
                onEnded={() => {
                  // console.warn('播放完成x')
                  setMediaDuration(0)
                  playDingMusic()
                  setIsShowAnswerContent(true)
                  const time = (model.readTime! - mediaDuration)* 1000

                  setTimeout(() => {
                    // 需要判断回答类型是否是 voice
                    if (model.answerType === 'voice') {
                      onStartRecord?.()
                    } else {
                      onUpdateReply?.('start')
                    }
                  }, time < 0 ? 0 : time)
                }}
                onError={() => {
                  // console.warn('111', err)
                  setMediaDuration(0)
                  playDingMusic()
                }}
              />
              {
                (!model.tipInfo?.isShow && mediaDuration) ? <CutDownRight isRepay onTimeEnd={() => {
                  setTimeout(() => {
                    setIsShowAnswerContent(true)
                    if (onUpdateReply) {
                      onUpdateReply('start')
                    }
                    if (model.answerType === 'voice') {
                      onStartRecord?.()
                    }
                  }, 1000)
                }} count={mediaDuration} /> : null
              }
            </div>
          )
        }
        if (item.type === 'image') {
          return (
            <div key={item.value + index} className='relative flex items-center justify-center w-[100vw]  max-w-[500px] max-h-[500px] h-[100vw] mx-auto'>
              <img src={item.value} className='w-full h-full object-cover max-w-[500px] max-h-[500px] block' alt='' />
            </div>
          )
        }
        if (item.type === 'text') {
          return (
            <div key={item.value + index} className={`relative text-base leading-[28px] text-[#3d4d5c] font-bold ${index < model.content.length - 1 ? 'mb-[10px]' : ''}`}>
              {item.value}
            </div>
          )
        }
        if (item.type === 'audio' || item.type === 'voice') {
          return (
            <div key={item.value + index}>
              <div className='relative flex items-center justify-center text-base leading-[28px] text-[#3d4d5c] flex-col rounded-lg mt-[12px]'>
                <audio src={item.value} 
                  ref={(el: HTMLAudioElement | null) => {
                    if (el) {
                      audioRefs.current[item.value] = el
                    }
                  }} 
                  onClick={() => {
                    if (isMobile) {
                      autoPlayAudio()
                    }
                  }}
                  onLoadedMetadata={() => {
                  // console.error(audioRefs.current[item.value].duration, 'audioRefs.current[item.value]')
                    if (audioRefs.current[item.value]) {
                      setMediaDuration(Math.ceil(audioRefs.current[item.value].duration))
                    }
                  }}
                  onEnded={() => {
                    // console.error('音频播放完成，开始录音', model.readTime)
                    playDingMusic()
                    setMediaDuration(0)
                    setIsShowAnswerContent(true)
                    const time = (model.readTime! - mediaDuration)* 1000

                    setTimeout(() => {
                      // 需要判断回答类型是否是 voice
                      if (model.answerType === 'voice') {
                        onStartRecord?.()
                      } else {
                        onUpdateReply?.('start')
                      }
                    }, time < 0 ? 0 : time)
                  }}
                  onError={() => {
                    playDingMusic()
                    setMediaDuration(0)
                  }}
                />
                <div 
                  className={'w-[80px] h-[80px] bg-green-500 rounded-full flex items-center justify-center'} 
                >
                  <img src={'https://gusto-english-oss.wemore.com/exam/icon-listen-3%403x.png'} alt='' className="w-[35px] h-[35px]" />
                </div>
                <div  className={'w-[250px] mt-4 h-[40px]'} >
                  {renderProgressBar(item)}
                </div>

              </div>
              {
                (!model.tipInfo?.isShow && mediaDuration) ? <CutDownRight isRepay onTimeEnd={() => {
                  console.info('我会执行吗')
                  setTimeout(() => {
                    setIsShowAnswerContent(true)
                    if (onUpdateReply) {
                      onUpdateReply('start')
                    }
                    if (model.answerType === 'voice') {
                      onStartRecord?.()
                    }
                  }, 1000)
                }} count={mediaDuration} /> : null
              }
            </div>
          )
        }
        if (item.type === 'paragraph') {
          const selects = item.value as unknown as {type: string, value: string}[]

          return (
            <div key={index} className="rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex flex-wrap items-center gap-1 text-base leading-8 text-gray-700">
                {selects?.map((itm: any, idx: number) => {
                  if (itm.type === 'text') {
                    return (
                      <span key={`text-${idx}`} className="text-gray-800 font-normal text-base">
                        {itm.value}
                      </span>
                    )
                  }
                  if (itm.type === 'select') {
                    return (
                      <div key={`select-${idx}`} className="inline-block mx-1">
                        <SelectViewEx 
                          key={itm.type + idx}
                          title={itm.type + idx} 
                          options={[{key: '     ', value: ' '}]?.concat(itm.value?.map((item: any) => ({key: item, value: item})))} 
                          onSelect={(e) => {
                            selectValueRef.current[itm.key] = [e.value] as any
                            onChangeSelect?.(selectValueRef.current!)
                          }} />
                      </div>
                    )
                  }

                  return null
                })}
              </div>
            </div>
          )
        }
        if (item.type === 'option') {
          return (
            <div key={index} className="grid grid-cols-2 gap-4 mt-4 px-2">
              {Object.entries(item.options || {}).map(([key, option]) => (
                <div 
                  key={item.type + key} 
                  className={`flex flex-col items-center p-4 border-2 rounded-lg hover:border-blue-400 hover:shadow-md cursor-pointer transition-all duration-300 relative ${
                    selectedOption === item.type + key ? 
                      'border-blue-500 bg-blue-50 shadow-lg ring-2 ring-blue-200' : 
                      'border-gray-300 bg-white hover:bg-gray-50'
                  }`}
                  onClick={() => {
                    setSelectedOption(item.type + key)
                    handleOptionClick({
                      // FIXME: this not working
                      value: key,  // 使用选项的 key 作为答案
                      image: Array.isArray(option) ? option[0] : option.image || ''
                    })
                    selectValueRef.current[item.type + index] = [key]
                    onChangeSelect?.(selectValueRef.current!)
                  }}
                >
                  {selectedOption === item.type + key && (
                    <div className="absolute top-2 right-2 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center z-10">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                  <div className="w-full mb-2 flex justify-center flex-1">
                    {Array.isArray(option) ? (
                      typeof option[0] === 'string' && !option[0].startsWith('http') ? (
                        <div className="text-center p-2 min-h-[80px] flex items-center justify-center w-full">
                          <span className="text-sm font-medium text-gray-700">{option[0]}</span>
                        </div>
                      ) : (
                        <Image 
                          src={option[0]} 
                          alt={`Option ${index + 1}`}
                          width={160}
                          height={160}
                          className="object-contain rounded-lg shadow-sm"
                        />
                      )
                    ) : (
                      option.image ? (
                        <Image 
                          src={option.image} 
                          alt={`Option ${index + 1}`}
                          width={160}
                          height={160}
                          className="object-contain rounded-lg shadow-sm"
                        />
                      ) : (
                        <div className="text-center p-2 min-h-[80px] flex items-center justify-center w-full">
                          <span className="text-sm font-medium text-gray-700">{option.text || ''}</span>
                        </div>
                      )
                    )}
                  </div>
                </div>
              ))}
            </div>
          )
        }

        return null
      }) :
      null
  }

  const renderAnswerContents = () => {
    // console.warn(isShowAnswerContent, 'isShowAnswerContent')
    // 读题结束后 开始显示答题弹窗
    if (!isShowAnswerContent) return <div />
    if (model.answerType === 'text') {
      return (
        <div>
          <div className='relative'>
            <Textarea autoCorrect='off' autoComplete='off' key={model.title} placeholder="Type your message here.(在此输入答案。)" style={{height: 80, border: '2px solid #34D399'}} onChange={onChangeVal} />
            {textValue.length ? <span key={model.title + '2'} className='absolute bottom-[-24px] right-2 text-xs text-gray-500'>{countSegments(textValue)}</span> : <span />}
          </div>
          {mediaDuration === 0 && <CutDownRight onTimeEnd={() => {
            if (model.answerType === 'voice') {
              onStopRecord?.()
            }
            if (onUpdateReply) {
              onUpdateReply('end')
            }
            setIsShowAnswerContent(false)
          }} count={model.answerTime} />}
        </div>
      )
    }
    if (model.answerType === 'voice') {
      return (
        <div className="flex flex-col items-end mt-5">
          <div className='w-full'>
            <span className="text-lg font-semibold text-green-500 animate-pulse animate-bounce animate-[slide-horizontal_1s_ease-in-out_infinite]">
              Recording in progress...(录音中...)
            </span>
            <ProgressBar duration={model.answerTime} key={model.title} />
          </div>
          <Button
            className=" bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mt-[14px]"
            onClick={() => {
              onStopRecord?.()
              if (onUpdateReply) {
                // onUpdateReply('end')
              }
              setIsShowAnswerContent(false)
            }}
          >
            Next
          </Button>
          <CutDownRight onTimeEnd={() => {
            if (isMobile) {
              onStopRecord?.(isMobile)

              return
            }
            onStopRecord?.()
            setIsShowAnswerContent(false)
            if (onUpdateReply) {
              onUpdateReply('end')
            }
          }} count={model.answerTime} />
        </div>
      )
    }
    if (model.answerType === 'option' || model.answerType === 'input') {
      return (
        <div className="flex flex-col items-end mt-5">
          <CutDownRight onTimeEnd={() => {
            if (model.answerType === 'voice') {
              onStopRecord?.()
            }
            setIsShowAnswerContent(false)
            if (onUpdateReply) {
              onUpdateReply('end')
            }
          }} count={model.answerTime} />
        </div>
      )
    }

    return <div />
  }

  // 读题的时间
  const renderReadTime = () => {
    if (!isShowAnswerContent && !model.tipInfo?.isShow) {
      // console.warn(model, mediaDuration, '读题倒计时')
      const time = Math.max(model.readTime!, 1)

      const hasAudio = model.content?.find(item => item.type === 'audio')?.type

      const hasVideo = model.content?.find(item => item.type === 'video')?.type

      if (hasAudio || hasVideo) {
        return
      }

      return <CutDownRight isRepay onTimeEnd={() => {
        playDingMusic()
        setTimeout(() => {
          setIsShowAnswerContent(true)
          if (onUpdateReply) {
            onUpdateReply('start')
          }
          if (model.answerType === 'voice') {
            onStartRecord?.()
          }
        },1500)
      }} count={time} />
    }

    return <div />
  }

  return (
    <div className="w-full rounded-[8px] p-4 min-h-[250px] h-auto">
      <div className="flex flex-col gap-4">
        <div id='exam-content-box' className='flex-1 max-h-[calc(50vh)] overflow-y-auto scrollbar-none bg-[#f6f6f6] p-4 rounded-[4px]'>
          {renderContents()}
        </div>
        <div>
          {renderAnswerContents()}
        </div>
        {renderReadTime()}
      </div>
    </div>
  )
}

export default ExamContent
