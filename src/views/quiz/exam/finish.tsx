import {examInfo} from '@/global/hint'
import {useRef, useEffect, useState} from 'react'

const FinishView = () => {
  const videoRef = useRef<HTMLVideoElement>(null)

  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsMobile(/iPhone|iPad|iPod|Android/i.test(window.navigator.userAgent))
    }
  }, [])
  
  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.play().catch(error => {
        console.error('Error attempting to play the video:', error)
      })
    }
  }, [])
  useEffect(() => {
    if (videoRef.current && !isMobile) {
      videoRef.current.play().catch(error => {
        console.error('Error playing video:', error)
      })
    }
  }, [isMobile])

  return (
    <div className="w-full rounded-[8px] shadow-sm mt-[32px] mb-[32px] min-h-[250px] relative px-8">
      <video 
        ref={videoRef}
        {...(isMobile ? {} : {autoPlay: true})}
        src={examInfo.endVideoUrl}    
        muted={isMobile} // Prevent silent autoplay on iOS
        controls={isMobile} // Show native controls on mobile      
        playsInline
        preload="metadata"
        className="max-w-full object-contain rounded-lg"
        x5-playsinline={examInfo.endVideoUrl}>
      </video>
    </div> 
  )
}

export default  FinishView