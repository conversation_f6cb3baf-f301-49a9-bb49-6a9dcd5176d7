'use client'
/**
 * 导入所需的依赖和组件
 */
import {useEffect, useRef, useState} from 'react'
import styles from '../quiz-new/index.module.css'
import {Toaster} from '@/components/ui/toaster'
import ExamContentView from '../quiz-new/contents/exam.content'
import {getQuestionDetailApi} from '@/http/http'
import useMediaPermission from '@/hooks/useMediaPermission'
import {useVh} from '@/hooks/useVh'
import ErrHint from '@/components/business/ErrHint'
import useQuizStore from '@/store/quiz'

/**
 * 测验视图组件
 * @param props - 组件属性
 */
const QuestionNewView = ({questionId}: {questionId: string}) => {    
  // 考试状态管理
  const [loading, setLoading] = useState(true)

  const [currentQuestion, setCurrentQuestion] = useState({} as any)

  const {isRequestMediaPermission} = useMediaPermission({allowNext: true})

  const [showAnswer, setShowAnswer] = useState(false)

  const {currentAnswer,} = useQuizStore(state => state)

  const {setCurrentAnswer} = useQuizStore.getState()

  const examRef = useRef<any>(null)

  const [errHint, setErrHint] = useState({
    isOpen: false,
    icon: '',
    title: '',
    content: '',
    btnText: '',
    fn: () => {}
  })

  // 设置vh
  useVh()
  // 检查是否允许考试

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const VConsole = require('vconsole')

      if (window.location.href.includes('debug')) {
        new VConsole()
      }
    }
  }, [])

  useEffect(() => {
    getQuestionDetailApi(questionId).then(res => {
      console.log(res.data.data, 'res.data.data')
      const t = res.data.data as any

      const item = {
        ...t,
        tipInfo: {isShow: false, type: 'confirm', content: [{type: 'text', value: ''}]},
        question: t.question?.map((itm: { type: string }) => {
          return {...itm, type: itm.type === 'voice' ? 'audio' : itm.type}
        }), 
        content: t.question?.map((itm: { type: string }) => {
          return {...itm, type: itm.type === 'voice' ? 'audio' : itm.type}
        }), 
        type: t.question_type,
        questionType: t.question_type, 
        answerType: t.answer_type,
        readTime: t.read_duration, 
        answerTime: t.answer_duration,
        // tipInfo: {...(item.tip_info || {}), isShow: item?.tip_info?.is_show},
        id: t.question_id + '_mock',

      }

      if (item.question_type === 'fill_in_the_blanks' && item.answer_type === 'option') {
        const optionItem = item.question.find((ix: { type: string }) => ix.type === 'option')?.options

        const splitKey = optionItem ? Object.keys(optionItem).map(x => `{{${x}}}`) : []

        const splitParagraph = item.question.find((ix: { type: string }) => ix.type === 'paragraph')

        const splitParagraphValue = splitParagraph?.value || ''

        let temp1 = [] as any[]

        try {
          const splitParts = splitParagraphValue.split(new RegExp(splitKey.join('|'), 'g'))

          // console.log('uniqueParts', splitParts)
          splitParts.forEach((part: string, index: number) => {
            temp1.push({type: 'text', value: part.replace(/\n/g, '')}) 
            if (index < splitParts.length - 1) {
              temp1.push({type: 'select', value: optionItem?.[Object.keys(optionItem)?.[index]], key: Object.keys(optionItem)?.[index]})
            }
          })
        } catch (error) {
          console.info(error, 'error')
          temp1 = []
        }
        // console.log('temp', temp)
        item.question = [{type: 'paragraph', value: temp1}]
        item.content = [{type: 'paragraph', value: temp1}]

      }
      setCurrentQuestion(item)
      setLoading(false)
    })
  }, [questionId])

  /**
   * 处理文本题目答案变更
   * @param text - 答案文本
   * @param isInput - 是否为输入类型
   */
  const onChangeText = (text: string, isInput?: boolean) => {
    const ans = {question_id: currentQuestion.id, answer: [{type: isInput ? 'input' : 'text', value: text || ' '}]}

    setCurrentAnswer([ans])
  }

  /**
   * 处理选择题答案变更
   * @param s - 选择的选项
   * @param isInput - 是否为输入类型
   */
  const onChangeSelect = (s: {[key: string]: string[]}, isInput?: boolean) => {
    const ans = {question_id: currentQuestion.id, answer: isInput ? [{type: 'input', inputs: s}] : [{type: 'option', value: '', options: s}]} as any

    setCurrentAnswer([ans])
  }

  /**
   * 处理下一题
   */
  const handleNextQuestion = () => {
    // 判断当前题目是否已经回答
    // return
    console.info(currentAnswer, 'currentAnswer')
    setShowAnswer(true)
  }

  // 授权成功的微信移动端
  
  if (!isRequestMediaPermission || loading) return <div className={styles.page}><Toaster /></div>
  if (showAnswer) {
    return (
      <div className={styles.page}>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
              你的答案
            </h2>
            
            <div className="space-y-4">
              {currentAnswer.map((answerItem, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="text-sm text-gray-500 mb-3">
                    题目 {index + 1}
                  </div>
                  
                  {answerItem.answer.map((answer, answerIndex) => (
                    <div key={answerIndex} className="mb-3 last:mb-0">
                      {answer.type === 'voice' ? (
                        <div className="flex items-center justify-center">
                          <button
                            onClick={() => {
                              console.log('播放音频', answer.value)
                              if (answer.value && typeof answer.value === 'object' && 'type' in answer.value) {
                                const audioUrl = URL.createObjectURL(answer.value)

                                const audio = new Audio(audioUrl)

                                audio.play().catch(error => {
                                  console.error('播放音频失败:', error)
                                })
                              
                                // 播放结束后释放URL对象
                                audio.addEventListener('ended', () => {
                                  URL.revokeObjectURL(audioUrl)
                                })
                              } else {
                                console.log('播放音频', answer.value)
                              }
                            }}
                            className="flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                          >
                            <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                            </svg>
                            播放录音
                          </button>
                        </div>
                      ) : answer.type === 'text' ? (
                        <div className="bg-gray-50 rounded-lg p-4 border-l-4 border-blue-500">
                          <div className="text-sm text-gray-600 mb-2">文本答案:</div>
                          <div className="text-gray-800 font-medium">
                            {answer.value.toString()}
                          </div>
                        </div>
                      ) : answer.type === 'input' ? (
                        <div className="bg-gray-50 rounded-lg p-4 border-l-4 border-green-500">
                          <div className="text-sm text-gray-600 mb-2">输入答案:</div>
                          <div className="text-gray-800 font-medium">
                            {answer.value || '无内容'}
                          </div>
                        </div>
                      ) : (
                        <div className="bg-gray-50 rounded-lg p-4 border-l-4 border-purple-500">
                          <div className="text-sm text-gray-600 mb-2">其他类型答案:</div>
                          <pre className="whitespace-pre-wrap text-sm text-gray-800 bg-white p-3 rounded border">
                            {JSON.stringify(answer, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </div>
          <div className="flex justify-center mt-8 mb-4">
            <button
              onClick={() => {
                window.location.reload()
              }}
              className="flex items-center px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
            >
              <svg className="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
              </svg>
            重新开始
            </button>
          </div>
        </div>
      </div>
    )
  }

  console.info(currentAnswer, 'currentAnswer', currentQuestion)

  return (
    <div className={styles.page}>
      <audio src='https://gusto-english-oss.wemore.com/exam/ding.mp3' className={styles.audioHide} />
      <ExamContentView 
        ref={examRef}
        model={currentQuestion} 
        currentNumber={1} 
        totalNumber={1} 
        onNextQuestion={handleNextQuestion}
        onChangeText={onChangeText}
        currentAnswer={[]}
        onChangeSelect={onChangeSelect}
        isOnlyView={true}
        onMockData={() => {
          setTimeout(() => {
            console.info('mock data', currentAnswer)
            setShowAnswer(true)
          },1000)
        }}
      />

      <ErrHint 
        isOpen={errHint.isOpen} 
        onClose={() => {
          setErrHint({...errHint, isOpen: false})
          errHint.fn()
        }} 
        icon={errHint.icon} 
        title={errHint.title} 
        content={errHint.content} 
        btnText={errHint.btnText} 
      />
    </div>
  )
}

export default QuestionNewView