'use client'
import {Toaster} from '@/components/ui/toaster'
import {useEffect, useState} from 'react'
import ExamBtn from '../quiz/exam/btn'
import ExamContent from '../quiz/exam/content'
import ExamTitle from '../quiz/exam/title'
import {getQuestionDetailApi} from '@/http/http'

const QuestionView = ({questionId}: {questionId: string}) => {

  const [btnStatus, setBtnStatus] = useState('')

  const [loading, setLoading] = useState(true)

  const [currentQuestion, setCurrentQuestion] = useState({} as any)

  useEffect(() => {
    getQuestionDetailApi(questionId).then(res => {
      console.log(res.data.data, 'res.data.data')
      const t = res.data.data as any

      const item = {
        ...t,
        tipInfo: {isShow: false, type: 'confirm', content: [{type: 'text', value: ''}]},
        content: t.question?.map((itm: { type: string }) => {
          return {...itm, type: itm.type === 'voice' ? 'audio' : itm.type}
        }), 
        type: t.question_type,
        questionType: t.question_type, 
        answerType: t.answer_type,
        readTime: t.read_duration, 
        answerTime: t.answer_duration,
        // tipInfo: {...(item.tip_info || {}), isShow: item?.tip_info?.is_show},
        id: t.question_id,
      }

      if (item.question_type === 'fill_in_the_blanks' && item.answer_type === 'option') {
        const optionItem = item.question.find((ix: { type: string }) => ix.type === 'option')?.options

        const splitKey = optionItem ? Object.keys(optionItem).map(x => `{{${x}}}`) : []

        const splitParagraph = item.question.find((ix: { type: string }) => ix.type === 'paragraph')

        const splitParagraphValue = splitParagraph?.value || ''

        let temp1 = [] as any[]

        try {
          const splitParts = splitParagraphValue.split(new RegExp(splitKey.join('|'), 'g'))

          // console.log('uniqueParts', splitParts)
          splitParts.forEach((part: string, index: number) => {
            temp1.push({type: 'text', value: part.replace(/\n/g, '')}) 
            if (index < splitParts.length - 1) {
              temp1.push({type: 'select', value: optionItem?.[Object.keys(optionItem)?.[index]], key: Object.keys(optionItem)?.[index]})
            }
          })
        } catch (error) {
          console.info(error, 'error')
          temp1 = []
        }
        // console.log('temp', temp)
        item.content = [{type: 'paragraph', value: temp1}]
      }
      setCurrentQuestion(item)
      setLoading(false)
    })
  }, [questionId])

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const VConsole = require('vconsole')

      if (window.location.href.includes('debug')) {
        new VConsole()
      }
    }
  }, [])

  // 检查是否需要请求媒体权限

  const handleNext = () => {

  }

  const handleSubmit = async () => {

  }

  const onStartRecord = () => {
    setBtnStatus('start') 
  }

  const onStopRecord = async () => {
    setBtnStatus('end') 
  }

  const onChangeText = () => {

  }

  const onChangeSelect = () => {

  }

  const onUpdateReply = () => {
    setBtnStatus('start') 
  }

  if (loading) {
    return (
      <div className="w-screen h-screen flex flex-col items-center justify-center bg-green-100">
        <div className="relative w-20 h-20">
          <div className="absolute w-full h-full border-4 border-t-4 border-t-transparent border-green-800 rounded-full animate-spin"></div>
        </div>
        <div className="mt-4 text-lg font-semibold text-green-800 animate-pulse">资源加载中...</div>
        <Toaster />
      </div>
    )
  }

  return (
    <div className="w-screen h-screen flex flex-col items-center justify-center relative">
      <div className="w-full max-w-[800px] mx-auto pt-[32px] pb-[1px] mb-[10px] bg-white rounded-lg border border-e6e6e6 shadow-[0_0_15px_0_#cfcfcf] relative">
        <ExamTitle 
          title={currentQuestion.title} 
          isQuiz={true} 
          currentQuestion={1} 
          totalQuestions={1} 
        />
        
        <ExamContent
          key={1}
          model={currentQuestion} 
          examMediaInfo={{isAutoplay: true, isShow: true, type: ''} as any} 
          onStartRecord={onStartRecord} 
          onStopRecord={onStopRecord} 
          onChangeText={onChangeText}
          onChangeSelect={onChangeSelect}
          onUpdateReply={onUpdateReply}
        />
      </div>
      <div className="w-[750px]">
        <ExamBtn
          isLastQuestion={true}
          isSubmitting={false}
          showButton={(btnStatus === 'start' && currentQuestion.answerType !== 'voice') || currentQuestion.questionType === 'single_image_choice'}
          onNext={handleNext}
          onSubmit={handleSubmit}
        />
      </div>
      <Toaster />
    </div>
  )
}

export default QuestionView