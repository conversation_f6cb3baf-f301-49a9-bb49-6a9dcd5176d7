import {create} from 'zustand'

// 定义题目类型枚举
type QuestionType = 'text' | 'video' | 'image' | 'audio' | 'self_intro' | 'repeat_sentence' | 'write_from_dictation' | 'single_image_choice' | 'inline_input'// 
type AnswerType = 'text' | 'voice' | 'video' | 'option' | 'input' // 文本答案、语音答案、视频答案

// 设备信息接口定义
interface DeviceInfo {
  audioDeviceId: string
  videoDeviceId: string
}

// 题目对象接口定义
export interface Question {
  id: string
  title: string // 题目标题
  questionType: QuestionType // 题目类型
  content: {type: string; value: string}[] // 题目内容 可以拓展安需求来
  answerType: AnswerType // 答题类型
  answerTime: number // 回答题目的时间
  readTime?: number // 看题时间

  introduction?: string // 题目介绍

  // 提示类型
  tipInfo?: {
    isShow: boolean
    type: 'confirm',
    content: {type: string, value: string}[]
  }
}

// 答案对象接口定义
// export interface Answer {
//   questionId: number // 对应题目ID
//   content: string | string[] // 答案内容(支持单个或多个答案)
//   type: AnswerType // 答案类型
// }

//  FIX 暂时兼容老类型，当答案后面修改时需要处理
export interface Answer {
  question_id: string
  answer: {type: string, value: string} []
}

// Quiz存储接口定义
interface QuizStore {
  questionLists: Question[] // 题目列表
  currentQuestion: Question // 当前题目
  currentAnswer: Answer[] // 当前答案对象
  currentQuestionNumber: number // 当前题目序号
  totalNumber: number // 题目总数
  deviceInfo: DeviceInfo // 设备信息
  currentExamId: string
  // Store方法
  setQuestionLists: (questions: Question[]) => void // 设置题目列表
  setCurrentQuestion: (question: Question) => void // 设置当前题目
  setCurrentAnswer: (answer: Answer[]) => void // 设置当前答案
  setCurrentQuestionNumber: (number: number) => void // 设置当前题目序号
  setDeviceInfo: (info: DeviceInfo) => void // 设置设备信息
  updateAudioDeviceId: (id: string) => void // 更新音频设备ID
  updateVideoDeviceId: (id: string) => void // 更新视频设备ID

  updateCurrentExamId: (id: string) => void
}

// 创建Quiz存储
const useQuizStore = create<QuizStore>(set => ({
  // 初始状态
  questionLists: [],
  currentQuestion: {} as Question,
  currentAnswer: [],
  currentQuestionNumber: 1,
  totalNumber: 0,
  deviceInfo: {
    audioDeviceId: '',
    videoDeviceId: '',
  },
  currentExamId: '',
  // 更新方法
  setQuestionLists: (questions: Question[]) =>
    set({questionLists: questions, totalNumber: questions.length}),
  setCurrentQuestion: (question: Question) => set({currentQuestion: question}),
  setCurrentAnswer: (answer: Answer[]) => set({currentAnswer: answer}),
  setCurrentQuestionNumber: (number: number) => set({currentQuestionNumber: number}),
  setDeviceInfo: (info: DeviceInfo) => set({deviceInfo: info}),

  updateCurrentExamId: (id: string) => set({currentExamId: id}),
  updateAudioDeviceId: (id: string) =>
    set(state => ({deviceInfo: {...state.deviceInfo, audioDeviceId: id} })),
  updateVideoDeviceId: (id: string) =>
    set(state => ({deviceInfo: {...state.deviceInfo, videoDeviceId: id} })),
}))

export default useQuizStore
