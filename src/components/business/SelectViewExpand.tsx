import * as React from 'react'

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from './select'

interface SelectProps {
  title: string
  options: {key: string; value: string}[]
  onSelect?(item: {key: string; value: string}): void
}

export const SelectViewEx = (props: SelectProps) => {
  const [value, setValue] = React.useState(props.options?.[0]?.value)

  const renderItem = () => {
    return props.options?.map(item => {
      return (
        <SelectItem key={item.key} value={item.value} className="font-['GTVCS-Bold'] text-[18px]">
          {item.key}
        </SelectItem>
      )
    })
  }

  return (
    <Select
      value={value}
      defaultValue={props.options?.[0]?.value}
      onValueChange={f => {
        setValue(f)
        const item = props.options.find(x => x.value === f)

        props.onSelect?.(item!)
      }}
    >
      <SelectTrigger className={'w-[130px] inline-block relative top-[3px] left-[1px] right-[1px] bg-[#FFF] font-bold font-[\'GTVCS-Bold\']'}           style={{
        fontSize: '18px',
        color: value === props.options?.[0]?.value ? '#C1C1C1' : '#FF712F'
      }}>
        <SelectValue 
          placeholder={props.title}
          style={{
            fontSize: '20px',
            color: value === props.options?.[0]?.value ? '#C1C1C1' : '#FF712F'
          }}
          className="text-[20px]"
        />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>{renderItem()}</SelectGroup>
      </SelectContent>
    </Select>
  )
}
