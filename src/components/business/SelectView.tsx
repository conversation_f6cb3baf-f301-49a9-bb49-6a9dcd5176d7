import * as React from 'react'

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface SelectProps {
  title: string
  options: {key: string; value: string}[]
  onSelect?(item: {key: string; value: string}): void
}

export const SelectView = (props: SelectProps) => {
  const renderItem = () => {
    return props.options?.map(item => {
      return (
        <SelectItem key={item.key} value={item.value}>
          {item.key}
        </SelectItem>
      )
    })
  }

  return (
    <Select
      defaultValue={props.options?.[0]?.value}
      onValueChange={f => {
        // console.warn(f, 'f')
        const item = props.options.find(x => x.value === f)

        props.onSelect?.(item!)
      }}
    >
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder={props.title} />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>{renderItem()}</SelectGroup>
      </SelectContent>
    </Select>
  )
}
