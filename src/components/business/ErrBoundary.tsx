'use client'

import React from 'react'
import * as Sentry from '@sentry/nextjs'

export class ErrorBoundary extends React.Component<any, any> {
  constructor(props: any) {
    super(props)
    this.state = {hasError: false,}
  }

  async componentDidCatch(error: Error) {
    this.setState({hasError: true})
    Sentry.captureException(error)
    console.error(error)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gray-50 py-8 px-4 md:px-8 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              页面出错了
            </h1>
            <p className="text-gray-600 mb-6">
              抱歉，页面加载过程中发生了错误
            </p>
            <button 
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              onClick={() => window.location.reload()}
            >
              刷新页面
            </button>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}
