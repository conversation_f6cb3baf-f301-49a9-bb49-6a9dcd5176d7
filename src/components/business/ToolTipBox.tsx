import {
  AlertDialog, AlertDialogAction, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle
} from '@/components/ui/alert-dialog'

interface ToolTipBoxProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  content: {type: string, value: string}[];
}

const ToolTipBox = ({
  isOpen, onClose, title, content
}: ToolTipBoxProps) => {

  // const audioRef = useRef<HTMLAudioElement | null>(null)

  // const handlePlayAudio = (audioElement: HTMLAudioElement | null) => {
  //   if (audioElement) {
  //     audioElement.play()
  //   }
  // }

  const renderContent = () => {
    return content?.map((item, index) => {
      if (item.type === 'text') {
        return (
          <div key={index} className='relative flex items-center text-base leading-[28px] text-[#3d4d5c] mb-[10px]'>
            {item.value}
          </div>
        )
      }
      // if (item.type === 'voice') {
      //   return (
      //     <div key={index} className='relative flex items-center text-base leading-[28px] text-[#3d4d5c] flex-row rounded-lg mt-[12px]'>
      //       <div 
      //         className="flex items-center justify-center bg-green-500 rounded-full w-[48px] h-[48px] mt-[5px]"
      //         onClick={() => handlePlayAudio(audioRef.current)}
      //       >
      //         <img src="images/<EMAIL>" alt="Listen Icon" className="w-6 h-6" style={{filter: 'hue-rotate(90deg)'}} />
      //         <audio ref={audioRef} src={item.value}  />
      //       </div>

      //       <div className="ml-2.5 text-sm">
      //         点击可以播放提示录音
      //       </div>
      //     </div>
      //   )
      // }
      return null
    })
  }

  return (
    <AlertDialog open={isOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          {renderContent()}
        </AlertDialogHeader>

        <AlertDialogFooter>
          <AlertDialogAction className='bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mt-[14px]' onClick={onClose}>Next</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export default ToolTipBox
