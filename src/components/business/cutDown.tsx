import useCutDown from '@/hooks/useCutDown'
import {useEffect} from 'react'

interface Props {
    onTimeEnd(): void
    count?: number
    isRepay?: boolean
    onTimeChange?: (count: number) => void
}

const CutDown = (props: Props) => {
  const {count, start} = useCutDown(props.count || 3)

  useEffect(() => {
    start()
  }, []) 
  
  useEffect(() => {
    if (count === 0) {
      props.onTimeEnd()
    }
  }, [count])

  return <div className='absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center text-white text-5xl'>{count}</div>
}

export const CutDownRight = (props: Props) => {
  const {count, start} = useCutDown(props.count!)

  useEffect(() => {
    start()
  }, []) 

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60).toString().padStart(2, '0')

    const remainingSeconds = seconds % 60

    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`
  }
    
  useEffect(() => {
    if (count === 0) {
      props.onTimeEnd()
    }
    props.onTimeChange?.(count)
  }, [count])
  
  return <div className='absolute top-[0px] right-4 flex items-center justify-center text-green-500 font-bold text-[32px] leading-[44px] w-[100px]'>
    {formatTime(count)}
  </div>
}

export default CutDown