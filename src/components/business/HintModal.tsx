'use client'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import {useToast} from '@/hooks/use-toast'
import {requestMediaDevices} from '@/utils/permission'
import {useEffect, useRef, useState} from 'react'
import {SelectView} from './SelectView'
import useQuizStore from '@/store/quiz'
import {examInfo} from '@/global/hint'
interface HintModalProps {
  isAllowClick?: boolean
  onClose: () => void
}
const HintModal = (props: HintModalProps) => {
  const {toast} = useToast()

  const videoRefs = useRef<HTMLVideoElement>()

  const [open, setOpen] = useState(true)

  const [showBtn, setShowBtn] = useState(false)

  const {updateAudioDeviceId, updateVideoDeviceId} = useQuizStore.getState()

  const [devicesInfo, setDeviceInfo] = useState<any>({})

  const [requestMedia, setRequestMedia] = useState(false)

  // 查询当前设备信息
  useEffect(() => {
    requestMediaDevices()?.then(r => {
      setDeviceInfo(r)
      console.warn(r, 'rrr')
      // 默认选中音频第一个
      const firstAudio = r?.audioDevices?.[0]?.value

      updateAudioDeviceId(firstAudio!)
      setRequestMedia(true)
    })
  }, [])

  useEffect(() => {
    setTimeout(() => {
      setShowBtn(true)
    }, 3000)
  }, [])  

  useEffect(() => {
    if (videoRefs.current) {
      setTimeout(() => {
        if (videoRefs.current) {
          // 判断非微信执行
          const isWeChat = /MicroMessenger/i.test(navigator.userAgent)

          if (!isWeChat) {
            videoRefs.current?.play().catch(error => {
              videoRefs.current?.play()
              console.error('Error playing video:', error)
              if (error.name === 'NotAllowedError') {
                setShowBtn(true)
                console.warn('The request is not allowed by the user agent or the platform in the current context, possibly because the user denied permission.')
              }
            })
          }
          setShowBtn(true)
          console.warn(videoRefs.current, 'videoRefs')
        }
      }, 3000)
    }
  }, [videoRefs, requestMedia])

  const closeModal = () => {
    if (!props.isAllowClick) {
      toast({
        title: 'Scheduled: Catch up',
        description: 'Friday, February 10, 2023 at 5:57 PM',
      })

      return
    }
    setOpen(false)
    props.onClose()
  }

  // 确定设备使用那个 音视频源
  const onSelect = (t: string) => (item: {key: string; value: string}) => {
    // 切换输出源
    if (t === '视频') {
      updateVideoDeviceId(item.value)
    } else {
      updateAudioDeviceId(item.value)
    }
  }

  const showMediaInfo = (type: string, data: any[]) => {
    if (!data?.length) return

    return (
      <div className="flex items-center">
        <AlertDialogDescription className="mr-2">当前{type}</AlertDialogDescription>{' '}
        {renderOptions(data, type)}
      </div>
    )
  }

  const renderOptions = (data: any[], type: string) => {
    return <SelectView title={'请选择' + type} options={data} onSelect={onSelect(type)} />
  }

  const isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)

  const videoProps = isSafari ? {controls: true} : {}

  //

  return (
    <AlertDialog open={open}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>这里是 Gusto English 水平评估</AlertDialogTitle>
          <AlertDialogDescription>
            <video 
              src={examInfo.startVideoUrl} 
              controls
              autoPlay={!/MicroMessenger/i.test(navigator.userAgent)}
              {...videoProps}
              style={{
                borderRadius: '8px', width: '100%', height: '100%', maxHeight: '200px', objectFit: 'contain', margin: '0 auto'
              }} 
              playsInline
              x5-playsinline={'start.mov'} 
              onEnded={() => setShowBtn(true)} 
              ref={(el: HTMLVideoElement | null) => {
                if (el) {
                  videoRefs.current = el
                }
              }}
              onError={() => {
                console.error('Video playback error: No sound')
                setShowBtn(true)
              }}
            />
          </AlertDialogDescription>
          <AlertDialogDescription>
          本次评估共15～30个问题, 需要您录制音频或用文本回答
          </AlertDialogDescription>
          <AlertDialogDescription>总共用时15～20分钟</AlertDialogDescription>
          <AlertDialogDescription>
           录音录像资料仅供助教进行水平评估使用, 不涉及隐私泄露
          </AlertDialogDescription>
          <AlertDialogDescription>
          为了确保考试正常进行，请您关闭其他正在播放的音频
          </AlertDialogDescription>
          
          {showMediaInfo('视频', devicesInfo?.videoDevices)}
          {showMediaInfo('音频', devicesInfo?.audioDevices)}
          <AlertDialogDescription className="relative bg-gradient-to-r from-amber-50 to-orange-50 border-l-4 border-amber-500 p-4 rounded-lg shadow-sm">
            <div className="flex items-center space-x-2">
              <span className="text-2xl animate-bounce">🎧</span>
              <div>
                <div className="text-amber-800 font-semibold text-base">
                  ⚠️ 重要提示
                </div>
                <div className="text-amber-700 text-sm mt-1">
                  为保证声音效果, 请使用耳机
                </div>
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter>
          {showBtn && <AlertDialogAction onClick={closeModal} className='bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mt-[14px]'>Next</AlertDialogAction>}
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export default HintModal
