import useQuizStore from '@/store/quiz'
import {Button} from '../ui/button'

import useGetAudio from '@/hooks/useGetAudio'
import {useState, useRef, useEffect} from 'react'

const CheckContent = ({onNext}: { onNext: () => void }) => {  // Add onNext as a prop
  const [countdown, setCountdown] = useState(0)

  const [playCountdown, setPlayCountdown] = useState(0) // Define a play countdown

  const [isRecording, setIsRecording] = useState(false)

  const {deviceInfo} = useQuizStore(state => state)

  const {startRecording, stopRecording, audioUrl} = useGetAudio()

  const audioRef = useRef<HTMLAudioElement | null>(null)

  useEffect(() => {
    checkAudioPermission()
    checkPlaybackPermission()
  }, []) // 空依赖数组表示只在组件挂载时执行一次

  const [hasAudioPermission, setHasAudioPermission] = useState(false)

  const [hasPlaybackPermission, setPlaybackPermission] = useState(false)

  const checkAudioPermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({audio: true})

      stream.getTracks().forEach(track => track.stop())
      setHasAudioPermission(true)
    } catch {
      setHasAudioPermission(false)
    }
  }
  
  const checkPlaybackPermission = async () => {
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

      const oscillator = audioContext.createOscillator()

      oscillator.connect(audioContext.destination)
      // 只创建不播放，检查是否能够成功创建音频上下文
      await audioContext.resume()
      oscillator.disconnect()
      await audioContext.close()
      setPlaybackPermission(true)
    } catch (error) {
      console.error('Playback permission error:', error)
      setPlaybackPermission(false)
    }
  }

  const handleStartRecording = () => {
    try {
      if (countdown) return
      if (playCountdown) return
      if (isRecording) {
        startPlay()

        return
      }
      setCountdown(10)
      startRecording(deviceInfo.audioDeviceId)
      const interval = setInterval(() => {
        setCountdown(prev => {
          if (prev === 1 || prev <= 0) {
            clearInterval(interval)
            setIsRecording(true)
            stopRecording()

            return 0
          }

          return prev - 1
        })
      }, 1000)
    } catch (error) {
      console.error('Error starting recording:', error)
    }
  }

  const startPlay = () => {
    console.log('Playing the recorded audio...')
    try {
      if (audioUrl) {
        const audio = new Audio(audioUrl)

        audioRef.current = audio
        audio.play()
        setPlayCountdown(10)
        const playInterval = setInterval(() => {
          setPlayCountdown(prev => {
            if (prev === 1 || prev <= 0) {
              clearInterval(playInterval)

              return 0
            }

            return prev - 1
          })
        }, 1000)
      } else {
        console.warn('No audio URL available to play.')
      }
    } catch (error) {
      console.error('Error playing audio:', error)
    }
  }

  const handleNext = () => {
    try {
      stopRecording()
      if (audioRef.current) {
        audioRef.current.pause()
        audioRef.current.currentTime = 0
      }
    } catch (error) {
      console.error('Failed to stop recording:', error)
    }
    onNext()
  }

  return (  
    <div className="w-screen h-screen flex flex-col items-center justify-center relative">
      <div className="w-full max-w-[600px] mx-auto bg-white rounded-lg border border-e6e6e6 shadow-[0_0_15px_0_#cfcfcf] relative px-4">
        <div className="w-full rounded-[8px] p-4 min-h-[250px]">
          <div>
            <h2 className="text-xl md:text-2xl font-bold mb-4">设备检测</h2>
            <p className="mb-4">请确保您的设备正常工作，以便顺利完成考试。</p>
            <div className="flex gap-4 justify-center">
              <div className="bg-blue-100 p-3 rounded w-full max-w-[100%] md:max-w-[320px] mx-auto mb-4">
                <div className="flex">
                  <div 
                    className={`flex items-center justify-center ${hasAudioPermission ? 'bg-green-500' : 'bg-red-500'} rounded-full m-2`} 
                    style={{width: '48px', height: '48px'}}
                  >
                    <img 
                      src="https://gusto-english-oss.wemore.com/exam/icon-listen-3%403x.png" 
                      alt="Microphone Icon" 
                      className="w-6 h-6" 
                      style={{filter: 'hue-rotate(90deg)'}} 
                    />
                  </div>
                  <div>
                    <p className="mb-2 text-gray-600 text-sm">麦克风权限检查</p>
                    {!hasAudioPermission ? (
                      <Button 
                        className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                        onClick={checkAudioPermission}
                      >
                      申请麦克风权限
                      </Button>
                    ) : (
                      <Button 
                        className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                        onClick={handleStartRecording}
                        disabled={countdown > 0}
                      >
                        {isRecording ? (playCountdown ? `播放倒计时... ${playCountdown} 秒` : '播放录音') : '麦克风权限已授权'}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
              <div className="bg-blue-100 p-3 rounded w-full max-w-[100%] md:max-w-[320px] mx-auto mb-4">
                <div className="flex">
                  <div 
                    className={`flex items-center justify-center ${hasPlaybackPermission ? 'bg-green-500' : 'bg-red-500'} rounded-full m-2`} 
                    style={{width: '48px', height: '48px'}}
                  >
                    <img 
                      src="https://gusto-english-oss.wemore.com/exam/icon-listen-3%403x.png" 
                      alt="Speaker Icon" 
                      className="w-6 h-6" 
                      style={{filter: 'hue-rotate(90deg)'}} 
                    />
                  </div>
                  <div>
                    <p className="mb-2 text-gray-600 text-sm">播放权限检查</p>
                    {!hasPlaybackPermission ? (
                      <Button 
                        className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                        onClick={checkPlaybackPermission}
                      >
                      申请播放权限
                      </Button>
                    ) : (
                      <Button 
                        className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                      >
                      播放权限已授权
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Button 
          className="float-right bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mt-4 mr-4 mb-4"
          onClick={handleNext}
        >
          Next
        </Button>
      </div>
    </div>
  )
}

export default CheckContent
