'use client'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'

interface ErrHintProps {
  isOpen: boolean
  onClose: () => void
  icon?: string
  title?: string
  content?: string
  btnText?: string
}

const ErrHint = ({
  isOpen, onClose, icon, title, content, btnText = '重试'
}: ErrHintProps) => {
  return (
    <AlertDialog open={isOpen}>
      <AlertDialogContent style={{width: 'calc(100vw - 32px)', borderRadius: '16px', height: '270px'}}>
        <AlertDialogHeader style={{fontSize: '100px', position: 'relative', top: '-100px'}}>
          <div className="flex justify-center items-center"> {icon}</div>
          <AlertDialogTitle style={{
            color: '#030303',
            fontFamily: 'GTVCS-Bold',
            fontSize: '22px',
            fontStyle: 'normal',
            fontWeight: 700,
            lineHeight: '27.5px',
            textAlign: 'center'
          }}>{title || '录制失败'}</AlertDialogTitle>
          <AlertDialogDescription style={{
            color: '#030303',
            fontFamily: 'GTVCS-Bold', 
            fontSize: '14px',
            fontStyle: 'normal',
            fontWeight: 700,
            lineHeight: '15.714px',
            marginTop: '10px',
            marginBottom: '45px',
            textAlign: 'center'
          }}>
            {content || '点击重新按钮后可以继续测试'}
          </AlertDialogDescription>
          <AlertDialogAction 
            onClick={onClose}
            className="flex w-[200px] h-[60px] px-[126.5px] py-[18.5px] justify-center items-center gap-[5px] flex-shrink-0 m-[auto] rounded-[17px] bg-[#FF8E39] text-white"
          >
            {btnText}
          </AlertDialogAction>
        </AlertDialogHeader>
        {/* <AlertDialogFooter> */}
        {/* </AlertDialogFooter> */}
      </AlertDialogContent>
    </AlertDialog>
  )
}

export default ErrHint
