import React, {useEffect, useState} from 'react'

interface ProgressBarProps {
  duration: number;
}

const ProgressBar = ({duration}: ProgressBarProps) => {

  const [width, setWidth] = useState('0')

  useEffect(() => {
    setTimeout(() => {
      setWidth('100%')
    }, 200)
  }, [duration])

  // 动态设置 CSS 动画时长
  const progressStyle = {
    width: width,
    transition: `width ${duration}s linear` // 动态控制动画时长
  }

  // console.warn(duration, 'duration')
  return (
    <div className="bg-gray-200 w-full h-4 rounded-lg overflow-hidden">
      <div className="bg-green-500 h-4 rounded-lg" style={progressStyle}></div>
    </div>
  )
}

export default ProgressBar