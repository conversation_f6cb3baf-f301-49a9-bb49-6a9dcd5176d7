import Lottie from 'lottie-react'
import {useState, useEffect} from 'react'
import {animations, AnimationName} from './animation'
 
interface Props {
  size?: number
  name: AnimationName
  styles?: React.CSSProperties
  loop?: boolean // Add loop prop
}
 
const LottieAnimation: React.FC<Props> = ({
  size = 120, name, styles, loop = false
}) => {
  const [animationData, setAnimationData] = useState<any>(null)

  const [loading, setLoading] = useState(true)
 
  useEffect(() => {
    const loadAnimation = async () => {
      try {
        setLoading(true)
        const module2 = await animations[name]()

        setAnimationData(module2.default)
      } catch (error) {
        console.error('Failed to load animation:', error)
      } finally {
        setLoading(false)
      }
    }
 
    loadAnimation()
  }, [name])
 
  if (loading || !animationData) {
    return <div style={{width: size, height: size}}></div>
  }
 
  return (
    <div style={{
      width: size, height: size, margin: '0 auto', ...styles
    }}>
      <Lottie animationData={animationData} loop={loop} autoplay={true} />
    </div>
  )
}
 
export default LottieAnimation