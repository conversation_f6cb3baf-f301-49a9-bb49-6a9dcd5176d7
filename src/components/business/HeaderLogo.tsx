import Image from 'next/image'

interface Props {
  className?: string
}

const HeaderLogo = (props: Props) => {
  return (
    <div 
      className={`${props.className} fixed top-[30px] left-[30px] flex items-center gap-[10px] cursor-pointer z-9999`} 
      onClick={() => {
        // window.location.href = '/'
      }}
    >
      <Image
        src="/new/appicon-gusto-test.png"
        alt="Gusto English"
        width={45}
        height={45}
        priority
      />
      <div className="flex flex-col gap-[2px] justify-center">
        <p className="font-['GTVCS-Black'] font-black text-[15.9px] leading-[100%] tracking-[0px] text-white">
          Gusto English
        </p>
        <p className="font-['GTVCS-Black'] font-black text-[13.8px] leading-[100%] tracking-[0px] text-white mt-[2px]">
          高拓英语水平测试
        </p>
      </div>
    </div>
  )
}

export default HeaderLogo
