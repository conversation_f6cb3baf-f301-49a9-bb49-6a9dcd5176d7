import {isProd} from '@/global/consts'

export const readAloudContent = ({len}: {len: number}) => [
  {
    type: 'text',
    value: 'Part'
  },
  {
    type: 'text',
    value: `请在规定时间内读出屏幕上出现的句子，本题型共${len || 10}句。`
  },
  {
    type: 'text',
    value: `Please read aloud the sentences that appear on the screen within the specified time. There ${len > 1 ? 'are' : 'is'} a total of ${len} sentence${len > 1 ? 's' : ''} in this section.`
  },
]
export const repeatSentenceContent = ({len}: {len: number}) => [
  {
    type: 'text',
    value: 'Part'
  },
  {
    type: 'text',
    value: `请在规定时间内用语音复述听到的内容，每题录音仅播放一次，本题型共${len}题。如果需要可以用纸笔打草稿。 `
  },
  {
    type: 'text',
    value: `Please repeat aloud the content you hear within the specified time. Each recording will be played only once. There ${len > 1 ? 'are' : 'is'} a total of ${len} question${len > 1 ? 's' : ''} in this section.`
  }
]

export const describeImageContent = ({len}: {len: number}) => [
  {
    type: 'text',
    value: 'Part'
  },
  {
    type: 'text',
    value: '请在规定时间内用语音描述图片的内容，每题有15秒的时间准备。 '
  },
  {
    type: 'text',
    value: `Please describe the image within the given time. Each recording will be played only once. There ${len > 1 ? 'are' : 'is'} ${len} question${len > 1 ? 's' : ''} in this section.`
  }
]

export const writeFromDictationContent = ({len}: {len: number}) => [
  {
    type: 'text',
    value: 'Part'
  },
  {
    type: 'text',
    value: `请在规定时间内用文字复述你所听到的内容，每题录音仅播放一次，本题型共${len}题。如果需要可以用纸笔打草稿。`
  },
  {
    type: 'text',
    value: `Please summarize in writing what you hear within the given time. Each recording will be played only once. There ${len > 1 ? 'are' : 'is'} ${len} question${len > 1 ? 's' : ''} in this section.`
  }
]

export const summarizeWrittenTextContent = ({len}: {len: number}) => [
  {
    type: 'text',
    value: 'Part'
  },
  {
    type: 'text',
    value: '请在规定时间内用50个词左右概括屏幕上出现的文本。'
  },
  {
    type: 'text',
    value: `Please summarize in writing what you hear within the given time. Each recording will be played only once. There ${len > 1 ? 'are' : 'is'} ${len} question${len > 1 ? 's' : ''} in this section.`
  }
]

export const fillInBlankContent = ({len}: {len: number}) => [
  {
    type: 'text',
    value: 'Part'
  },
  {
    type: 'text',
    value: `请在规定时间内阅读${len}篇文章并选择合适的单词填空。`
  },
  {
    type: 'text',
    value: `Read the articles within the specified time and choose the appropriate words. There ${len > 1 ? 'are' : 'is'} ${len} paragraph${len > 1 ? 's' : ''}.`
  }
]

export const qaContent = ({len}: {len: number}) => [
  {
    type: 'text',
    value: 'Part'
  },
  {
    type: 'text',
    value: `请在规定时间内回答视频里出现的问题, 本题共${len}题`
  },
  {
    type: 'text',
    value: `Please answer the question${len > 1 ? 's' : ''} in the video, there ${len > 1 ? 'are' : 'is'} ${len} question${len > 1 ? 's' : ''} in this section(s)`
  }
]

export const examInfo = {
  startVideoUrl: isProd ?
    'https://gusto-english-oss.wemore.com/exam/intro.mp4' :
    'https://gusto-english-oss.wemore.com/exam/intro-dev.mp4',
  endVideoUrl: 'https://gusto-english-oss.wemore.com/exam/outro.mp4'
}