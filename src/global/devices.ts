// 兼容设备相关
 
export const BrowserDetector = () => {
  const isSafari = () => {
    const userAgent = window.navigator.userAgent.toLowerCase()

    console.warn(userAgent,'userAgent')

    return /safari/.test(userAgent) && !/chrome/.test(userAgent)
  }

  const isChrome = () => {
    const userAgent = window.navigator.userAgent.toLowerCase()

    return /chrome/.test(userAgent)
  }

  const isWechat = () => {
    const userAgent = window.navigator.userAgent.toLowerCase()

    return /micromessenger/.test(userAgent)
  }

  const  isWeCom = () => {
    const userAgent = navigator.userAgent.toLowerCase()

    return userAgent.includes('wxwork') || isWechat()
  }

  const isMobile = () => {
    const userAgent = window.navigator.userAgent.toLowerCase()

    const isMobile =
        /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)

    return isMobile
  }

  return {
    isChrome, isMobile, isSafari, isWechat, isQywx: isWeCom
  }
}
