export const playDingMusic = (fn?: () => void) => {
  const defaultUrl = 'https://gusto-english-oss.wemore.com/exam/ding.mp3'

  const audioUrl = defaultUrl

  const audio = new Audio(audioUrl)

  // 尝试设置音频输出设备为扬声器
  audio?.play().catch(error => {
    console.error('Error playing audio:', error)
  })
  setTimeout(() => {
    audio.pause()
    audio.currentTime = 0
    fn?.()
  }, 1000)
}

export const countSegments = (str: string) => {
  try {
    if (typeof str !== 'string') return 0
    if (!str.length) return 0
    let status = 0
   
    const s = str + ' '
   
    let count = 0
  
    const len= s.length
  
    for(let i=0;i<len;i++){
      if(s[i]!=' '){
        if(s[i+1]!=' ') i+=1
        status=1
      }else{ 
        if(status==1) count+=1
        status=0
      }
    }
  
    return count 
  } catch (e) {
    console.error(e)

    return 0
  }
}