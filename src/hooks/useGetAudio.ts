import {useState, useRef} from 'react'

const useGetAudio = () => {
  const [audioUrl, setAudioUrl] = useState<string | null>(null)

  const mediaRecorderRef = useRef<MediaRecorder | null>(null)

  const startRecording = async (deviceId: string) => {
    console.log('🎤 开始录音流程...')
    try {
      console.log('📱 请求音频设备权限，设备ID:', deviceId)
      const stream = await navigator.mediaDevices?.getUserMedia?.({audio: {deviceId} })

      if (stream) {
        console.log('✅ 音频流获取成功，轨道数量:', stream.getTracks().length)
        const mediaRecorder = new MediaRecorder(stream)

        mediaRecorderRef.current = mediaRecorder
        mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            console.log('📦 音频数据可用，大小:', event.data.size, 'bytes')
            const audioBlob = new Blob([event.data], {type: 'audio/mp3'})

            const url = URL.createObjectURL(audioBlob)

            console.log('🔗 音频URL创建成功:', url)

            setAudioUrl(url)
          } else {
            console.warn('⚠️ 音频数据为空')
          }
        }
        mediaRecorder.start()
        console.log('▶️ 录音器开始录制')
      } else {
        console.error('❌ 无法获取音频流')
      }
    } catch (error) {
      console.error('❌ 获取音频流失败:', error)
    }
  }

  const stopRecording = () => {
    console.log('⏹️ 停止录音...')
    try {
      if (mediaRecorderRef.current) {
        mediaRecorderRef.current?.stop?.()
        console.log('✅ 录音器已停止')
      } else {
        console.warn('⚠️ 没有正在进行的录音')
      }
    } catch (error) {
      console.error('❌ 停止录音失败:', error)
    }
  }

  return {audioUrl, startRecording, stopRecording}
}

export default useGetAudio
