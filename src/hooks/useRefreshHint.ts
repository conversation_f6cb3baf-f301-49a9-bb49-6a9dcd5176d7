import {useState, useEffect} from 'react'

const useRefreshHint = () => {
  const [showHint, setShowHint] = useState(false)

  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      event.preventDefault()
      event.returnValue = ''

      return true
      setShowHint(true)
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [])

  const handleRefresh = () => {
    setShowHint(false)
    window.location.reload()
  }

  const handleCancel = () => {
    setShowHint(false)
  }

  return {showHint, handleRefresh, handleCancel}
}

export default useRefreshHint
