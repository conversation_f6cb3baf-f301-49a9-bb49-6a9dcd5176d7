import {useState, useRef} from 'react'
import useRecorder from './useRecorder'

interface UseGetWxAudioReturn {
  audioUrl: string | null
  startRecording: () => Promise<void>
  stopRecording: () => Promise<void>
  resetAudio: () => void
}

const useGetWxAudio = (): UseGetWxAudioReturn => {
  const [audioUrl, setAudioUrl] = useState<string | null>(null)

  const [isRecording, setIsRecording] = useState(false)

  const mediaStream = useRef<MediaStream | null>(null)

  const audioContextRef = useRef<AudioContext | null>(null)

  const recRef = useRef<any>(null)

  useRecorder()

  // 清理资源的函数
  const cleanup = () => {
    console.log('🧹 清理资源...')
    if (mediaStream.current) {
      mediaStream.current.getTracks().forEach(track => track.stop())
      mediaStream.current = null
    }
    if (audioContextRef.current) {
      audioContextRef.current.close()
      audioContextRef.current = null
    }
    recRef.current = null
  }

  const startRecording = async () => {    
    if (isRecording) {
      console.warn('⚠️ 录音已在进行中，忽略重复请求')

      return
    }

    try {
      resetAudio()

      console.log('📱 请求用户媒体设备权限...')
      const stream = await navigator.mediaDevices?.getUserMedia?.({audio: true})

      if (!stream) {
        throw new Error('无法获取媒体流')
      }

      console.log('✅ 媒体流获取成功，轨道数量:', stream.getTracks().length)
      mediaStream.current = stream
      
      console.log('🎵 创建音频上下文...')

      audioContextRef.current = new AudioContext()
      const input = audioContextRef.current.createMediaStreamSource(stream)

      recRef.current = new window.Recorder(input, {numChannels: 1})
      
      recRef.current.record()
      console.info('开始录音...')
      setIsRecording(true)
      
    } catch (error) {
      console.error('❌ 开始录音失败:', error)
      cleanup()
      throw error
    }
  }

  const stopRecording = async () => {
    console.log('⏹️ 停止录音流程...')
    
    // if (!recRef.current || !isRecording) {
    //   console.warn('⚠️ 没有正在进行的录音')

    //   return
    // }
    
    return new Promise<void>(async (resolve, reject) => {
      try {
        console.log('🛑 停止录音器...')
        recRef.current.stop()
        setIsRecording(false)
        
        console.log('🛑 停止媒体流...')
        mediaStream.current?.getTracks().forEach(track => track.stop())
        
        console.log('📦 导出音频文件...')
        
        // 将 exportWAV 包装为 Promise
        const blob = await new Promise<Blob>((resolveBlob, rejectBlob) => {
          try {
            recRef.current.exportWAV((blob: Blob) => {
              if (blob && blob.size > 0) {
                console.log('📁 音频文件导出成功，大小:', blob.size, 'bytes')
                resolveBlob(blob)
              } else {
                rejectBlob(new Error('导出的音频文件无效'))
              }
            })
          } catch (error) {
            rejectBlob(error)
          }
        })
        
        // 清理之前的 URL
        if (audioUrl) {
          URL.revokeObjectURL(audioUrl)
        }
        
        const url = URL.createObjectURL(blob)

        setAudioUrl(url)
        
        console.log('✅ 录音停止流程完成')
        cleanup()
        resolve()
        
      } catch (error) {
        console.error('❌ 停止录音失败:', error)
        setIsRecording(false)
        cleanup()
        reject(error)
      }
    })
  }

  const resetAudio = () => {
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl)
      setAudioUrl(null)
    }
  }

  return {
    audioUrl, 
    startRecording, 
    stopRecording,
    resetAudio
  }
}

export default useGetWxAudio