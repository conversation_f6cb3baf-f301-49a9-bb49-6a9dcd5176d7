import {useRef, useState} from 'react'
import useQuizStore from '@/store/quiz'
import {getUploadInfoApi} from '@/http/http'
import {uploadFileToOss} from '@/http/api'
import {getStorage, setStorage} from '@/utils/storage'
import {catchErrorPlus} from '@/utils/sentry'
import useRecorder from './useRecorder'

interface RecordOptions {
  deviceId: string
  questionId: string
  onRecordStart?: () => void
  onRecordStop?: () => void
  onUploadSuccess?: (url: string) => void
  onUploadError?: (error: any) => void
}

interface AnswerData {
  question_id: string
  answer: Array<{type: string, value: string}>
}

const useStartNewRecord = () => {
  const currentQuestionId = useRef('')

  const recordOptions = useRef<RecordOptions>()

  const {currentAnswer, currentExamId} = useQuizStore(state => state)

  const {setCurrentAnswer} = useQuizStore.getState()

  const [isRecording, setIsRecording] = useState(false)

  const [isUploading, setIsUploading] = useState(false)
  
  const mediaStream = useRef<MediaStream | null>(null)

  const recRef = useRef<any>(null)

  const audioContextRef = useRef<AudioContext | null>(null)

  useRecorder()

  const startRecording = async (deviceId: string, questionId: string) => {
    console.log('🎤 开始录音流程 我是专门适配微信的录制器', {deviceId, questionId})
    
    if (isRecording) {
      console.warn('⚠️ 录音已在进行中，忽略重复请求')

      return
    }
    
    currentQuestionId.current = questionId
    recordOptions.current = {deviceId, questionId}
    
    try {
      console.log('📱 请求用户媒体设备权限...')
      const stream = await navigator.mediaDevices?.getUserMedia?.({audio: deviceId ? {deviceId: {exact: deviceId} } : true})

      console.log('✅ 媒体流获取成功，轨道数量:', stream.getTracks().length)
      mediaStream.current = stream
      
      console.log('🎵 创建音频上下文...')
      audioContextRef.current = new AudioContext()
      const input = audioContextRef.current.createMediaStreamSource(stream)

      recRef.current = new window.Recorder(input, {numChannels: 1})
      
      console.log('▶️ 开始录音...')
      recRef.current.record()
      
      setIsRecording(true)
      console.log('🎉 录音启动成功')
      
    } catch (error) {
      console.error('❌ 录音启动失败:', error)
      catchErrorPlus(error as Error, {
        tags: {component: 'quiz', type: 'start record error'}, 
        extras: {deviceId, questionId}
      })
      throw error
    }
  }

  const cleanup = () => {
    console.log('🧹 开始清理录音资源...')
    
    if (mediaStream.current) {
      console.log('🛑 停止媒体流轨道...')
      mediaStream.current.getTracks().forEach(track => track.stop())
      mediaStream.current = null
    }
    
    if (audioContextRef.current) {
      console.log('🔇 关闭音频上下文...')
      audioContextRef.current.close()
      audioContextRef.current = null
    }
    
    if (recRef.current) {
      console.log('🗑️ 清理录音器引用...')
      recRef.current = null
    }
    
    setIsRecording(false)
    setIsUploading(false)
    console.log('✅ 资源清理完成')
  }

  const stopRecording = async () => {
    console.log('⏹️ 停止录音流程...')
    
    if (!recRef.current || !isRecording) {
      console.warn('⚠️ 没有正在进行的录音')

      return
    }
    
    return new Promise<void>(async (resolve, reject) => {
      try {
        console.log('🛑 停止录音器...')
        recRef.current.stop()
        setIsRecording(false)
        console.log('🛑 停止媒体流...')
        mediaStream.current?.getTracks().forEach(track => track.stop())
        
        console.log('📦 导出音频文件...')
        
        // 将 exportWAV 包装为 Promise
        const blob = await new Promise<Blob>((resolveBlob, rejectBlob) => {
          recRef.current.exportWAV((blob: Blob) => {
            console.log('📁 音频文件导出成功，大小:', blob.size, 'bytes')
            resolveBlob(blob)
          })
        })
        
        // 等待上传完成
        await uploadAudioFile(blob)
        
        console.log('✅ 录音停止和上传流程完成')
        resolve()
        
      } catch (error) {
        console.error('❌ 停止录音失败:', error)
        catchErrorPlus(error as Error, {tags: {component: 'quiz', type: 'stop record error'} })
        reject(error)
      }
    })
  }

  const uploadAudioFile = async (blob: Blob) => {
    console.log('📤 开始上传音频文件...', {
      questionId: currentQuestionId.current, 
      blobSize: blob.size,
      blobType: blob.type
    })
    if (currentQuestionId?.current?.includes?.('_mock')) {
      console.log('🎉 当前题目ID是mock，忽略上传')
      const answerData: AnswerData = {
        question_id: currentQuestionId.current, 
        answer: [{type: 'voice', value: blob as any}]
      }
  
      setCurrentAnswer([answerData])

      return
    }

    if (!currentQuestionId.current) {
      console.error('❌ 当前题目ID不可用')

      return
    }

    setIsUploading(true)
    
    try {
      const file = new File([blob], `${currentQuestionId.current}.wav`, {type: 'audio/wav'})

      const options = {
        file_name: file.name, 
        content_type: 'audio/wav', 
        exam_id: currentQuestionId.current
      }

      console.log('🔍 获取上传信息...', options)
      const uploadRes = await getUploadInfoApi(options, true)

      console.log('✅ 上传信息获取成功:', uploadRes.data.data)
      
      console.log('☁️ 正在上传文件到OSS...')
      await uploadFileToOss(file, uploadRes.data.data.upload_url, true)
      
      const uploadedUrl = uploadRes.data.data.cdn_url

      console.log('🎉 文件上传成功，CDN URL:', uploadedUrl)
      
      updateAnswer(currentQuestionId.current, uploadedUrl)
      console.log('💾 音频上传流程完成')
      
    } catch (error) {
      console.error('❌ 上传失败:', error)
      catchErrorPlus(error as Error, {
        tags: {component: 'quiz', type: 'upload'}, 
        extras: {
          deviceId: recordOptions.current?.deviceId, 
          questionId: recordOptions.current?.questionId,
          token: getStorage('x-5e-token'),
          uid: getStorage('x-5e-uid')
        }
      })
      throw error
    } finally {
      setIsUploading(false)
      console.log('🏁 上传流程结束')
    }
  }

  const updateAnswer = (questionId: string, audioUrl: string) => {
    console.log('📝 更新答案数据...', {questionId, audioUrl})
    
    const newAnswer = [...currentAnswer]

    const existingIndex = newAnswer.findIndex(item => item.question_id === questionId)
    
    const answerData: AnswerData = {
      question_id: questionId, 
      answer: [{type: 'voice', value: audioUrl}]
    }

    if (existingIndex !== -1) {
      console.log('🔄 更新已存在的答案，索引:', existingIndex)
      newAnswer[existingIndex] = answerData
    } else {
      console.log('➕ 添加新答案')
      newAnswer.push(answerData)
    }

    setCurrentAnswer(newAnswer)
    console.log('💾 答案已更新到store，总数:', newAnswer.length)
    
    // 缓存答案
    const cacheData = {
      number: newAnswer.length, 
      answer: newAnswer, 
      id: currentExamId
    }

    setStorage(currentExamId + '___cacheAnswer', cacheData)
    console.log('💾 答案已缓存到本地存储:', cacheData)
  }

  return {
    isRecording, 
    isUploading,
    startRecording, 
    stopRecording,
    cleanup
  }
}

export default useStartNewRecord
