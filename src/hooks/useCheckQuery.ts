import {isProd, LoginUrl} from '@/global/consts'
import {captureError, captureStrMessage} from '@/utils/sentry'
import {getStorage, setStorage} from '@/utils/storage'
import {useEffect, useState} from 'react'
const useCheckUrlQuery = () => {
  const [initToken, setInitToken] = useState('')

  const checkQueryString = () => {
    const queryString = window.location.search

    const urlParams = new URLSearchParams(queryString)

    const sid = urlParams.get('sid')

    const uid = urlParams.get('uid')
    
    setInitToken(sid! || getStorage('x-5e-token')!)
    if (sid) {
      localStorage.setItem('x-5e-token', sid)
      localStorage.setItem('x-5e-uid', uid!)
      const path = getStorage('link_path')

      // 
      if (window?.location?.pathname.includes('quiz-new')) {
        setStorage('link_path', '')
        window.location.href = window.origin + window?.location?.pathname

        return
      }

      if (path) {
        window.location.href = `${window.location.origin}${path}`
      } else {
        // 需要处理
        const redirectGustoUrl = urlParams.get('redirectGustoUrl')

        window.location.href = redirectGustoUrl || window.location.href.split('?')[0]
        captureStrMessage(redirectGustoUrl!, '最后重定向的url: redirectGustoUrl')
      }
    } else if (!localStorage.getItem('x-5e-token')) {
      if (urlParams.get('debug')) {
        setStorage('x-5e-token', isProd ? 'sKoICl7mFXaG0vFa7cJSAjxJqwm09wA8' : 'qJvKkSgEyrZLg3aoJn0DtRMwUt4Sbf3q')
        setInitToken(isProd ? 'sKoICl7mFXaG0vFa7cJSAjxJqwm09wA8' : 'qJvKkSgEyrZLg3aoJn0DtRMwUt4Sbf3q')

        return
      }
      setStorage('link_path', window?.location?.pathname)
      window.location.href = LoginUrl + '?redirectGustoUrl=' + window.location.href
      captureStrMessage(LoginUrl + '?redirectGustoUrl=' + window.location.href, '重定向的url: redirectGustoUrl')
    }
  }

  useEffect(() => {
    checkQueryString()
  }, [])

  return [initToken]
}

export default useCheckUrlQuery
