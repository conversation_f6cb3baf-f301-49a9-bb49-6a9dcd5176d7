import {useEffect} from 'react'

const useNoAllowZoom = () => {
  useEffect(() => {
    // Prevent zooming on mobile devices
    const preventZoom = (e: TouchEvent) => {
      if (e.touches.length > 1) {
        e.preventDefault()
      }
    }

    // Prevent double tap zoom
    let lastTouchEnd = 0

    const preventDoubleTapZoom = (e: TouchEvent) => {
      const now = Date.now()

      if (now - lastTouchEnd <= 300) {
        e.preventDefault()
      }
      lastTouchEnd = now
    }

    // Add event listeners
    document.addEventListener('touchstart', preventZoom, {passive: false})
    document.addEventListener('touchend', preventDoubleTapZoom, {passive: false})

    // Cleanup event listeners on unmount
    return () => {
      document.removeEventListener('touchstart', preventZoom)
      document.removeEventListener('touchend', preventDoubleTapZoom)
    }
  }, [])
}

export default useNoAllowZoom
