'use client'

import {useEffect} from 'react'
import quizStore, {type Answer} from '@/store/quiz'
import {getQuestionApi, getQuestionTypeTipApi} from '@/http/http'
// import {
//   readAloudContent, repeatSentenceContent, writeFromDictationContent, describeImageContent, summarizeWrittenTextContent,
//   qaContent,
//   fillInBlankContent
// } from '@/global/hint'
import {useToast} from './use-toast'
import {getStorageJson, setSessionStorage, setStorage} from '@/utils/storage'
import {ToastAction} from '@/components/ui/toast'

interface Item {
  question: any[]; question_type: any 
  read_duration: number
  answer_duration: number
  answer_type: string
  tip_info: any
  question_id: string
}

const getExamType = (quizType: string) => {
  // if (quizType === 'enroll') return quizType
  // if (quizType === 'month-assessment') return 'month_assessment'
  // if (quizType === 'dev-mock') return 'dev_mock'

  return quizType || 'enroll'
}

interface QuesContent {
  [p: string]: (...s: any) => {type: string, value: string}[]
} 
interface QuesTempContent {
  [p: string]: {type: string, value: string}[]
} 

const useInitQuestion = (params: {quizType: string, fn?: (isOnlyCloseLoading: boolean) => void, allowNext: boolean}) => {
  const {toast} = useToast()

  useEffect(() => {
    if (params?.quizType && params.allowNext) {
      getQuestionLists()
    }
  }, [params.quizType, params.allowNext])

  const getQuestionLists = async () => {
    const examType = getExamType(params?.quizType)

    // 获取题目文案提示
    const hintInfoLists = (await (getQuestionTypeTipApi())).data.list?.map((item: { question_type: string }) => ({...item, type: item.question_type}))

    setSessionStorage('hintInfoLists', hintInfoLists)

    await getQuestionApi({exam_type: examType}).then(res => {
      // console.warn(res.data.data, 'res')

      if (res.data?.data?.list?.length) {
        const questions = res.data.data.list?.map((item: Item) => {
          const temp = {} as any

          if (item.tip_info) {
            temp.tipInfo = item?.tip_info
          }

          // 多选项目
          if (item.question_type === 'fill_in_the_blanks' && item.answer_type === 'option') {
            const optionItem = item.question.find(ix => ix.type === 'option')?.options

            const splitKey = optionItem ? Object.keys(optionItem).map(x => `{{${x}}}`) : []

            const splitParagraph = item.question.find(ix => ix.type === 'paragraph')

            const splitParagraphValue = splitParagraph?.value || ''

            let temp = [] as any[]

            try {
              const splitParts = splitParagraphValue.split(new RegExp(splitKey.join('|'), 'g'))

              // console.log('uniqueParts', splitParts)
              splitParts.forEach((part: string, index: number) => {
                temp.push({type: 'text', value: part.replace(/\n/g, '')}) 
                if (index < splitParts.length - 1) {
                  temp.push({type: 'select', value: optionItem?.[Object.keys(optionItem)?.[index]], key: Object.keys(optionItem)?.[index]})
                }
              })
            } catch (error) {
              console.info(error, 'error')
              temp = []
            }
            // console.log('temp', temp)
            item.question = [{type: 'paragraph', value: temp}]
          }

          return {
            ...item, 
            ...temp,
            introduction: hintInfoLists?.find((hint: any) => hint.type === item.question_type)?.introduction,
            content: item.question?.map(itm => {
              return {...itm, type: itm.type === 'voice' ? 'audio' : itm.type}
            }), 
            type: item.question_type,
            questionType: item.question_type, 
            answerType: item.answer_type,
            readTime: item.read_duration, 
            answerTime: item.answer_duration,
            // tipInfo: {...(item.tip_info || {}), isShow: item?.tip_info?.is_show},
            id: item.question_id,
          } 
        })

        // console.warn(questions, 'questions')
        // 获取题目的顺序
        const questionTypes = questions?.map((item: any) => item.type)

        const uniqueQuestionTypes = Array.from(new Set(questionTypes)) as string[]

        // console.info(uniqueQuestionTypes, 'uniqueQuestionTypes')
        const questionContent: QuesContent = {
          read_aloud: () => {
            const temp = hintInfoLists?.find((item: {type: string}) => item.type === 'read_aloud')

            return temp?.introduction ? [{type: 'text', value: temp?.introduction}] : []
          },
          repeat_sentence: () => {
            const temp = hintInfoLists?.find((item: {type: string}) => item.type === 'repeat_sentence')

            return temp?.introduction ? [{type: 'text', value: temp?.introduction}] : []
          },
          write_from_dictation: () => {
            const temp = hintInfoLists?.find((item: {type: string}) => item.type === 'write_from_dictation')

            return temp?.introduction ? [{type: 'text', value: temp?.introduction}] : []
          },
          describe_image: () => {
            const temp = hintInfoLists?.find((item: {type: string}) => item.type === 'describe_image')

            return temp?.introduction ? [{type: 'text', value: temp?.introduction}] : []
          },
          summarize_written_text: () => {
            const temp = hintInfoLists?.find((item: {type: string}) => item.type === 'summarize_written_text')

            return temp?.introduction ? [{type: 'text', value: temp?.introduction}] : []
          },
          quick_qa: () => {
            const temp = hintInfoLists?.find((item: {type: string}) => item.type === 'quick_qa')

            return temp?.introduction ? [{type: 'text', value: temp?.introduction}] : []
          },
          fill_in_the_blanks: () => {
            const temp = hintInfoLists?.find((item: {type: string}) => item.type === 'fill_in_the_blanks')

            return temp?.introduction ? [{type: 'text', value: temp?.introduction}] : []
          },
          write_email: () => {
            const temp = hintInfoLists?.find((item: {type: string}) => item.type === 'write_email')

            return temp?.introduction ? [{type: 'text', value: temp?.introduction}] : []
          },
          self_intro: () => {
            const temp = hintInfoLists?.find((item: {type: string}) => item.type === 'self_intro')

            return temp?.introduction ? [{type: 'text', value: temp?.introduction}] : []
          },
          single_image_choice: () => {
            const temp = hintInfoLists?.find((item: {type: string}) => item.type === 'single_image_choice')

            // console.warn(temp, 'temp1111')

            return temp?.introduction ? [{type: 'text', value: temp?.introduction}] : []
          },
          describe_image_with_voice: () => {
            const temp = hintInfoLists?.find((item: {type: string}) => item.type === 'describe_image_with_voice')

            return temp?.introduction ? [{type: 'text', value: temp?.introduction}] : []
          },
          single_text_choice: () => {
            const temp = hintInfoLists?.find((item: {type: string}) => item.type === 'single_text_choice')

            return temp?.introduction ? [{type: 'text', value: temp?.introduction}] : []
          },
          inline_input: () => {
            const temp = hintInfoLists?.find((item: {type: string}) => item.type === 'inline_input')

            return temp?.introduction ? [{type: 'text', value: temp?.introduction}] : []
          },
          true_false: () => {
            const temp = hintInfoLists?.find((item: {type: string}) => item.type === 'true_false')

            return temp?.introduction ? [{type: 'text', value: temp?.introduction}] : []
          },
        }

        // 为了适配这个逻辑
        let temp = {} as QuesTempContent

        Object.keys(questionContent).forEach(key => {
          const filterQuestions = questions.filter((item: {type: string}) => item.type === key) || []

          const payload = {len: filterQuestions?.length, quesLen: 0}

          // 填空题需要获取 填空题的个数!
          temp = {...temp, [key]: questionContent[key]?.(payload)}
        })
        // console.info(uniqueQuestionTypes, questions, 'uniqueQuestionTypes questions', temp)
        uniqueQuestionTypes?.forEach((item: string, index) => {
          const firstTypeIndex = questions?.findIndex((it: {type: string}) => it.type === item)

          // console.warn(firstTypeIndex, 'firstTypeIndex')
          if (firstTypeIndex !== -1) {
            if (temp?.[item]?.length) {
              temp[item][0].value = `Part ${index + 1}`
              questions[firstTypeIndex].tipInfo = {isShow: true, type: 'confirm', content: temp[item]}
            }
          }
        })
        // console.warn(questions, 'temp 0000000')
        // 特殊处理多选项目
        quizStore.getState().updateCurrentExamId(res.data.data.exam_id)
        quizStore.getState().setQuestionLists(questions)
        // console.warn(res.data.data.new_exam, 'res.data.data.new_exam')
        if (res.data.data.new_exam) {
          quizStore.getState().setCurrentQuestion(questions[0])
          quizStore.getState().setCurrentQuestionNumber(1)
          params.fn?.(true)
        } else {
          // 判断有无缓存 
          const cacheQuestion = getStorageJson(res.data.data.exam_id + '___cacheAnswer') || {}
          
          // 如果缓存的题目的长度大于答题的长度则清空缓存
          if (cacheQuestion?.answer?.length > questions.length) {
            setStorage(res.data.data.exam_id + '___cacheAnswer', {})
            quizStore.getState().setCurrentQuestion(questions[0])
            quizStore.getState().setCurrentQuestionNumber(1)
            params.fn?.(false)
          } else if (cacheQuestion?.id === res.data.data.exam_id && cacheQuestion?.number) {
            quizStore.getState().setCurrentQuestionNumber(cacheQuestion.number + 1)
            quizStore.getState().setCurrentQuestion(questions[cacheQuestion.number])
            updateCurrentAnswer(cacheQuestion.answer)
            params.fn?.(false)
          } else {
            // 提示弹窗重新开始
            quizStore.getState().setCurrentQuestion(questions[0])
            quizStore.getState().setCurrentQuestionNumber(1)
            params.fn?.(false)
          }
        }
      }

    }).catch(r => {
      // console.error(r, 'r')
      toast({
        title: 'Failed to get question',
        description: r?.data?.msg || '',
        variant: 'destructive',
      })
    })
  }

  const updateQuestionNumber = (number: number) => {
    const {questionLists} = quizStore.getState()

    quizStore.getState().setCurrentQuestionNumber(number)
    quizStore.getState().setCurrentQuestion(questionLists[number - 1])
  }

  const updateCurrentAnswer = (answer: Answer[]) => {
    quizStore.getState().setCurrentAnswer(answer)
  }

  return {
    questionLists: quizStore(state => state.questionLists),
    currentQuestion: quizStore(state => state.currentQuestion),
    currentQuestionNumber: quizStore(state => state.currentQuestionNumber),
    totalNumber: quizStore(state => state.totalNumber),
    currentAnswer: quizStore(state => state.currentAnswer),
    updateQuestionNumber,
    updateCurrentAnswer,
  }
}

export default useInitQuestion
