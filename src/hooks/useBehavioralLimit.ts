import {isProd} from '@/global/consts'
import {useEffect} from 'react'

const useBehavioralLimit = () => {
  useEffect(() => {
    const handleContextMenu = (event: MouseEvent) => {
      if (isProd) {
        event.preventDefault()
      }
    }

    window.document.addEventListener('contextmenu', handleContextMenu)

    return () => {
      window.document.removeEventListener('contextmenu', handleContextMenu)
    }
  }, [])
}

export default useBehavioralLimit
