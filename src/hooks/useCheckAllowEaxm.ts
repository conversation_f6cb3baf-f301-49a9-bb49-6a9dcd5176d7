import {isProd, LoginUrl} from '@/global/consts'
import {getExamListApi, getUserInfoApi, getUserPayInfo} from '@/http/http'
import {getStorage} from '@/utils/storage'
import {useEffect} from 'react'

const WhitePhoneLists = [
  '15021662997', 
  '13141232231', 
  '15528388780', 
  '17601379688', 
  '15014006740', 
  '19900009999', 
  '17688490998',
  '13118693845',
  '13842636840',
  '18116382840',
  '13411657684',
  '13108956092',
  '15252788529',
  '13686443256'
]

const useCheckAllowExam = (initToken: string) => {
  useEffect(() => {
    if (!isProd) return
    if (!initToken) return
    getUserPayInfo().then(res => {
      const lists = res.data?.data || []

      const isAllowed = Boolean(lists?.filter?.((item: { was_purchased_or_subscribed: any; }) => item.was_purchased_or_subscribed)?.length)

      if (isAllowed) return
      // 查询用户信息 是否是白名单
      const uid = getStorage('x-5e-uid')

      // 先判断登录
      if (!uid) {
        return window.location.href = LoginUrl
      }

      // 检查白名单
      getUserInfoApi(uid!).then(itm => {
        console.log(itm.data.data, '请求用户信息')
        const phone = itm?.data?.data?.phone

        if (WhitePhoneLists.includes(phone)) {
          return
        } else {
          // 增加一种情况, 无支付记录, 且自己的完成考试场数<1, 则允许, 否则弹回官网个人页
          getExamListApi().then(res => {
            const finishedExamLists = res.data?.data?.list?.filter?.((item: any) => item?.submit_at !== null) || []

            console.log('11111', finishedExamLists)
            console.log('22222', res.data?.data?.list)
            console.log('33333', res.data)
            if (finishedExamLists.length > 0) {
              // 跳转到等待页面
              return window.location.href = '/waiting'
              // return window.location.href = 'https://gustoenglish.com/'
            } else {
              return
            }
          })
          // window.location.href = 'https://gravity-pay.wemore.com/post-gamma'
        }
      })
      
    })
  }, [initToken])
}

export default useCheckAllowExam
