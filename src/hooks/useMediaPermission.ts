import {requestMediaPermission} from '@/utils/permission'
import {useEffect, useState} from 'react'

const useMediaPermission = ({allowNext}: {allowNext: boolean}) => {
  const [isRequestMediaPermission, setRequestMediaPermission] = useState(false)

  useEffect(() => {
    if (!allowNext) return
    try {
      requestMediaPermission()
        ?.catch(() => {})
        ?.finally(() => {
          setRequestMediaPermission(true)
        })
    } catch (error) {
      console.info(error, '媒体查询')
    }
  }, [allowNext])

  return {isRequestMediaPermission}
}

export default useMediaPermission
