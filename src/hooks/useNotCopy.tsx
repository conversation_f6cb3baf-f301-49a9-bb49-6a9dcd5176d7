import {useEffect} from 'react'

const useNotAllowCopy = () => {
  useEffect(() => {
    // Prevent copy on all elements
    const preventCopy = (e: Event) => {
      e.preventDefault()

      return false
    }

    // Define all events to prevent
    const events = [
      'copy',
      'cut', 
      'paste',
      'contextmenu',
    //   'selectstart',
    //   'mousedown',
    //   'dragstart',
    //   'keydown'
    ]

    // Add event listeners
    events.forEach(event => {
      document.addEventListener(event, preventCopy)
    })

    // Cleanup event listeners on unmount
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, preventCopy)
      })
    }
  }, [])
}

export default useNotAllowCopy