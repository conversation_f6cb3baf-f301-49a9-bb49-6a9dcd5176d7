import {imageProblem} from '@/global/questions'
import {Question} from '@/store/quiz'
import {getRandomNumber} from '@/utils/tool'

const imageIndex = getRandomNumber(imageProblem.length - 1)

export const mockQuestions: Question[] = [
  {
    id: '002',
    title: 'Read Aloud', 
    questionType: 'text',
    content: [{
      type: 'text', value: `Perseverance is the key to success. It is the ability to keep going even when things get tough, to persist through challenges and obstacles. No matter how difficult a task may seem, it is important to stay focused and continue moving forward. Success is not always immediate; it requires patience, effort, and determination.
      <PERSON> once said, "I have not failed. I've just found 10,000 ways that won't work." This mindset of persistence is what eventually led him to invent the light bulb. Similarly, many successful people in various fields—whether it be sports, business, or science—have faced setbacks. However, they didn’t let those setbacks define them. Instead, they used them as stepping stones to greater achievements.
      So, remember that when you feel like giving up, it’s not the end. It’s just another chance to grow stronger. Keep trying, keep learning, and never give up on your goals.`
    }],
    answerType: 'voice', 
    readTime: 10,
    answerTime: 30,
    tipInfo: {
      type: 'confirm',
      isShow: true,
      content: [
        {type: 'text', value: '这是一段回答题，请回答以上内容'}, 
      ]
    }
  },
  {
    id: '002',
    title: 'Read Aloud2', 
    questionType: 'text',
    content: [{
      type: 'text', value: `Perseverance is the key to success. It is the ability to keep going even when things get tough, to persist through challenges and obstacles. No matter how difficult a task may seem, it is important to stay focused and continue moving forward. Success is not always immediate; it requires patience, effort, and determination.
      Thomas Edison once said, "I have not failed. I've just found 10,000 ways that won't work." This mindset of persistence is what eventually led him to invent the light bulb. Similarly, many successful people in various fields—whether it be sports, business, or science—have faced setbacks. However, they didn’t let those setbacks define them. Instead, they used them as stepping stones to greater achievements.
      So, remember that when you feel like giving up, it’s not the end. It’s just another chance to grow stronger. Keep trying, keep learning, and never give up on your goals.`
    }],
    answerType: 'voice', 
    readTime: 10,
    answerTime: 30,
  },
  {
    id: '003',
    title: 'Repeat Sentence', 
    questionType: 'audio',
    content: [{type: 'audio', value: 'https://echo101-oss.wemore.com/gusto_english/assessment/month1.mp4'}],
    answerType: 'voice', 
    readTime: 10,
    answerTime: 30,
    tipInfo: {
      type: 'confirm',
      isShow: true,
      content: [
        {type: 'text', value: '这是一段跟读题，请读出以上内容'}, 
      ]
    }
  },
  {
    id: '003',
    title: 'Repeat Sentence2', 
    questionType: 'audio',
    content: [{type: 'audio', value: 'https://echo101-oss.wemore.com/gusto_english/assessment/month1.mp4'}],
    answerType: 'voice', 
    readTime: 10,
    answerTime: 30,
  },
  {
    id: '004',
    title: 'Write from Dictation', 
    questionType: 'audio',
    content: [{type: 'audio', value: 'https://echo101-oss.wemore.com/gusto_english/assessment/month1.mp4'}],
    answerType: 'text', 
    readTime: 10,
    answerTime: 30,
    tipInfo: {
      type: 'confirm',
      isShow: true,
      content: [
        {type: 'text', value: '这是一段听写题，请写出以上内容'}, 
      ]
    }
  },
  {
    id: '004',
    title: 'Write from Dictation2', 
    questionType: 'audio',
    content: [{type: 'audio', value: 'https://echo101-oss.wemore.com/gusto_english/assessment/month1.mp4'}],
    answerType: 'text', 
    readTime: 10,
    answerTime: 30,
  },
  {
    id: '005',
    title: 'Describe Image', 
    questionType: 'image',
    content: [{type: 'image', value: imageProblem[imageIndex]}],
    answerType: 'text', 
    readTime: 0,
    answerTime: 30,
    tipInfo: {
      type: 'confirm',
      isShow: true,
      content: [
        {type: 'text', value: '这是一段看图题，请写出图片上的内容'}, 
      ]
    }
  },
  {
    id: '006',
    title: 'Summarize Written Text', 
    questionType: 'text',
    content: [{type: 'text', value: 'Sound waves are captured and converted into electrical signals. These signals are then processed and stored digitally. When you press play, the device retrieves the stored data and converts it back into electrical signals. These signals are then amplified and sent to the speakers, where they vibrate the air to create sound waves that we can hear.'}],
    answerType: 'text', 
    readTime: 0,
    answerTime: 5 * 60,
    tipInfo: {
      type: 'confirm',
      isShow: true,
      content: [
        {type: 'text', value: '这是一段简要概括题，请写出以上内容简要概括'}, 
      ]
    }
  },
  {
    id: '006',
    title: 'Write Email ', 
    questionType: 'text',
    content: [{type: 'text', value: 'Imagine a symphony trapped within a digital shell. With the press of a button, this shell is shattered, releasing a torrent of sound waves that dance and swirl around us. The air becomes alive with vibrations, carrying melodies and stories from the past into the present.'}],
    answerType: 'text', 
    readTime: 0,
    answerTime: 15 * 60,
    tipInfo: {
      type: 'confirm',
      isShow: true,
      content: [
        {type: 'text', value: '这是一个作文题，请写出以上内容简要出作文'}, 
      ]
    }
  },
]

export const mockMonthQuestions: Question[] = [
  // {
  //   title: 'Please listen carefully and record your answer',
  //   type: 'video',
  //   content: [{type: 'video', value: 'https://echo101-oss.wemore.com/gusto_english/assessment/month1.mp4'}],  
  //   answerType: 'voice',
  //   questionNumber: 1,
  //   cutDown: 90,
  //   questionDescription: '月度评估-题目1"', 
  // },
  // {
  //   title: "Please describe what do you see in the picture in less than 100 words",
  //   type: 'image',
  //   content: [{type: 'image', value: imageProblem[imageIndex]}],
  //   answerType: 'text',
  //   questionNumber: 2,
  //   cutDown: 300,
  //   questionDescription: imageProblem[imageIndex].split('/').pop()!,
  // }
]