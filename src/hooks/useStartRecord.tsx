import {useRef, useState} from 'react'
import useQuizStore from '@/store/quiz'
import {getUploadInfoApi} from '@/http/http'
import {uploadFileToOss} from '@/http/api'
import {getStorage, setStorage} from '@/utils/storage'
import {catchErrorPlus} from '@/utils/sentry'

interface RecordOptions {
  deviceId: string
  questionId: string
  onRecordStart?: () => void
  onRecordStop?: () => void
  onUploadSuccess?: (url: string) => void
  onUploadError?: (error: any) => void
}

const useStartRecord = () => {
  const currentQuestionId = useRef('')

  const recordOptions = useRef<RecordOptions>()

  const {currentAnswer, currentExamId} = useQuizStore(state => state)

  const {setCurrentAnswer} = useQuizStore.getState()

  const [isRecording, setIsRecording] = useState(false)

  const [isUploading, setIsUploading] = useState(false)

  const recorder = useRef<MediaRecorder | null>(null)

  const mediaStream = useRef<MediaStream | null>(null)

  const recordedChunks = useRef<Blob[]>([])
  
  // 添加状态检查定时器的引用
  const statusCheckInterval = useRef<NodeJS.Timeout | null>(null)
  
  const startRecording = async (deviceId: string, questionId: string) => {
    console.log('开始录音... 我只转门录制非微信的录制器', {deviceId, questionId})
    
    const options: RecordOptions = {
      deviceId,
      questionId
    }
    
    currentQuestionId.current = questionId
    recordOptions.current = options
    recordedChunks.current = []
    
    try {
      console.log('请求用户媒体设备...')
      const stream = await navigator.mediaDevices?.getUserMedia?.({audio: deviceId ? {deviceId: {exact: deviceId} } : true})

      console.log('获取媒体流成功:', stream)
      mediaStream.current = stream
      recorder.current = new MediaRecorder(stream, {audioBitsPerSecond: 128000,})

      // 添加录音状态监听器
      recorder.current.onstart = () => {
        console.log('📹 录音器启动事件触发')
        console.log('📹 录音器初始状态:', recorder.current?.state)
      }

      recorder.current.onerror = (event) => {
        console.error('❌ 录音器错误事件:', event)
        console.error('❌ 录音器错误状态:', recorder.current?.state)
        catchErrorPlus(new Error('MediaRecorder error'), {
          tags: {component: 'quiz', type: 'recorder error'}, 
          extras: {deviceId, questionId, error: event}
        })
      }

      recorder.current.onpause = () => {
        console.log('⏸️ 录音器暂停事件触发')
      }

      recorder.current.onresume = () => {
        console.log('▶️ 录音器恢复事件触发')
      }

      recorder.current.ondataavailable = handleDataAvailable
      
      // 微信端判断，每1000ms执行一次
      recorder.current.start() // 1000ms分片

      setIsRecording(true)
      console.log('录音启动成功')

      // 开始状态检查
      startStatusCheck()
      
    } catch (error) {
      console.error('录音启动失败:', error)
      catchErrorPlus(error as Error, {tags: {component: 'quiz', type: 'start record error'}, extras: {deviceId, questionId} })
      throw error
    }
  }

  // 开始状态检查
  const startStatusCheck = () => {
    console.log('🔍 开始录音状态检查')
    let dataAvailableCount = 0

    let lastChunkCount = 0
    
    statusCheckInterval.current = setInterval(() => {
      if (recorder.current) {
        const currentChunkCount = recordedChunks.current.length

        const chunkDiff = currentChunkCount - lastChunkCount
        
        console.log('🔍 录音状态检查:', {
          state: recorder.current.state,
          chunksCount: currentChunkCount,
          newChunks: chunkDiff,
          isRecording: isRecording
        })

        // 检查是否有新的数据块产生
        if (chunkDiff === 0 && recorder.current.state === 'recording' && isRecording) {
          dataAvailableCount++
          console.warn('⚠️ 警告: 连续', dataAvailableCount, '秒没有收到录音数据')
          
          // 如果连续3秒没有数据，可能有问题
          if (dataAvailableCount >= 3) {
            console.error('❌ 录音可能异常：连续3秒无数据')
            catchErrorPlus(new Error('Recording data timeout'), {
              tags: {component: 'quiz', type: 'recording timeout'}, 
              extras: {
                questionId: currentQuestionId.current,
                chunksCount: currentChunkCount,
                recorderState: recorder.current.state
              }
            })
          }
        } else {
          dataAvailableCount = 0 // 重置计数器
        }
        
        lastChunkCount = currentChunkCount
      } else {
        console.log('🔍 录音器已不存在，停止状态检查')
        stopStatusCheck()
      }
    }, 1000)
  }

  // 停止状态检查
  const stopStatusCheck = () => {
    if (statusCheckInterval.current) {
      console.log('🔍 停止录音状态检查')
      clearInterval(statusCheckInterval.current)
      statusCheckInterval.current = null
    }
  }

  const cleanup = () => {
    console.log('清理录音资源...')
    
    // 停止状态检查
    stopStatusCheck()
    
    if (mediaStream.current) {
      mediaStream.current.getTracks().forEach(track => track.stop())
      mediaStream.current = null
    }
    
    recorder.current = null
    console.log('资源清理完成')
  }

  const stopRecording = async () => {
    console.log('停止录音...', {isRecording, hasRecorder: !!recorder.current})
    
    if (!recorder.current || !isRecording) {
      console.log('没有正在进行的录音可以停止')
      stopStatusCheck() // 确保停止状态检查

      return
    }

    setIsRecording(false)

    return new Promise<void>((resolve, reject) => {
      if (!recorder.current) {
        console.log('录音器不可用，立即解决Promise')
        stopStatusCheck()
        resolve()

        return
      }

      // 设置录制停止的回调
      recorder.current.onstop = async () => {
        console.log('🛑 录音器已停止，处理录音结束...')
        stopStatusCheck() // 停止状态检查
        try {
          // 等待录制数据处理和上传完成
          await handleRecordStop()
          cleanup()
          console.log('录音结束处理成功')
          resolve()
        } catch (error) {
          console.error('处理录音结束时出错:', error)
          catchErrorPlus(error as Error, {
            tags: {component: 'quiz', type: 'stop record error'}, extras: {
              deviceId: recordOptions.current?.deviceId, questionId: recordOptions.current?.questionId,
              token: getStorage('x-5e-token'),
              uid: getStorage('x-5e-uid')
            }
          })
          cleanup()
          reject(error)
        }
      }

      recorder.current.stop()
      
      // 停止所有音频轨道
      if (mediaStream.current) {
        console.log('停止媒体轨道...')
        mediaStream.current.getTracks().forEach(track => track.stop())
        mediaStream.current = null
      }
    })
  }

  const handleDataAvailable = (event: BlobEvent) => {
    console.log('🎵 录音数据可用:', {
      size: event.data.size,
      totalChunks: recordedChunks.current.length + 1,
      timestamp: new Date().toLocaleTimeString()
    })
    if (event.data.size > 0) {
      recordedChunks.current.push(event.data)
    }
  }

  const handleRecordStop = async () => {
    console.log('录音已停止，音频块数量:', recordedChunks.current.length)
    
    if (recordedChunks.current.length > 0) {
      const blob = new Blob(recordedChunks.current, {type: 'audio/mpeg'})

      console.log('创建音频Blob:', {size: blob.size, type: blob.type})
      await uploadAudioFile(blob)
    } else {
      console.warn('没有可用的录音数据块')
    }
  }

  const uploadAudioFile = async (blob: Blob) => {
    console.log('开始上传音频文件...', {questionId: currentQuestionId.current, blobSize: blob.size})

    if (currentQuestionId?.current?.includes?.('_mock')) {
      console.log('🎉 当前题目ID是mock，忽略上传')
      const answerData: any = {
        question_id: currentQuestionId.current, 
        answer: [{type: 'voice', value: blob as any}]
      }
  
      setCurrentAnswer([answerData])

      return
    }
    
    if (!currentQuestionId.current) {
      console.error('当前题目ID不可用')

      return
    }

    setIsUploading(true)
    
    try {
      const file = new File([blob], `${currentQuestionId.current}.mp3`, {type: 'audio/mpeg'})

      const fileName = file.name

      const fileType = file.type

      const options = {
        file_name: fileName, 
        content_type: fileType, 
        exam_id: currentQuestionId.current
      }

      console.log('获取上传信息...', options)
      // 获取上传信息
      const uploadRes = await getUploadInfoApi(options)

      console.log('上传信息获取成功:', uploadRes.data.data)
      
      console.log('正在上传文件到OSS...')
      // 上传文件到OSS
      await uploadFileToOss(file, uploadRes.data.data.upload_url)
      
      const uploadedUrl = uploadRes.data.data.cdn_url

      console.log('文件上传成功，URL:', uploadedUrl)
      
      // 更新答案
      updateAnswer(currentQuestionId.current, uploadedUrl)
      
      console.log('音频上传完成:', uploadedUrl)
      
    } catch (error) {
      console.error('上传失败:', error)
      catchErrorPlus(error as Error, {
        tags: {component: 'quiz', type: 'upload'}, extras: {
          deviceId: recordOptions.current?.deviceId, questionId: recordOptions.current?.questionId,
          token: getStorage('x-5e-token'),
          uid: getStorage('x-5e-uid')
        }
      })
      throw error
    } finally {
      setIsUploading(false)
      console.log('上传流程结束')
    }
  }

  const updateAnswer = (questionId: string, audioUrl: string) => {
    console.log('更新答案...', {questionId, audioUrl})
    
    const newAnswer = [...currentAnswer]

    const existingIndex = newAnswer.findIndex(item => item.question_id === questionId)
    
    const answerData = {
      question_id: questionId, 
      answer: [{type: 'voice', value: audioUrl}]
    }

    if (existingIndex !== -1) {
      console.log('更新已存在的答案，索引:', existingIndex)
      // 更新已存在的答案
      newAnswer[existingIndex] = answerData
    } else {
      console.log('添加新答案')
      // 添加新答案
      newAnswer.push(answerData)
    }

    setCurrentAnswer(newAnswer)
    console.log('答案已更新到store:', newAnswer)
    
    // 缓存答案
    const cacheData = {
      number: newAnswer.length, 
      answer: newAnswer, 
      id: currentExamId
    }

    setStorage(currentExamId + '___cacheAnswer', cacheData)
    console.log('答案已缓存到本地存储:', cacheData)
  }

  return {
    isRecording, 
    isUploading,
    startRecording, 
    stopRecording,
    cleanup
  }
}

export default useStartRecord
