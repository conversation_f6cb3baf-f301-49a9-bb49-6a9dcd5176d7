import {useState, useEffect, useRef} from 'react'

function useCountdown(initialSeconds: number) {
  const [count, setCount] = useState(initialSeconds)

  const [isRunning, setIsRunning] = useState(false)

  const intervalRef = useRef<NodeJS.Timeout>()

  const startCountdown = () => {
    if (initialSeconds <= 0) return
    setIsRunning(true)
  }

  const stopCountdown = () => {
    setIsRunning(false)
    setCount(initialSeconds)
    clearInterval(intervalRef.current)
  }

  useEffect(() => {
    if (isRunning) {
      const interval = setInterval(() => {
        setCount(prevCount => {
          if (prevCount <= 0) {
            clearInterval(intervalRef.current)
            setIsRunning(false)

            return 0
          }

          return prevCount - 1
        })
      }, 1000)

      intervalRef.current = interval
    } else {
      clearInterval(intervalRef.current)
    }

    return () => clearInterval(intervalRef.current)
  }, [isRunning, initialSeconds])

  const setCountZero = () => {
    setIsRunning(false)
    setCount(0)
  }

  return {
    count, start: startCountdown, stop: stopCountdown, isRunning, resetZero:setCountZero
  }
}

export default useCountdown