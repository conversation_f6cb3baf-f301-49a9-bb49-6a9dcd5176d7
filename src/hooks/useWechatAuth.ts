// useWechatAuth 微信授权

import {isProd} from '@/global/consts'
import {registerWechatOpenIdApi} from '@/http/http'
import {getStorageJson, setStorage} from '@/utils/storage'
import {useEffect, useState} from 'react'

const useWechatAuth = (initToken: string) => {
  const [allowNext, setAllowNext] = useState(false)  

  const [isWxMobile, setIsWxMobile] = useState(false)

  const calculateLink = () => {
    const appId = 'wx6ce62180384c80f8'

    const link = window.location.href.replace(window.location.origin, '')

    const examTest = isProd ? 'gustoenglish-exam' : 'exam-test'

    const redirectUri = encodeURIComponent(`https://wechat-oauth.wemore.com/${examTest}${link}`)

    const scope = 'snsapi_userinfo'

    const wechatAuthUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}#wechat_redirect`

    location.href = wechatAuthUrl
  }

  // 判断是微信内部打开
  const isWechat = () => {
    const ua = window.navigator.userAgent.toLowerCase()

    return /micromessenger/i.test(ua)
  }

  useEffect(() => {
    if (
      !isWechat() ||
      !isProd ||
      window.location.hostname.includes('gusto-english-exam-test.wemore.com')
    ) {
      setAllowNext(true)

      return
    }
    if (initToken && isWechat() && !window?.location?.search?.includes('code')) {
      calculateLink()

      return
    }
    const queryString = window.location.search

    const urlParams = new URLSearchParams(queryString)

    // 已经消费的code不处理
    if (initToken && urlParams.get('code') && !getStorageJson('wechat-code')?.[urlParams.get('code')!]) {
      // console.log(urlParams.get('code'), 'code')
      registerWechatOpenIdApi({code: urlParams.get('code')!}).then(() => {
        setStorage('wechat-code', {code: urlParams.get('code')!})
        
        // 判断如果是微信移动端 则 setIsWxMobile(true)

        // if (isWeChatMobileOnly()) {
        //   setIsWxMobile(true)

        //   return
        // }              
        
        setAllowNext(true)
      })
    }
  }, [initToken])

  return [allowNext, isWxMobile]
}

export default useWechatAuth