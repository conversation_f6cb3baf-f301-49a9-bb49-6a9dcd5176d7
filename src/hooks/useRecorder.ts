// hooks/useRecorder.ts
import {useEffect, useRef} from 'react'

declare global {
  interface Window {
    Recorder?: any;
  }
}

export default function useRecorder() {
  const streamRef = useRef<MediaStream | null>(null)

  const contextRef = useRef<AudioContext | null>(null)

  useEffect(() => {
    console.log('🎵 useRecorder hook 初始化...')
    
    if (typeof window === 'undefined') {
      console.log('⚠️ 非浏览器环境，跳过初始化')

      return
    }

    const loadScript = async () => {
      console.log('📜 检查 Recorder.js 是否已加载...')
      
      if (!window.Recorder) {
        console.log('📥 开始加载 Recorder.js 脚本...')
        await new Promise<void>((resolve, reject) => {
          const script = document.createElement('script')

          script.src = '/js/record.js'
          script.async = true
          script.onload = () => {
            console.log('✅ Recorder.js 加载成功')
            resolve()
          }
          script.onerror = () => {
            console.error('❌ Recorder.js 加载失败')
            reject(new Error('Recorder.js 加载失败'))
          }
          document.body.appendChild(script)
          console.log('📄 脚本标签已添加到页面')
        })
      } else {
        console.log('✅ Recorder.js 已存在，无需重复加载')
      }
    }

    loadScript().catch((error) => {
      console.error('❌ 脚本加载过程中发生错误:', error)
    })

    return () => {
      console.log('🧹 useRecorder hook 清理资源...')
      
      if (streamRef.current) {
        console.log('🛑 停止媒体流轨道...')
        streamRef.current.getTracks().forEach(track => track.stop())
        streamRef.current = null
      }
      
      if (contextRef.current) {
        console.log('🔇 关闭音频上下文...')
        contextRef.current.close()
        contextRef.current = null
      }
      
      console.log('✅ useRecorder hook 清理完成')
    }
  }, [])
}
