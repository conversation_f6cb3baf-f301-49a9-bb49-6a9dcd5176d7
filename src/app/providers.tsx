// app/providers.tsx
'use client'

import {usePathname, useSearchParams} from 'next/navigation'
import {useEffect} from 'react'

import posthog from 'posthog-js'
import {PostHogProvider as PHProvider} from 'posthog-js/react'
import {getStorage} from '@/utils/storage'
import {isProd} from '@/global/consts'

const NEXT_PUBLIC_POSTHOG_KEY = 'phc_Vf64SQcwVrCdDEeYIgapDiQs2f2wVu7XbNVxjZ8wt3F'

const NEXT_PUBLIC_POSTHOG_HOST = 'https://eu.i.posthog.com'

export function PostHogProvider({children}: { children: React.ReactNode }) {
  
  useEffect(() => {
    // 如果域名存在localhost则直接return
    if (typeof window !== 'undefined' && window.location.hostname.includes('localhost')) {
      return
    }
    if (isProd) {
      posthog.init(NEXT_PUBLIC_POSTHOG_KEY as string, {
        api_host: NEXT_PUBLIC_POSTHOG_HOST || 'https://eu.i.posthog.com',
        person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
        defaults: '2025-05-24',
        autocapture: false
      })
      
      // 如果存在用户信息则设置id
      const uid = getStorage('x-5e-uid')
      
      if (uid) {
        posthog.identify(uid)
      }
    }
  }, [])

  if (!isProd || (typeof window !== 'undefined' && window.location.hostname.includes('localhost'))) {
    return <>{children}</>
  }

  return (
    <PHProvider client={posthog}>
      {children}
    </PHProvider>
  )
}
