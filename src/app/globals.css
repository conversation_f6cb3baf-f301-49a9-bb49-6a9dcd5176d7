@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'GTVCS-Black'; /* 为了明确是自己设置的字体生效取得二逼名字 */
  src: url('../../public/font/GTVCS-Black.otf');
}

@font-face {
  font-family: 'GTVCS-Bold'; /* 为了明确是自己设置的字体生效取得二逼名字 */
  src: url('../../public/font/GTVCS-Bold.otf');
}

@font-face {
  font-family: 'GTVCS-Book'; /* 为了明确是自己设置的字体生效取得二逼名字 */
  src: url('../../public/font/GTVCS-Book.otf');
}


html,
body {
  max-width: 100vw;
  overflow-x: hidden;

  user-select: none;
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE10+/Edge */
}

body {
  font-family: Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f0f0f0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, PingFang SC, Microsoft YaHei,
  Source Han Sans SC, Noto Sans CJK SC, WenQuanYi Micro Hei, sans-serif;
  outline: none;
  /* overflow: hidden; */
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}



#exam-content-box:-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 8px !important;
  width: 8px !important;
  background: #D9D9D9 !important;
}



/* 从右侧进入的动画 */
.slide-right-enter {
  transform: translateX(100%);
  opacity: 0;
}

.slide-right-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: all 300ms ease-out;
}

.slide-right-exit {
  transform: translateX(0);
  opacity: 1;
}

.slide-right-exit-active {
  transform: translateX(-100%);
  opacity: 0;
  transition: all 300ms ease-out;
}

/* 从左侧进入的动画 */
.slide-left-enter {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-left-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: all 300ms ease-out;
}

.slide-left-exit {
  transform: translateX(0);
  opacity: 1;
}

.slide-left-exit-active {
  transform: translateX(100%);
  opacity: 0;
  transition: all 300ms ease-out;
}

.DialogContent {
  position: fixed;
  top: 10%;
  left: 50%;
  width: calc(100vw - 32px);
  transform: translate(-50%, -50%);
}

/* Start Generation Here */
/* 滚动条样式优化 */
.scrollview::-webkit-scrollbar {
  width: 4px;
}

.scrollview::-webkit-scrollbar-track {
  background: transparent;
}

.scrollview::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.scrollview::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Firefox 滚动条样式 */
.scrollview {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}
/* End Generation Here */

.summarize-text-with-voice {
  font-family: 'GTVCS-Book';
}


  
.bg-primary-low {
  background-color: #fff;
}

.bg-primary {
  background-color: #22c55e;
}

.bg-success {
  background-color: #ffc640;
}
