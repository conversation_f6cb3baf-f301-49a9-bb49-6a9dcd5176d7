import QuizView from '@/views/quiz'
import {ErrorBoundary} from '@/components/business/ErrBoundary'
import axios from 'axios'
import {BaseHttpUrl} from '@/global/consts'

const QuizPage = async (props: any) => {
  const appId = await props.params

  console.log('当前路由:', appId)

  return (
    <ErrorBoundary>
      <QuizView quizType={appId.exam_type} />
    </ErrorBoundary>
  )
}

export default QuizPage

export async function generateStaticParams() {
  try {
    const api = axios.create({baseURL: BaseHttpUrl})

    const res = await api.get('/gustoenglish/exam_types')

    const examTypes = res.data?.data?.list || []

    return examTypes.map((exam_type: string) => ({exam_type: exam_type,}))
  } catch (error) {
    console.error('Failed to fetch exam types:', error)

    return [ 'enroll', 'month_assessment', 'foo', 'dev_mock' ].map((exam_type) => ({exam_type,}))
  }
}
