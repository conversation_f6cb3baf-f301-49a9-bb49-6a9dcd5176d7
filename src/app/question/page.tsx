'use client'
import {ErrorBoundary} from '@/components/business/ErrBoundary'
import {useEffect, useState} from 'react'
import QuestionNewView from '@/views/question-new'

const QuestionPage = () => {
  const [id, setId] = useState(process.env.NODE_ENV === 'development' ? '1074' : '')

  useEffect(() => {
    setId(window?.location?.pathname?.split('/')?.[2] || '1074')
  }, [])

  if (!id) return null

  return (
    <ErrorBoundary>
      <QuestionNewView questionId={id} />
    </ErrorBoundary>
  )
}

export default QuestionPage
