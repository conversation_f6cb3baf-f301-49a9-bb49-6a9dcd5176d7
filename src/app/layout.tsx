import type {Metadata} from 'next'
import localFont from 'next/font/local'

import './globals.css'
import {PostHogProvider} from './providers'

const geistSans = localFont({
  src: './fonts/GeistVF.woff',
  variable: '--font-geist-sans',
  weight: '100 900',
})

const geistMono = localFont({
  src: './fonts/GeistMonoVF.woff',
  variable: '--font-geist-mono',
  weight: '100 900',
})

const gtVcsBlack = localFont({
  src: './fonts/GTVCS-Black.otf',
  variable: '--font-gtvcs-black',
  display: 'swap',
})

const gtVcsBold = localFont({
  src: './fonts/GTVCS-Bold.otf',
  variable: '--font-gtvcs-bold', 
  display: 'swap',
})

const gtVcsBook = localFont({
  src: './fonts/GTVCS-Book.otf',
  variable: '--font-gtvcs-book', 
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'Gusto English 高拓英语',
  description: 'Gusto English 高拓英语',
}

export default function RootLayout({children,}: Readonly<{
  children: React.ReactNode
}>) {
  
  return (
    <html lang="en" suppressHydrationWarning>
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
      <body className={`${geistSans.variable} ${geistMono.variable} ${gtVcsBlack.variable} ${gtVcsBold.variable} ${gtVcsBook.variable}`} suppressHydrationWarning>
        <PostHogProvider>
          {children}
        </PostHogProvider>
      </body>
    </html>
  )
}
