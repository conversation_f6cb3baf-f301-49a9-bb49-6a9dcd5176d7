'use client'
import AnswerView from '@/views/answer'
import {ErrorBoundary} from '@/components/business/ErrBoundary'
import {useEffect, useState} from 'react'

const AnswerPage = () => {
  const [exId, setExId] = useState('')

  useEffect(() => {
    setExId(window?.location?.pathname?.split('/')?.[2] || '751')
  }, [])

  if (!exId) return null

  return (
    <ErrorBoundary>
      <AnswerView exId={exId} />
    </ErrorBoundary>
  )
}

export default AnswerPage
