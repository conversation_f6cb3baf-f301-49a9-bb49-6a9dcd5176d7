'use client'
import {ErrorBoundary} from '@/components/business/ErrBoundary'
import {useEffect, useState} from 'react'
import QuizNewView from '@/views/quiz-new'
import HeaderLogo from '@/components/business/HeaderLogo'
import useCheckUrlQuery from '@/hooks/useCheckQuery'
import {useNoAllowZoom, useNotAllowCopy} from '@/hooks/root'

const QuizNewPage = () => {
  useCheckUrlQuery()
  // 禁止移动端copy
  useNotAllowCopy()
  // 禁止缩放
  useNoAllowZoom()
  const [type, setType] = useState('')

  const [status, setStatus] = useState('init')

  useEffect(() => {
    const devType = process.env.NODE_ENV === 'development' ? 'Gusto_Public' : ''

    setType(window?.location?.pathname?.split('/')?.[2] || devType)
    setStatus('finish')
  }, [])

  if (!type) {
    if (status === 'finish') {
      return (
        <div className='w-[100vw] h-[100vh] flex items-center justify-center bg-gradient-to-b from-[#080808] to-[#EC2726]'>
          <HeaderLogo></HeaderLogo>
          <div className='text-center p-8'>
            <p className='font-["GTVCS-Black"] font-black text-[30px] leading-[100%] tracking-[0px] text-center text-white mb-[5px]'>当前考试不存在</p>
            <p className='font-black text-[20px] leading-[100%] tracking-[0px] text-center text-white mt-[10px]'>请检查当前url 是否是考试类型</p>
          </div>
        </div>
      )
    }

    return null
  }

  return (
    <ErrorBoundary>
      <QuizNewView quizType={type} />
    </ErrorBoundary>
  )
}

export default QuizNewPage
