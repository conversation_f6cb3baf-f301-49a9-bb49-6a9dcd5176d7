import {Answer} from '@/store/quiz'
import {request} from './api'

const PRODUCT_PREFIX  = '/gustoenglish'

const PAYMENT_PREFIX = '/payment-go'

const NORMAL_PREFIX = '/community-x'

export type BaseResponse<T> = {
  code: string
  data: T
  msg: string
}

// 提交答案
export const publishAnswerApi = (id: string,answers: Answer[]) => {
  return request.post(`${PRODUCT_PREFIX}/exams/${id}/submit`, {answers})
}

// 获取题目
export const getQuestionApi = (params: {exam_type: string}) => {
  return request.get(`${PRODUCT_PREFIX}/exams/questions`, {params})
}

// 获取考试列表
export const getExamListApi = () => {
  return request.get(`${PRODUCT_PREFIX}/exams`)
}

// 获取历史考试结果
export const getExamScoreApi = () => {
  return request.get(`${PRODUCT_PREFIX}/exams/scores/top`)
}

// 获取上传信息
export const getUploadInfoApi = (payload: {file_name: string, content_type: string, exam_id: string}, isWav?: boolean) => {
  return request.get(`${PRODUCT_PREFIX}/oss/upload/signature`, {params: {scenario: 'create_answer', ...payload}, headers: isWav ? {'Content-Type': 'audio/wav'} : {'Content-Type': 'audio/mp3'} })
}

// 获取支付信息
export const getUserPayInfo = () => {
  const params = {platform: 'wechatpay'}

  return request.get(`${PAYMENT_PREFIX}/products/third-party-pay`, {params})
}

export const loginApi = async (req: { phone: string; code: string }) => {
  return request
    .post(NORMAL_PREFIX + '/users/phoneLogin', req)
    .then(r => {
      return r
    })
    .catch(r => {
      return r
    })
}

export const sendCodeApi = async (req: { phone: string }) => {
  return request
    .post(NORMAL_PREFIX + '/users/sendSmsCode', req)
    .then(r => {
      return r
    })
    .catch(r => {
      return r
    })
}

// 查询用户信息
export const getUserInfoApi = (id: string) => {
  return request.get(`${NORMAL_PREFIX}/users/${id}`)
}

// 获取考试类型
export const getExamTypeApi = () => {
  return request.get(`${PRODUCT_PREFIX}/exam_types`)
}

// 获取用户证书信息
export const getUserMineInfoApi = () => {
  return request.get(`${NORMAL_PREFIX}${PRODUCT_PREFIX}/mine`)
}

// 获取考试最高等级
export const getExamMaxLevelApi = () => {
  return request.get(`${PRODUCT_PREFIX}/exams/scores/top`)
}

export const requestWechatNativePay = async (p?: any) => {
  return request
    .post(PAYMENT_PREFIX + '/platforms/wechatpay/prepay', {...p,trade_type: 'native'})
    .then(r => {
      return r
    })
    .catch(r => {
      return r
    })
}

export const requestWechatJsApiPay = async (p: any) => {
  return request
    .post(PAYMENT_PREFIX + '/platforms/wechatpay/prepay', {...p, trade_type: 'jsapi'})
    .then(r => {
      return r
    })
    .catch(r => {
      return r
    })
}

export const requestWechatCodeSave = async (code: string) => {
  return request
    .post(PAYMENT_PREFIX + '/platforms/wechatpay/oauth?code=' + code)
    .then(r => {
      return r
    })
    .catch(r => {
      return r
    })

}

export const requestJsApiConfig = async () => {
  return request
    .get(PAYMENT_PREFIX + '/platforms/wechatpay/jsapi-config')
    .then(r => {
      return r
    })
    .catch(r => {
      return r
    })
}

export const requestProductInfo = async (p?: {discount_code?: string}) => {
  const params = {platform: 'wechatpay'} as any

  if (p?.discount_code) {
    params['discount_code'] = p.discount_code
  }

  return request
    .get(PAYMENT_PREFIX + '/products/third-party-pay', {params})
    .then(r => {
      return r
    })
    .catch(r => {
      return r
    })
}

export const requestOrderStatus = async (order_id: string) => {
  return request
    .get(PAYMENT_PREFIX + '/orders/' + order_id, {params: {order_id} })
    .then(r => {
      return r
    })
    .catch(r => {
      return r
    })
}

export const requestOrdersRedeem = (p: {product_slug?: string; discount_code?: string}) => {
  return request
    .post(PAYMENT_PREFIX + '/orders/redeem', {...p})
    .then(r => {
      return r
    })
    .catch(r => {
      return r
    })
}

// 支付宝预支付
export const requestAlipayNativePay = async (p: {product_slug?: string; discount_code?: string, trade_type?: string}) => {
  return request
    .post(PAYMENT_PREFIX + '/platforms/alipay/prepay', {trade_type: 'wap', ...p})
    .then(r => {
      return r
    })
    .catch(r => {
      return r
    })
}

// 获取支付历史
export const getPaymentHistoryApi = () => {
  return request
    .get(PAYMENT_PREFIX + '/gustoenglish/history')
    .then(r => {
      return r
    })
    .catch(r => {
      return r
    })
}

// 获取delta期的状态
export const getDeltaStatusApi = async () => {
  return request
    .get(PAYMENT_PREFIX + '/gustoenglish/delta-status')
    .then(r => {
      return r
    })
    .catch(r => {
      return r
    })
}

// 获取题型的 提示
export const getQuestionTypeTipApi = async () => {
  return request
    .get(`${PRODUCT_PREFIX}/enums/question_types`)
    .then(r => {
      return r
    })
    .catch(r => {
      return r
    })
}

// 邮箱登录
export const loginWithEmail = (params: { email: string; code: string }) => {
  return  request.post<Partial<any>>(NORMAL_PREFIX + '/users/emailLogin', params).then(r => {
    return r
  })
    .catch(r => {
      return r
    })
}

export const requestEmailCode = (data?: {email: string}) => {
  return request.post<any>(NORMAL_PREFIX + '/users/sendEmailCode', data).then(r => {
    return r
  })
    .catch(r => {
      return r
    })
}

// 获取gem lists
export const getGemListsApi = (params?: {user_id: string, req: {limit?: number, cursor?: string}} ) => {
  return request
    .get(NORMAL_PREFIX + `/users/${params?.user_id}/gems`, {params: params?.req})
    .then(r => {
      return r
    })
    .catch(r => {
      return r
    })
}

// 获取答案详情
export const getAnswerDetailApi = (id: number) => {
  return request
    .get(`${PRODUCT_PREFIX}/exams/${id}/result`)
    .then(r => {
      return r
    }).catch(() => {
      console.info('我在补货错误')
      // return r
    })
}

// 获取gem总数
export const getGemTotalApi = (params: {user_id: string}) => {
  return request
    .get(NORMAL_PREFIX + `/users/${params?.user_id}/stats`, {params: {BeginTime: '2024-05-22 00:00:00', EndTime: new Date().toISOString()} })
    .then(r => {
      return r
    })
}

// 注册微信open_id
export const registerWechatOpenIdApi = (params: {code: string}) => {
  return request
    .post(PRODUCT_PREFIX + '/wechat/register_openid', params)
    .then(r => {
      return r
    })
    .catch(r => {
      return r
    })
}

// 获取单个题目详情
export const getQuestionDetailApi = (id: string) => {
  return request.get(`${PRODUCT_PREFIX}/questions/${id}`)
}