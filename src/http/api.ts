import * as Sentry from '@sentry/nextjs'
import {BaseHttpUrl, LoginUrl} from '@/global/consts'
import {getStorage, getStorageJson, setStorage} from '@/utils/storage'
import axios, {AxiosResponse, InternalAxiosRequestConfig} from 'axios'

// 请求前的拦截器
const requestInterceptors = (config: InternalAxiosRequestConfig) => {
  config.headers['X-5E-Token'] = getStorage('x-5e-token')
  //  || getStorage('x-5e-token')
  if (config.url && config.url.includes('/oss/upload/signature')) {
    config.headers['Content-Type'] = 'audio/mp3'
  }
  if (config.url?.includes('payment')) {
    config.headers['X-5E-APP-ID'] = 'community-x'
  }
  // console.warn(config, 'config')

  return config
}

// 响应后的拦截器
function responseInterceptors(response: AxiosResponse) {
  return response
}

const errHandler = async (error: {
  response: {status: number; config: any; data: {code: string; msg: string}}
  request: {responseURL: string}
  config: any
}) => {
  // sentry 提示
  Sentry.captureException(new Error(`Error HTTP: ${error.config.url}`), {
    contexts: {
      request: {
        body: error.response.config.data,
        url: error.response.config.url,
      },
      response: {
        status: error.response.status,
        body: error.response.data,
      },
      user: {...getStorageJson('userInfo')!, 'request-token': getStorage('x-5e-token')},
    },
  })
  if (error.response.data.code === 'TokenOutOfDate') {
    // 刷新token
    const newConfig = await refreshToken({config: error.response.config,})

    return Promise.resolve(request(newConfig))
  }
  if (error.response.data.code === 'BadToken') {
    setStorage('x-5e-token', '')
    setStorage('link_path', window?.location?.pathname)
    window.location.href = LoginUrl
  }
  if (error.response.status == 403) {
    setStorage('x-5e-token', '')
    setStorage('link_path', window?.location?.pathname)
    window.location.href = LoginUrl
  }
  console.warn(JSON.stringify(error.response))
  console.warn(JSON.stringify(error))

  return Promise.reject(error.response)
}

const init = {baseURL: BaseHttpUrl}

export const request = axios.create(init)

request.interceptors.request.use(requestInterceptors)

request.interceptors.response.use(responseInterceptors, errHandler)

export const uploadFileToOss = (file: File, url: string, isWav?: boolean) => {
  return axios.put(url, file, {headers: isWav ? {'Content-Type': 'audio/wav'} : {'Content-Type': 'audio/mpeg',},})
}

export const uploadFileToOss2 = (file: File, url: string) => {
  fetch(url, {
    method: 'PUT',
    headers: new Headers({
      'Content-Type': 'text/plain',
      // "Access-Control-Allow-Origin": "*",
    }),
    body: file,
  }).then((response) => {
    console.log(response, 'response')

    if (!response.ok) {
      throw new Error('文件上传到OSS失败')
    }
    console.log(response)

    // alert("文件已上传");
    return response
  })
}

export const refreshToken = async (payload: { config: any }) => {
  const resToken = await request.post('/community-x/users/token/refresh', {token: getStorage('x-5e-token')})

  // console.info(resToken, '---------------------------------------------------------new token')
  const newConfig = {...payload.config}

  newConfig.headers!['X-5E-TOKEN'] = resToken?.data?.data?.token
  setStorage('x-5e-token', resToken?.data?.data?.token)
  newConfig.headers!.isFlag = resToken?.data?.data?.token

  return newConfig
}