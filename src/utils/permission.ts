// 请求多媒体权限
export const requestMediaPermission = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({audio: true})

    stream.getTracks().forEach(track => track.stop()) // 立即释放资源

    return stream
  } catch (error) {
    console.info('请求媒体失败', error)
  }
}

// 获取媒体 输入出源
export const requestMediaDevices = async () => {
  try {
    const d = await navigator.mediaDevices?.enumerateDevices?.()

    const lists = Array.from(d)
    // FIX: 暂时关闭视频权限
    // console.warn(lists, 'lists')
    // const videoDevices = lists
    //   ?.filter(device => device.kind === 'videoinput')
    //   ?.map(item => ({...item, key: item.label, value: item.deviceId}))

    const audioDevices = lists
      ?.filter(device => device.kind === 'audioinput')
      ?.map(item => ({...item, key: item.label, value: item.deviceId}))

    return {videoDevices: [], audioDevices}
  } catch (error) {
    console.info('请求媒体源失败', error)
  }
}

export const isWeChatMobileOnly = () => {
  const ua = navigator.userAgent.toLowerCase()

  return /micromessenger/i.test(ua)

  // return (
  //   /micromessenger/i.test(ua) &&  // 包含微信标识
  //   !/windowswechat/i.test(ua) &&  // 排除Windows微信
  //   !/macintosh/i.test(ua)         // 排除Mac微信
  // )
}