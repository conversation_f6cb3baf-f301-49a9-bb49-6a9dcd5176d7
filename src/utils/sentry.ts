import * as Sentry from '@sentry/nextjs'

export const captureError = (error: Error | unknown, title?: string) => {
  if (error instanceof Error) {
    Sentry.captureException(error, {tags: {title: title} })
    console.error(`[${title || 'Unknown Error'}]`, error)
  } else {
    Sentry.captureMessage(String(error), {tags: {title: title} })
    console.error(`[${title || 'Unknown Error'}]`, error)
  }
}

export const captureStrMessage = (message: string, title?: string) => {
  Sentry.captureMessage(message, {tags: {title: title} })
  console.error(`[${title || 'Unknown Error'}]`, message)
}

interface ErrorContext {
  tags?: Record<string, string>;
  extras?: Record<string, any>;
  level?: Sentry.SeverityLevel;
}

/**
 * 
 * @param error 
 * @param context  tags: { component: "Checkout" },
 * extras: { cartId: "abc123" },
 * level: "fatal",
 *
 */

export const catchErrorPlus = (error: Error, context: ErrorContext = {}) => {
  Sentry.withScope((scope) => {
    // 设置上下文标签
    if (context.tags) {
      Object.entries(context.tags).forEach(([key, value]) => {
        scope.setTag(key, value)
      })
    }
    
    // 附加额外数据
    if (context.extras) {
      scope.setExtras(context.extras)
    }
  
    // 上报错误
    Sentry.captureException(error, {level: context.level || 'error',})
  })
}