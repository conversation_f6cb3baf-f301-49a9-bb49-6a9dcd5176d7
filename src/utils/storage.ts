import {captureError} from './sentry'

const isStorageDisabled = process.env.DISABLE_STORAGE === 'true'

export const getStorage = (key: string) => {
  // if (isStorageDisabled) return null
  try {
    return window.localStorage.getItem(key)    
  } catch (error) {
    //
    captureError(error, 'getStorage')
  }
}

export const getStorageStr = (key: string) => {
  try {
    return window.localStorage.getItem(key)
  } catch (error) {
    console.error(error, 'getStorageStr')

    return ''
  }
}

export const getStorageJson = (key: string) => {
  if (isStorageDisabled) return {}
  
  try {
    return JSON.parse(window.localStorage.getItem(key)!)
  } catch (error) {
    console.error(error, 'getStorageJson')

    return {}
  }
}

export const getSessionStorage = (key: string) => {
  return window.sessionStorage.getItem(key)
}

export const getSessionStorageJson = (key: string) => {
  try {
    return JSON.parse(window.sessionStorage.getItem(key)!)
  } catch (error) {
    console.error(error, 'getSessionStorageJson')

    return {}
  }
}

export const setSessionStorage = (key: string, val: any) => {
  return window.sessionStorage.setItem(key, typeof val === 'string' ? val : JSON.stringify(val))
}

export const setStorage = (key: string, val: any) => {
  if (isStorageDisabled) return

  return localStorage.setItem(key, typeof val === 'string' ? val : JSON.stringify(val))
}

export const createATag = (url: string) => {
  const a = document.createElement('a')

  a.href = url
  a.target = '_blank'
  a.click()
}
