{"name": "gusto-exam1", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev-2": "NODE_ENV=dev next dev", "dev:no-storage": "DISABLE_STORAGE=true next dev --turbopack", "dev:prod": "NODE_ENV=prod next dev --turbopack", "build:test": "NODE_ENV=test next build", "build:prod": "NODE_ENV=prod next build", "build": "next build", "start": "next start", "lint": "next lint", "fix": "npx eslint --fix ."}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.2", "@sentry/nextjs": "^8", "ali-oss": "^6.21.0", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "downloadjs": "^1.4.7", "echarts": "^5.6.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "framer-motion": "^12.18.1", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "lucide-react": "^0.456.0", "next": "15.0.3", "next-themes": "^0.4.3", "posthog-js": "^1.256.2", "prettier": "^3.3.3", "react": "19.0.0-rc-66855b96-20241106", "react-dom": "19.0.0-rc-66855b96-20241106", "react-qrcode-logo": "^3.0.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "vconsole": "^3.15.1", "zustand": "^5.0.1"}, "devDependencies": {"@types/lodash": "^4.17.14", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "15.0.3", "husky": "^9.1.7", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "typescript": "^5"}}