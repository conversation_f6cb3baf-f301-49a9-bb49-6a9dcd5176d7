{
  "extends": ["plugin:prettier/recommended", "next/core-web-vitals", "next/typescript"],
  "rules": {
    "prettier/prettier": "off", // 启用 Prettier 规则
    "semi": ["error", "never"], // 或者 "warn"
    "quotes": ["error", "single"],
    "object-curly-spacing": ["error", "never", {"objectsInObjects": true}],
    "arrow-spacing": ["error", { "before": true, "after": true }], // optional, for better readability around arrow

    "react/jsx-curly-spacing": ["error", { "when": "never", "children": true  }],
    "object-curly-newline": ["error", { "multiline": true, "minProperties": 4 }],
    "@typescript-eslint/no-explicit-any": "off",
    "no-multiple-empty-lines": ["error", {
      "max": 1, // 限制连续空行最多为1行
      "maxEOF": 0 // 文件末尾不允许空行
    }],
    "padding-line-between-statements": [
      "error",
      { "blankLine": "always", "prev": "*", "next": "return" },
      { "blankLine": "always", "prev": ["const", "let", "var"], "next": "*" }
      // ... 其他配置
    ],
    "indent": ["error", 2],
    "max-len": ["warn", {
      "code": 500,
      "tabWidth": 2
    }],
    "operator-linebreak": ["error", "after"],
    "@typescript-eslint/ban-ts-comment": "off",
    "@typescript-eslint/no-non-null-asserted-optional-chain": 0,
    "@typescript-eslint/no-require-imports": 0,
    "react/no-unescaped-entities": "off",
    "@typescript-eslint/no-unused-vars": "off"
  }
}
