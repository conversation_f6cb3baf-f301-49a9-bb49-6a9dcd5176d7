{"version": 3, "file": "common.js", "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAEA;AACA;AAAA;AAEA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAEA;AAAA;AAAA;AAGA;AAEA;;;;;;;;;;;;;;;;ACzDA;AAEA;AAIA;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AC/CA;AACA;;AAEA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACrEA;AAEA;AACA;AACA;AAeA;AACA;AACA;AAAA;AAAA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AAAA;AAAA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AAAA;AAAA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvFA;AACA;AACA;AA0BA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;ACzDA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://taro-miniprogram/._src_components_Button_index.tsx", "webpack://taro-miniprogram/._src_global_consts.ts", "webpack://taro-miniprogram/._src_http_api.ts", "webpack://taro-miniprogram/._src_http_http.ts", "webpack://taro-miniprogram/._src_store_user.ts", "webpack://taro-miniprogram/._src_utils_storage.ts"], "sourcesContent": ["import { View, Text } from '@tarojs/components'\nimport { ReactNode } from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { clsx } from 'clsx'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',\n  {\n    variants: {\n      variant: {\n        default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',\n        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'underline-offset-4 hover:underline text-primary',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 rounded-md',\n        lg: 'h-11 px-8 rounded-md',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps extends VariantProps<typeof buttonVariants> {\n  children: ReactNode\n  className?: string\n  disabled?: boolean\n  onClick?: () => void\n}\n\nexport function Button({\n  children,\n  className,\n  variant,\n  size,\n  disabled = false,\n  onClick,\n  ...props\n}: ButtonProps) {\n  return (\n    <View\n      className={clsx(buttonVariants({ variant, size, className }))}\n      onClick={disabled ? undefined : onClick}\n      {...props}\n    >\n      <Text>{children}</Text>\n    </View>\n  )\n}\n\nexport default Button\n", "export const isProd = process.env.NODE_ENV === 'production'\n\nexport const BaseHttpUrl = isProd \n  ? 'https://api.wemore.com' \n  : 'https://apitest.wemore.com'\n\nexport const LoginUrl = isProd \n  ? 'https://test.gustoenglish.com/login-quiz' \n  : 'https://gusto-english-exam-test.wemore.com/login-quiz'\n\n// 考试类型\nexport const EXAM_TYPES = {\n  MONTH_ASSESSMENT: 'month-assessment',\n  DEV_MOCK: 'dev-mock'\n}\n\n// 题目类型\nexport const QUESTION_TYPES = {\n  SINGLE_IMAGE_CHOICE: 'single-image-choice',\n  FILL_IN_BLANK: 'fill-in-blank',\n  READ_ALOUD: 'read-aloud',\n  REPEAT_SENTENCE: 'repeat-sentence',\n  WRITE_FROM_DICTATION: 'write-from-dictation',\n  SUMMARIZE_WRITTEN_TEXT: 'summarize-written-text',\n  QUICK_QA: 'quick-qa',\n  DESCRIBE_IMAGE: 'describe-image'\n}\n\n// 存储键名\nexport const STORAGE_KEYS = {\n  TOKEN: 'token',\n  USER_INFO: 'userInfo',\n  EXAM_DATA: 'examData',\n  CURRENT_QUESTION: 'currentQuestion',\n  ANSWERS: 'answers'\n}\n\n// 页面路径\nexport const PAGE_PATHS = {\n  INDEX: '/pages/index/index',\n  LOGIN: '/pages/login/index',\n  QUIZ: '/pages/quiz/index',\n  PAY: '/pages/pay/index',\n  MINE: '/pages/mine/index',\n  CERTIFICATE: '/pages/certificate/index',\n  ANSWER: '/pages/answer/index',\n  WAITING: '/pages/waiting/index'\n}\n", "import Taro from '@tarojs/taro'\nimport { getStorage } from '../utils/storage'\n\n// 创建请求配置\nconst request = {\n  baseUrl: process.env.NODE_ENV === 'production' \n    ? 'https://api.wemore.com' \n    : 'https://apitest.wemore.com',\n\n  get(url: string, data?: any) {\n    return this.request(url, 'GET', data)\n  },\n\n  post(url: string, data?: any) {\n    return this.request(url, 'POST', data)\n  },\n\n  put(url: string, data?: any) {\n    return this.request(url, 'PUT', data)\n  },\n\n  delete(url: string, data?: any) {\n    return this.request(url, 'DELETE', data)\n  },\n\n  request(url: string, method: any, data?: any) {\n    const token = getStorage('token')\n    \n    return new Promise((resolve, reject) => {\n      Taro.request({\n        url: this.baseUrl + url,\n        data,\n        method,\n        header: {\n          'Content-Type': 'application/json',\n          'Authorization': token ? `Bearer ${token}` : ''\n        },\n        success: (res) => {\n          if (res.statusCode === 401) {\n            // 未授权，跳转到登录页\n            Taro.navigateTo({\n              url: '/pages/login/index'\n            })\n            reject(new Error('未授权，请重新登录'))\n            return\n          }\n          \n          if (res.statusCode >= 200 && res.statusCode < 300) {\n            resolve(res.data)\n          } else {\n            Taro.showToast({\n              title: res.data.msg || '请求失败',\n              icon: 'none',\n              duration: 2000\n            })\n            reject(new Error(res.data.msg || '请求失败'))\n          }\n        },\n        fail: (err) => {\n          Taro.showToast({\n            title: '网络请求失败',\n            icon: 'none',\n            duration: 2000\n          })\n          reject(err)\n        }\n      })\n    })\n  }\n}\n\nexport { request }\n", "import { request } from './api'\n\nconst PRODUCT_PREFIX = '/gustoenglish'\nconst PAYMENT_PREFIX = '/payment-go'\nconst NORMAL_PREFIX = '/community-x'\n\nexport type BaseResponse<T> = {\n  code: string\n  data: T\n  msg: string\n}\n\nexport interface Answer {\n  questionId: string\n  answer: string\n  audioUrl?: string\n  duration?: number\n}\n\n// 提交答案\nexport const publishAnswerApi = (id: string, answers: Answer[]) => {\n  return request.post(`${PRODUCT_PREFIX}/exams/${id}/submit`, { answers })\n}\n\n// 获取题目\nexport const getQuestionApi = (params: { exam_type: string }) => {\n  return request.get(`${PRODUCT_PREFIX}/exams/questions`, params)\n}\n\n// 获取考试列表\nexport const getExamListApi = () => {\n  return request.get(`${PRODUCT_PREFIX}/exams`)\n}\n\n// 获取历史考试结果\nexport const getExamScoreApi = () => {\n  return request.get(`${PRODUCT_PREFIX}/exams/scores/top`)\n}\n\n// 获取答案详情\nexport const getAnswerDetailApi = (examId: string) => {\n  return request.get(`${PRODUCT_PREFIX}/exams/${examId}/answers`)\n}\n\n// 微信登录\nexport const wechatLoginApi = (code: string) => {\n  return request.post(`${NORMAL_PREFIX}/auth/wechat/miniprogram`, { code })\n}\n\n// 获取用户信息\nexport const getUserInfoApi = () => {\n  return request.get(`${NORMAL_PREFIX}/user/info`)\n}\n\n// 获取支付信息\nexport const getPaymentInfoApi = () => {\n  return request.get(`${PAYMENT_PREFIX}/products`)\n}\n\n// 创建支付订单\nexport const createPaymentOrderApi = (productId: string) => {\n  return request.post(`${PAYMENT_PREFIX}/orders`, { productId })\n}\n\n// 检查支付状态\nexport const checkPaymentStatusApi = (orderId: string) => {\n  return request.get(`${PAYMENT_PREFIX}/orders/${orderId}/status`)\n}\n\n// 上传文件\nexport const uploadFileApi = (filePath: string) => {\n  return new Promise((resolve, reject) => {\n    Taro.uploadFile({\n      url: request.baseUrl + '/upload',\n      filePath,\n      name: 'file',\n      success: (res) => {\n        try {\n          const data = JSON.parse(res.data)\n          resolve(data)\n        } catch (e) {\n          reject(new Error('上传失败'))\n        }\n      },\n      fail: reject\n    })\n  })\n}\n", "import { create } from 'zustand'\nimport { getStorage, setStorage } from '../utils/storage'\nimport { STORAGE_KEYS } from '../global/consts'\n\nexport interface UserInfo {\n  id: string\n  name: string\n  avatar: string\n  phone: string\n  email: string\n  products: string[]\n  [key: string]: any\n}\n\ninterface UserState {\n  token: string | null\n  userInfo: UserInfo | null\n  isLoggedIn: boolean\n  \n  // Actions\n  setToken: (token: string) => void\n  setUserInfo: (userInfo: UserInfo) => void\n  logout: () => void\n  \n  // Getters\n  hasProduct: (productId: string) => boolean\n}\n\nconst useUserStore = create<UserState>((set, get) => ({\n  token: getStorage(STORAGE_KEYS.TOKEN) || null,\n  userInfo: getStorage(STORAGE_KEYS.USER_INFO) ? JSON.parse(getStorage(STORAGE_KEYS.USER_INFO)) : null,\n  isLoggedIn: !!getStorage(STORAGE_KEYS.TOKEN),\n  \n  // Actions\n  setToken: (token) => {\n    setStorage(STORAGE_KEYS.TOKEN, token)\n    set({ token, isLoggedIn: true })\n  },\n  \n  setUserInfo: (userInfo) => {\n    setStorage(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo))\n    set({ userInfo })\n  },\n  \n  logout: () => {\n    setStorage(STORAGE_KEYS.TOKEN, '')\n    setStorage(STORAGE_KEYS.USER_INFO, '')\n    set({ token: null, userInfo: null, isLoggedIn: false })\n  },\n  \n  // Getters\n  hasProduct: (productId) => {\n    const { userInfo } = get()\n    return userInfo?.products?.includes(productId) || false\n  }\n}))\n\nexport default useUserStore\n", "import Taro from '@tarojs/taro'\n\n// 设置存储\nexport const setStorage = (key: string, value: any) => {\n  try {\n    Taro.setStorageSync(key, value)\n  } catch (error) {\n    console.error('设置存储失败:', error)\n  }\n}\n\n// 获取存储\nexport const getStorage = (key: string) => {\n  try {\n    return Taro.getStorageSync(key)\n  } catch (error) {\n    console.error('获取存储失败:', error)\n    return null\n  }\n}\n\n// 获取JSON格式存储\nexport const getStorageJson = (key: string) => {\n  try {\n    const value = Taro.getStorageSync(key)\n    return value ? JSON.parse(value) : null\n  } catch (error) {\n    console.error('获取JSON存储失败:', error)\n    return null\n  }\n}\n\n// 设置JSON格式存储\nexport const setStorageJson = (key: string, value: any) => {\n  try {\n    Taro.setStorageSync(key, JSON.stringify(value))\n  } catch (error) {\n    console.error('设置JSON存储失败:', error)\n  }\n}\n\n// 移除存储\nexport const removeStorage = (key: string) => {\n  try {\n    Taro.removeStorageSync(key)\n  } catch (error) {\n    console.error('移除存储失败:', error)\n  }\n}\n\n// 清空存储\nexport const clearStorage = () => {\n  try {\n    Taro.clearStorageSync()\n  } catch (error) {\n    console.error('清空存储失败:', error)\n  }\n}\n"], "names": [], "sourceRoot": ""}