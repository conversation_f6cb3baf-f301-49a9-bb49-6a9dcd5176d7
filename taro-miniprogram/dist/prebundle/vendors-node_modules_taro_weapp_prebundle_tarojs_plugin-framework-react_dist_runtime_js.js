"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["vendors-node_modules_taro_weapp_prebundle_tarojs_plugin-framework-react_dist_runtime_js"],{

/***/ "./node_modules/.taro/weapp/prebundle/@tarojs_plugin-framework-react_dist_runtime.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/.taro/weapp/prebundle/@tarojs_plugin-framework-react_dist_runtime.js ***!
  \*******************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   connectReactPage: function() { return /* binding */ connectReactPage; },
/* harmony export */   createH5NativeComponentConfig: function() { return /* binding */ createH5NativeComponentConfig; },
/* harmony export */   createNativeComponentConfig: function() { return /* binding */ createNativeComponentConfig; },
/* harmony export */   createNativePageConfig: function() { return /* binding */ createNativePageConfig; },
/* harmony export */   createReactApp: function() { return /* binding */ createReactApp; },
/* harmony export */   setReconciler: function() { return /* binding */ setReconciler; },
/* harmony export */   useAddToFavorites: function() { return /* binding */ useAddToFavorites; },
/* harmony export */   useDidHide: function() { return /* binding */ useDidHide; },
/* harmony export */   useDidShow: function() { return /* binding */ useDidShow; },
/* harmony export */   useError: function() { return /* binding */ useError; },
/* harmony export */   useLaunch: function() { return /* binding */ useLaunch; },
/* harmony export */   useLoad: function() { return /* binding */ useLoad; },
/* harmony export */   useOptionMenuClick: function() { return /* binding */ useOptionMenuClick; },
/* harmony export */   usePageNotFound: function() { return /* binding */ usePageNotFound; },
/* harmony export */   usePageScroll: function() { return /* binding */ usePageScroll; },
/* harmony export */   usePullDownRefresh: function() { return /* binding */ usePullDownRefresh; },
/* harmony export */   usePullIntercept: function() { return /* binding */ usePullIntercept; },
/* harmony export */   useReachBottom: function() { return /* binding */ useReachBottom; },
/* harmony export */   useReady: function() { return /* binding */ useReady; },
/* harmony export */   useResize: function() { return /* binding */ useResize; },
/* harmony export */   useRouter: function() { return /* binding */ useRouter; },
/* harmony export */   useSaveExitState: function() { return /* binding */ useSaveExitState; },
/* harmony export */   useScope: function() { return /* binding */ useScope; },
/* harmony export */   useShareAppMessage: function() { return /* binding */ useShareAppMessage; },
/* harmony export */   useShareTimeline: function() { return /* binding */ useShareTimeline; },
/* harmony export */   useTabItemTap: function() { return /* binding */ useTabItemTap; },
/* harmony export */   useTitleClick: function() { return /* binding */ useTitleClick; },
/* harmony export */   useUnhandledRejection: function() { return /* binding */ useUnhandledRejection; },
/* harmony export */   useUnload: function() { return /* binding */ useUnload; }
/* harmony export */ });
/* harmony import */ var _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-4JFQ53LR.js */ "./node_modules/.taro/weapp/prebundle/chunk-4JFQ53LR.js");
/* harmony import */ var _chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-7MJDXN2B.js */ "./node_modules/.taro/weapp/prebundle/chunk-7MJDXN2B.js");
/* harmony import */ var _chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-QRPWKJ4C.js */ "./node_modules/.taro/weapp/prebundle/chunk-QRPWKJ4C.js");



// node_modules/@tarojs/plugin-framework-react/dist/runtime.js
(0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.init_dist)();
(0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.init_dist)();
var reactMeta = {
    PageContext: _chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.EMPTY_OBJ,
    R: _chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.EMPTY_OBJ
};
var HOOKS_APP_ID = "taro-app";
function isClassComponent(R, component) {
    var _a;
    const prototype = component.prototype;
    if ((_a = component.displayName) === null || _a === void 0 ? void 0 : _a.includes("Connect")) return false;
    return (0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.isFunction)(component.render) || !!(prototype === null || prototype === void 0 ? void 0 : prototype.isReactComponent) || prototype instanceof R.Component;
}
function ensureIsArray(item) {
    if ((0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.isArray)(item)) {
        return item;
    } else {
        return item ? [
            item
        ] : [];
    }
}
function setDefaultDescriptor(obj) {
    obj.writable = true;
    obj.enumerable = true;
    return obj;
}
function setRouterParams(options) {
    _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.router = Object.assign({
        params: options === null || options === void 0 ? void 0 : options.query
    }, options);
}
var createTaroHook = (lifecycle)=>{
    return (fn)=>{
        const { R: React, PageContext } = reactMeta;
        const id = React.useContext(PageContext) || HOOKS_APP_ID;
        const instRef = React.useRef();
        const fnRef = React.useRef(fn);
        if (fnRef.current !== fn) fnRef.current = fn;
        React.useLayoutEffect(()=>{
            let inst = instRef.current = (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.getPageInstance)(id);
            let first = false;
            if (!inst) {
                first = true;
                instRef.current = /* @__PURE__ */ Object.create(null);
                inst = instRef.current;
            }
            const callback = (...args)=>fnRef.current(...args);
            if ((0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.isFunction)(inst[lifecycle])) {
                inst[lifecycle] = [
                    inst[lifecycle],
                    callback
                ];
            } else {
                inst[lifecycle] = [
                    ...inst[lifecycle] || [],
                    callback
                ];
            }
            if (first) {
                (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.injectPageInstance)(inst, id);
            }
            return ()=>{
                const inst2 = instRef.current;
                if (!inst2) return;
                const list = inst2[lifecycle];
                if (list === callback) {
                    inst2[lifecycle] = void 0;
                } else if ((0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.isArray)(list)) {
                    inst2[lifecycle] = list.filter((item)=>item !== callback);
                }
                instRef.current = void 0;
            };
        }, []);
    };
};
var useDidHide = createTaroHook("componentDidHide");
var useDidShow = createTaroHook("componentDidShow");
var useError = createTaroHook("onError");
var useUnhandledRejection = createTaroHook("onUnhandledRejection");
var useLaunch = createTaroHook("onLaunch");
var usePageNotFound = createTaroHook("onPageNotFound");
var useLoad = createTaroHook("onLoad");
var usePageScroll = createTaroHook("onPageScroll");
var usePullDownRefresh = createTaroHook("onPullDownRefresh");
var usePullIntercept = createTaroHook("onPullIntercept");
var useReachBottom = createTaroHook("onReachBottom");
var useResize = createTaroHook("onResize");
var useUnload = createTaroHook("onUnload");
var useAddToFavorites = createTaroHook("onAddToFavorites");
var useOptionMenuClick = createTaroHook("onOptionMenuClick");
var useSaveExitState = createTaroHook("onSaveExitState");
var useShareAppMessage = createTaroHook("onShareAppMessage");
var useShareTimeline = createTaroHook("onShareTimeline");
var useTitleClick = createTaroHook("onTitleClick");
var useReady = createTaroHook("onReady");
var useRouter = (dynamic = false)=>{
    const React = reactMeta.R;
    return dynamic ? _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.router : React.useMemo(()=>_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.router, []);
};
var useTabItemTap = createTaroHook("onTabItemTap");
var useScope = ()=>void 0;
var taroHooks = Object.freeze({
    __proto__: null,
    useAddToFavorites,
    useDidHide,
    useDidShow,
    useError,
    useLaunch,
    useLoad,
    useOptionMenuClick,
    usePageNotFound,
    usePageScroll,
    usePullDownRefresh,
    usePullIntercept,
    useReachBottom,
    useReady,
    useResize,
    useRouter,
    useSaveExitState,
    useScope,
    useShareAppMessage,
    useShareTimeline,
    useTabItemTap,
    useTitleClick,
    useUnhandledRejection,
    useUnload
});
var h$1;
var ReactDOM$1;
var Fragment;
var pageKeyId = (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.incrementId)();
function setReconciler(ReactDOM2) {
    _chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.hooks.tap("getLifecycle", function(instance, lifecycle) {
        lifecycle = lifecycle.replace(/^on(Show|Hide)$/, "componentDid$1");
        return instance[lifecycle];
    });
    _chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.hooks.tap("modifyMpEvent", function(event) {
        Object.defineProperty(event, "type", {
            value: event.type.replace(/-/g, "")
        });
    });
    _chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.hooks.tap("batchedEventUpdates", function(cb) {
        ReactDOM2 === null || ReactDOM2 === void 0 ? void 0 : ReactDOM2.unstable_batchedUpdates(cb);
    });
    _chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.hooks.tap("mergePageInstance", function(prev, next) {
        if (!prev || !next) return;
        if ("constructor" in prev) return;
        Object.keys(prev).forEach((item)=>{
            const prevList = prev[item];
            const nextList = ensureIsArray(next[item]);
            next[item] = nextList.concat(prevList);
        });
    });
    if (false) {}
}
function connectReactPage(R, id) {
    return (Page)=>{
        const isReactComponent = isClassComponent(R, Page);
        const inject = (node)=>node && (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.injectPageInstance)(node, id);
        const refs = isReactComponent ? {
            ref: inject
        } : {
            forwardedRef: inject,
            // 兼容 react-redux 7.20.1+
            reactReduxForwardedRef: inject
        };
        if (reactMeta.PageContext === _chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.EMPTY_OBJ) {
            reactMeta.PageContext = R.createContext("");
        }
        return class PageWrapper extends R.Component {
            static getDerivedStateFromError(error) {
                var _a, _b;
                (_b = (_a = _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.app) === null || _a === void 0 ? void 0 : _a.onError) === null || _b === void 0 ? void 0 : _b.call(_a, error.message + error.stack);
                return {
                    hasError: true
                };
            }
            // React 16 uncaught error 会导致整个应用 crash，
            // 目前把错误缩小到页面
            componentDidCatch(error, info) {
                if (true) {
                    console.warn(error);
                    console.error(info.componentStack);
                }
            }
            render() {
                const children = this.state.hasError ? [] : h$1(reactMeta.PageContext.Provider, {
                    value: id
                }, h$1(Page, Object.assign(Object.assign({}, this.props), refs)));
                if (false) {} else {
                    return h$1("root", {
                        id
                    }, children);
                }
            }
            constructor(){
                super(...arguments);
                this.state = {
                    hasError: false
                };
            }
        };
    };
}
function createReactApp(App, react, dom, config) {
    if (true) {
        (0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.ensure)(!!dom, "\u6784\u5EFA React/Preact \u9879\u76EE\u8BF7\u628A process.env.FRAMEWORK \u8BBE\u7F6E\u4E3A 'react'/'preact' ");
    }
    reactMeta.R = react;
    h$1 = react.createElement;
    ReactDOM$1 = dom;
    Fragment = react.Fragment;
    const appInstanceRef = react.createRef();
    const isReactComponent = isClassComponent(react, App);
    let appWrapper;
    let appWrapperResolver;
    const appWrapperPromise = new Promise((resolve)=>appWrapperResolver = resolve);
    setReconciler(ReactDOM$1);
    function getAppInstance() {
        return appInstanceRef.current;
    }
    function waitAppWrapper(cb) {
        appWrapperPromise.then(()=>cb());
    }
    function renderReactRoot() {
        var _a, _b;
        const appId = (config === null || config === void 0 ? void 0 : config.appId) || "app";
        let container = _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.taroDocumentProvider.getElementById(appId);
        if (container == null) {
            const appContainer = _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.taroDocumentProvider.getElementById(_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.CONTAINER);
            container = _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.taroDocumentProvider.createElement(appId);
            container.id = appId;
            appContainer === null || appContainer === void 0 ? void 0 : appContainer.appendChild(container);
        }
        if ((react.version || "").startsWith("18")) {
            const root = ReactDOM$1.createRoot(container);
            (_a = root.render) === null || _a === void 0 ? void 0 : _a.call(root, h$1(AppWrapper));
        } else {
            (_b = ReactDOM$1.render) === null || _b === void 0 ? void 0 : _b.call(ReactDOM$1, h$1(AppWrapper), container);
        }
    }
    class AppWrapper extends react.Component {
        mount(pageComponent, id, cb) {
            const pageWrapper = connectReactPage(react, id)(pageComponent);
            const key = id + pageKeyId();
            const page = ()=>h$1(pageWrapper, {
                    key,
                    tid: id
                });
            this.pages.push(page);
            this.forceUpdate((...args)=>{
                _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.perf.stop(_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.PAGE_INIT);
                return cb(...args);
            });
        }
        unmount(id, cb) {
            const elements = this.elements;
            const idx = elements.findIndex((item)=>item.props.tid === id);
            elements.splice(idx, 1);
            this.forceUpdate(cb);
        }
        render() {
            const { pages, elements } = this;
            while(pages.length > 0){
                const page = pages.pop();
                elements.push(page());
            }
            let props = null;
            if (isReactComponent) {
                props = {
                    ref: appInstanceRef
                };
            }
            return h$1(App, props,  false ? 0 : elements.slice());
        }
        constructor(props){
            super(props);
            this.pages = [];
            this.elements = [];
            appWrapper = this;
            appWrapperResolver(this);
        }
    }
    if (true) {
        renderReactRoot();
    }
    const [ONLAUNCH, ONSHOW, ONHIDE] = _chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.hooks.call("getMiniLifecycleImpl").app;
    const appObj = Object.create({
        render (cb) {
            appWrapper.forceUpdate(cb);
        },
        mount (component, id, cb) {
            if (appWrapper) {
                appWrapper.mount(component, id, cb);
            } else {
                appWrapperPromise.then((appWrapper2)=>appWrapper2.mount(component, id, cb));
            }
        },
        unmount (id, cb) {
            if (appWrapper) {
                appWrapper.unmount(id, cb);
            } else {
                appWrapperPromise.then((appWrapper2)=>appWrapper2.unmount(id, cb));
            }
        }
    }, {
        config: setDefaultDescriptor({
            configurable: true,
            value: config
        }),
        [ONLAUNCH]: setDefaultDescriptor({
            value (options) {
                setRouterParams(options);
                if (false) {}
                const onLaunch = ()=>{
                    var _a;
                    const app = getAppInstance();
                    this.$app = app;
                    if (app) {
                        if (app.taroGlobalData) {
                            const globalData = app.taroGlobalData;
                            const keys = Object.keys(globalData);
                            const descriptors = Object.getOwnPropertyDescriptors(globalData);
                            keys.forEach((key)=>{
                                Object.defineProperty(this, key, {
                                    configurable: true,
                                    enumerable: true,
                                    get () {
                                        return globalData[key];
                                    },
                                    set (value) {
                                        globalData[key] = value;
                                    }
                                });
                            });
                            Object.defineProperties(this, descriptors);
                        }
                        (_a = app.onLaunch) === null || _a === void 0 ? void 0 : _a.call(app, options);
                    }
                    triggerAppHook("onLaunch", options);
                };
                waitAppWrapper(onLaunch);
            }
        }),
        [ONSHOW]: setDefaultDescriptor({
            value (options) {
                setRouterParams(options);
                const onShow = ()=>{
                    var _a;
                    const app = getAppInstance();
                    (_a = app === null || app === void 0 ? void 0 : app.componentDidShow) === null || _a === void 0 ? void 0 : _a.call(app, options);
                    triggerAppHook("onShow", options);
                };
                waitAppWrapper(onShow);
            }
        }),
        [ONHIDE]: setDefaultDescriptor({
            value () {
                const onHide = ()=>{
                    var _a;
                    const app = getAppInstance();
                    (_a = app === null || app === void 0 ? void 0 : app.componentDidHide) === null || _a === void 0 ? void 0 : _a.call(app);
                    triggerAppHook("onHide");
                };
                waitAppWrapper(onHide);
            }
        }),
        onError: setDefaultDescriptor({
            value (error) {
                const onError = ()=>{
                    var _a;
                    const app = getAppInstance();
                    (_a = app === null || app === void 0 ? void 0 : app.onError) === null || _a === void 0 ? void 0 : _a.call(app, error);
                    triggerAppHook("onError", error);
                    if (error === null || error === void 0 ? void 0 : error.includes("Minified React error")) {
                        console.warn("React \u51FA\u73B0\u62A5\u9519\uFF0C\u8BF7\u6253\u5F00\u7F16\u8BD1\u914D\u7F6E mini.debugReact \u67E5\u770B\u62A5\u9519\u8BE6\u60C5\uFF1Ahttps://docs.taro.zone/docs/config-detail#minidebugreact");
                    }
                };
                waitAppWrapper(onError);
            }
        }),
        onUnhandledRejection: setDefaultDescriptor({
            value (res) {
                const onUnhandledRejection = ()=>{
                    var _a;
                    const app = getAppInstance();
                    (_a = app === null || app === void 0 ? void 0 : app.onUnhandledRejection) === null || _a === void 0 ? void 0 : _a.call(app, res);
                    triggerAppHook("onUnhandledRejection", res);
                };
                waitAppWrapper(onUnhandledRejection);
            }
        }),
        onPageNotFound: setDefaultDescriptor({
            value (res) {
                const onPageNotFound = ()=>{
                    var _a;
                    const app = getAppInstance();
                    (_a = app === null || app === void 0 ? void 0 : app.onPageNotFound) === null || _a === void 0 ? void 0 : _a.call(app, res);
                    triggerAppHook("onPageNotFound", res);
                };
                waitAppWrapper(onPageNotFound);
            }
        })
    });
    function triggerAppHook(lifecycle, ...option) {
        const instance = (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.getPageInstance)(HOOKS_APP_ID);
        if (instance) {
            const app = getAppInstance();
            const func = _chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.hooks.call("getLifecycle", instance, lifecycle);
            if (Array.isArray(func)) {
                func.forEach((cb)=>cb.apply(app, option));
            }
        }
    }
    _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.app = appObj;
    return appObj;
}
var getNativeCompId = (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.incrementId)();
var h;
var ReactDOM;
var nativeComponentApp;
function initNativeComponentEntry(params) {
    var _a;
    const { R, ReactDOM: ReactDOM2, cb, isDefaultEntryDom = true } = params;
    class NativeComponentWrapper extends R.Component {
        componentDidMount() {
            this.ctx.component = this;
            const rootElement = this.root.current;
            rootElement.ctx = this.ctx;
            rootElement.performUpdate(true);
        }
        render() {
            return h("root", {
                ref: this.root,
                id: this.props.compId
            }, this.props.renderComponent(this.ctx));
        }
        constructor(){
            super(...arguments);
            this.root = R.createRef();
            this.ctx = this.props.getCtx();
        }
    }
    class Entry extends R.Component {
        componentDidMount() {
            if (isDefaultEntryDom) {
                _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.app = this;
            } else {
                nativeComponentApp = this;
            }
            cb && cb();
        }
        mount(Component, compId, getCtx, cb2) {
            const isReactComponent = isClassComponent(R, Component);
            const inject = (node)=>node && (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.injectPageInstance)(node, compId);
            const refs = isReactComponent ? {
                ref: inject
            } : {
                forwardedRef: inject,
                reactReduxForwardedRef: inject
            };
            if (reactMeta.PageContext === _chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.EMPTY_OBJ) {
                reactMeta.PageContext = R.createContext("");
            }
            const item = {
                compId,
                element: h(NativeComponentWrapper, {
                    key: compId,
                    compId,
                    getCtx,
                    renderComponent (ctx) {
                        return h(reactMeta.PageContext.Provider, {
                            value: compId
                        }, h(Component, Object.assign(Object.assign(Object.assign({}, (ctx.data || (ctx.data = {})).props), refs), {
                            $scope: ctx
                        })));
                    }
                })
            };
            this.setState({
                components: [
                    ...this.state.components,
                    item
                ]
            }, ()=>cb2 && cb2());
        }
        unmount(compId, cb2) {
            const components = this.state.components;
            const index = components.findIndex((item)=>item.compId === compId);
            const next = [
                ...components.slice(0, index),
                ...components.slice(index + 1)
            ];
            this.setState({
                components: next
            }, ()=>{
                (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.removePageInstance)(compId);
                cb2 && cb2();
            });
        }
        render() {
            const components = this.state.components;
            return components.map(({ element })=>element);
        }
        constructor(){
            super(...arguments);
            this.state = {
                components: []
            };
        }
    }
    setReconciler(ReactDOM2);
    let app = _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.taroDocumentProvider.getElementById("app");
    if (!isDefaultEntryDom && !nativeComponentApp) {
        const nativeApp = _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.taroDocumentProvider.createElement("nativeComponent");
        (_a = app === null || app === void 0 ? void 0 : app.parentNode) === null || _a === void 0 ? void 0 : _a.appendChild(nativeApp);
        app = nativeApp;
    }
    ReactDOM2.render(h(Entry, {}), app);
}
function createNativePageConfig(Component, pageName, data, react, reactDOM, pageConfig) {
    reactMeta.R = react;
    h = react.createElement;
    ReactDOM = reactDOM;
    setReconciler(ReactDOM);
    const [ONLOAD, ONUNLOAD, ONREADY, ONSHOW, ONHIDE, LIFECYCLES, SIDE_EFFECT_LIFECYCLES] = _chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.hooks.call("getMiniLifecycleImpl").page;
    let unmounting = false;
    let prepareMountList = [];
    let pageElement = null;
    let loadResolver;
    let hasLoaded;
    const id = pageName !== null && pageName !== void 0 ? pageName : `taro_page_${getNativeCompId()}`;
    function setCurrentRouter(page) {
        const router = page.route || page.__route__ || page.$taroPath;
        _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.router = {
            params: page.$taroParams,
            path: (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.addLeadingSlash)(router),
            $taroPath: page.$taroPath,
            onReady: (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.getOnReadyEventKey)(id),
            onShow: (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.getOnShowEventKey)(id),
            onHide: (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.getOnHideEventKey)(id)
        };
        if (!(0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.isUndefined)(page.exitState)) {
            _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.router.exitState = page.exitState;
        }
    }
    const pageObj = {
        options: pageConfig,
        [ONLOAD] (options = {}, cb) {
            hasLoaded = new Promise((resolve)=>{
                loadResolver = resolve;
            });
            _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.page = this;
            this.config = pageConfig || {};
            const uniqueOptions = Object.assign({}, options, {
                $taroTimestamp: Date.now()
            });
            const $taroPath = this.$taroPath = (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.getPath)(id, uniqueOptions);
            if (this.$taroParams == null) {
                this.$taroParams = uniqueOptions;
            }
            setCurrentRouter(this);
            _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.taroWindowProvider.trigger(_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.CONTEXT_ACTIONS.INIT, $taroPath);
            const mountCallback = ()=>{
                pageElement = _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.taroDocumentProvider.getElementById($taroPath);
                (0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.ensure)(pageElement !== null, "\u6CA1\u6709\u627E\u5230\u9875\u9762\u5B9E\u4F8B\u3002");
                (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.safeExecute)($taroPath, ONLOAD, this.$taroParams);
                loadResolver();
                pageElement.ctx = this;
                pageElement.performUpdate(true, cb);
            };
            const mount = ()=>{
                if (!_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.app) {
                    initNativeComponentEntry({
                        R: react,
                        ReactDOM,
                        cb: ()=>{
                            _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.app.mount(Component, $taroPath, ()=>this, mountCallback);
                        }
                    });
                } else {
                    _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.app.mount(Component, $taroPath, ()=>this, mountCallback);
                }
            };
            if (unmounting) {
                prepareMountList.push(mount);
            } else {
                mount();
            }
        },
        [ONUNLOAD] () {
            const $taroPath = this.$taroPath;
            _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.taroWindowProvider.trigger(_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.CONTEXT_ACTIONS.DESTORY, $taroPath);
            (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.safeExecute)($taroPath, ONUNLOAD);
            resetCurrent();
            unmounting = true;
            _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.app.unmount($taroPath, ()=>{
                unmounting = false;
                (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.removePageInstance)($taroPath);
                if (pageElement) {
                    pageElement.ctx = null;
                    pageElement = null;
                }
                if (prepareMountList.length) {
                    prepareMountList.forEach((fn)=>fn());
                    prepareMountList = [];
                }
            });
        },
        [ONREADY] () {
            hasLoaded.then(()=>{
                (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.safeExecute)(this.$taroPath, _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.ON_READY);
                (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__._raf)(()=>_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.eventCenter.trigger((0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.getOnReadyEventKey)(id)));
                this.onReady.called = true;
            });
        },
        [ONSHOW] (options = {}) {
            hasLoaded.then(()=>{
                _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.page = this;
                setCurrentRouter(this);
                _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.taroWindowProvider.trigger(_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.CONTEXT_ACTIONS.RECOVER, this.$taroPath);
                (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.safeExecute)(this.$taroPath, _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.ON_SHOW, options);
                (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__._raf)(()=>_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.eventCenter.trigger((0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.getOnShowEventKey)(id)));
            });
        },
        [ONHIDE] () {
            _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.taroWindowProvider.trigger(_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.CONTEXT_ACTIONS.RESTORE, this.$taroPath);
            if (_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.page === this) {
                _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.page = null;
                _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.router = null;
            }
            (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.safeExecute)(this.$taroPath, _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.ON_HIDE);
            _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.eventCenter.trigger((0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.getOnHideEventKey)(id));
        }
    };
    function resetCurrent() {
        _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.page = null;
        _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.router = null;
    }
    LIFECYCLES.forEach((lifecycle)=>{
        pageObj[lifecycle] = function() {
            return (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.safeExecute)(this.$taroPath, lifecycle, ...arguments);
        };
    });
    SIDE_EFFECT_LIFECYCLES.forEach((lifecycle)=>{
        var _a;
        if (Component[lifecycle] || ((_a = Component.prototype) === null || _a === void 0 ? void 0 : _a[lifecycle]) || Component[lifecycle.replace(/^on/, "enable")]) {
            pageObj[lifecycle] = function(...args) {
                var _a2;
                const target = (_a2 = args[0]) === null || _a2 === void 0 ? void 0 : _a2.target;
                if (target === null || target === void 0 ? void 0 : target.id) {
                    const id2 = target.id;
                    const element = _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.taroDocumentProvider.getElementById(id2);
                    if (element) {
                        target.dataset = element.dataset;
                    }
                }
                return (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.safeExecute)(this.$taroPath, lifecycle, ...args);
            };
        }
    });
    pageObj.eh = _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.eventHandler;
    if (!(0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.isUndefined)(data)) {
        pageObj.data = data;
    }
    _chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.hooks.call("modifyPageObject", pageObj);
    return pageObj;
}
function createH5NativeComponentConfig(Component, react, reactdom) {
    reactMeta.R = react;
    h = react.createElement;
    ReactDOM = reactdom;
    setReconciler(ReactDOM);
    return Component;
}
function createNativeComponentConfig(Component, react, reactdom, componentConfig) {
    var _a, _b;
    reactMeta.R = react;
    h = react.createElement;
    ReactDOM = reactdom;
    setReconciler(ReactDOM);
    const { isNewBlended } = componentConfig;
    const componentObj = {
        options: componentConfig,
        properties: {
            props: {
                type: null,
                value: null,
                observer (_newVal, oldVal) {
                    var _a2, _b2, _c, _d;
                    if (false) {}
                    oldVal && ((_d = this.component) === null || _d === void 0 ? void 0 : _d.forceUpdate());
                }
            }
        },
        created () {
            var _a2, _b2;
            if (false) {}
            const app = isNewBlended ? nativeComponentApp : _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.app;
            if (!app) {
                initNativeComponentEntry({
                    R: react,
                    ReactDOM,
                    isDefaultEntryDom: !isNewBlended
                });
            }
        },
        attached () {
            const compId = this.compId = getNativeCompId();
            setCurrent(compId);
            this.config = componentConfig;
            const app = isNewBlended ? nativeComponentApp : _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.app;
            app.mount(Component, compId, ()=>this, ()=>{
                const instance = (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.getPageInstance)(compId);
                if (instance && instance.node) {
                    const el = _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.taroDocumentProvider.getElementById(instance.node.uid);
                    if (el) {
                        el.ctx = this;
                    }
                }
            });
        },
        ready () {
            (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.safeExecute)(this.compId, "onReady");
        },
        detached () {
            resetCurrent();
            const app = isNewBlended ? nativeComponentApp : _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.app;
            app.unmount(this.compId);
        },
        pageLifetimes: {
            show (options) {
                (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.safeExecute)(this.compId, "onShow", options);
            },
            hide () {
                (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.safeExecute)(this.compId, "onHide");
            }
        },
        methods: {
            eh: _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.eventHandler,
            onLoad (options) {
                (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.safeExecute)(this.compId, "onLoad", options);
            },
            onUnload () {
                (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.safeExecute)(this.compId, "onUnload");
            }
        }
    };
    function resetCurrent() {
        _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.page = null;
        _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.router = null;
    }
    if (Component.onShareAppMessage || ((_a = Component.prototype) === null || _a === void 0 ? void 0 : _a.onShareAppMessage) || Component.enableShareAppMessage) {
        componentObj.methods.onShareAppMessage = function(options) {
            const target = options === null || options === void 0 ? void 0 : options.target;
            if (target) {
                const id = target.id;
                const element = _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.taroDocumentProvider.getElementById(id);
                if (element) {
                    target.dataset = element.dataset;
                }
            }
            return (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.safeExecute)(this.compId, "onShareAppMessage", options);
        };
    }
    if (Component.onShareTimeline || ((_b = Component.prototype) === null || _b === void 0 ? void 0 : _b.onShareTimeline) || Component.enableShareTimeline) {
        componentObj.methods.onShareTimeline = function() {
            return (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.safeExecute)(this.compId, "onShareTimeline");
        };
    }
    if (false) {}
    return componentObj;
}
function setCurrent(compId) {
    if (!getCurrentPages || typeof getCurrentPages !== "function") return;
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    if (_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.page === currentPage) return;
    _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.page = currentPage;
    const route = currentPage.route || currentPage.__route__;
    const router = {
        params: currentPage.options || {},
        path: (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.addLeadingSlash)(route),
        $taroPath: compId,
        onReady: "",
        onHide: "",
        onShow: ""
    };
    _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current.router = router;
    if (!currentPage.options) {
        Object.defineProperty(currentPage, "options", {
            enumerable: true,
            configurable: true,
            get () {
                return this._optionsValue;
            },
            set (value) {
                router.params = value;
                this._optionsValue = value;
            }
        });
    }
}
_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.hooks.tap("initNativeApi", function(taro) {
    for(const hook in taroHooks){
        taro[hook] = taroHooks[hook];
    }
});
if (false) {}



/***/ })

}]);