"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["common"],{

/***/ "./src/components/Button/index.tsx":
/*!*****************************************!*\
  !*** ./src/components/Button/index.tsx ***!
  \*****************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* unused harmony export Button */
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! class-variance-authority */ "webpack/container/remote/class-variance-authority");
/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(class_variance_authority__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ "webpack/container/remote/clsx");
/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(clsx__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ "webpack/container/remote/react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);




const buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_0__.cva)('inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background', {
  variants: {
    variant: {
      default: 'bg-primary text-primary-foreground hover:bg-primary/90',
      destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
      outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
      ghost: 'hover:bg-accent hover:text-accent-foreground',
      link: 'underline-offset-4 hover:underline text-primary'
    },
    size: {
      default: 'h-10 py-2 px-4',
      sm: 'h-9 px-3 rounded-md',
      lg: 'h-11 px-8 rounded-md'
    }
  },
  defaultVariants: {
    variant: 'default',
    size: 'default'
  }
});
function Button({
  children,
  className,
  variant,
  size,
  disabled = false,
  onClick,
  ...props
}) {
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
    className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(buttonVariants({
      variant,
      size,
      className
    })),
    onClick: disabled ? undefined : onClick,
    ...props,
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.Text, {
      children: children
    })
  });
}
/* harmony default export */ __webpack_exports__["default"] = (Button);

/***/ }),

/***/ "./src/global/consts.ts":
/*!******************************!*\
  !*** ./src/global/consts.ts ***!
  \******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EXAM_TYPES: function() { return /* binding */ EXAM_TYPES; },
/* harmony export */   PAGE_PATHS: function() { return /* binding */ PAGE_PATHS; },
/* harmony export */   STORAGE_KEYS: function() { return /* binding */ STORAGE_KEYS; }
/* harmony export */ });
/* unused harmony exports isProd, BaseHttpUrl, LoginUrl, QUESTION_TYPES */
const isProd = "development" === 'production';
const BaseHttpUrl = isProd ? 'https://api.wemore.com' : 'https://apitest.wemore.com';
const LoginUrl = isProd ? 'https://test.gustoenglish.com/login-quiz' : 'https://gusto-english-exam-test.wemore.com/login-quiz';

// 考试类型
const EXAM_TYPES = {
  MONTH_ASSESSMENT: 'month-assessment',
  DEV_MOCK: 'dev-mock'
};

// 题目类型
const QUESTION_TYPES = {
  SINGLE_IMAGE_CHOICE: 'single-image-choice',
  FILL_IN_BLANK: 'fill-in-blank',
  READ_ALOUD: 'read-aloud',
  REPEAT_SENTENCE: 'repeat-sentence',
  WRITE_FROM_DICTATION: 'write-from-dictation',
  SUMMARIZE_WRITTEN_TEXT: 'summarize-written-text',
  QUICK_QA: 'quick-qa',
  DESCRIBE_IMAGE: 'describe-image'
};

// 存储键名
const STORAGE_KEYS = {
  TOKEN: 'token',
  USER_INFO: 'userInfo',
  EXAM_DATA: 'examData',
  CURRENT_QUESTION: 'currentQuestion',
  ANSWERS: 'answers'
};

// 页面路径
const PAGE_PATHS = {
  INDEX: '/pages/index/index',
  LOGIN: '/pages/login/index',
  QUIZ: '/pages/quiz/index',
  PAY: '/pages/pay/index',
  MINE: '/pages/mine/index',
  CERTIFICATE: '/pages/certificate/index',
  ANSWER: '/pages/answer/index',
  WAITING: '/pages/waiting/index'
};

/***/ }),

/***/ "./src/http/api.ts":
/*!*************************!*\
  !*** ./src/http/api.ts ***!
  \*************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   request: function() { return /* binding */ request; }
/* harmony export */ });
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/taro */ "webpack/container/remote/@tarojs/taro");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/storage */ "./src/utils/storage.ts");



// 创建请求配置
const request = {
  baseUrl:  false ? 0 : 'https://apitest.wemore.com',
  get(url, data) {
    return this.request(url, 'GET', data);
  },
  post(url, data) {
    return this.request(url, 'POST', data);
  },
  put(url, data) {
    return this.request(url, 'PUT', data);
  },
  delete(url, data) {
    return this.request(url, 'DELETE', data);
  },
  request(url, method, data) {
    const token = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_1__.getStorage)('token');
    return new Promise((resolve, reject) => {
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().request({
        url: this.baseUrl + url,
        data,
        method,
        header: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        },
        success: res => {
          if (res.statusCode === 401) {
            // 未授权，跳转到登录页
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().navigateTo({
              url: '/pages/login/index'
            });
            reject(new Error('未授权，请重新登录'));
            return;
          }
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data);
          } else {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().showToast({
              title: res.data.msg || '请求失败',
              icon: 'none',
              duration: 2000
            });
            reject(new Error(res.data.msg || '请求失败'));
          }
        },
        fail: err => {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().showToast({
            title: '网络请求失败',
            icon: 'none',
            duration: 2000
          });
          reject(err);
        }
      });
    });
  }
};


/***/ }),

/***/ "./src/http/http.ts":
/*!**************************!*\
  !*** ./src/http/http.ts ***!
  \**************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getQuestionApi: function() { return /* binding */ getQuestionApi; },
/* harmony export */   getUserInfoApi: function() { return /* binding */ getUserInfoApi; },
/* harmony export */   publishAnswerApi: function() { return /* binding */ publishAnswerApi; },
/* harmony export */   wechatLoginApi: function() { return /* binding */ wechatLoginApi; }
/* harmony export */ });
/* unused harmony exports getExamListApi, getExamScoreApi, getAnswerDetailApi, getPaymentInfoApi, createPaymentOrderApi, checkPaymentStatusApi, uploadFileApi */
/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ "./src/http/api.ts");

const PRODUCT_PREFIX = '/gustoenglish';
const PAYMENT_PREFIX = '/payment-go';
const NORMAL_PREFIX = '/community-x';
// 提交答案
const publishAnswerApi = (id, answers) => {
  return _api__WEBPACK_IMPORTED_MODULE_0__.request.post(`${PRODUCT_PREFIX}/exams/${id}/submit`, {
    answers
  });
};

// 获取题目
const getQuestionApi = params => {
  return _api__WEBPACK_IMPORTED_MODULE_0__.request.get(`${PRODUCT_PREFIX}/exams/questions`, params);
};

// 获取考试列表
const getExamListApi = () => {
  return _api__WEBPACK_IMPORTED_MODULE_0__.request.get(`${PRODUCT_PREFIX}/exams`);
};

// 获取历史考试结果
const getExamScoreApi = () => {
  return _api__WEBPACK_IMPORTED_MODULE_0__.request.get(`${PRODUCT_PREFIX}/exams/scores/top`);
};

// 获取答案详情
const getAnswerDetailApi = examId => {
  return _api__WEBPACK_IMPORTED_MODULE_0__.request.get(`${PRODUCT_PREFIX}/exams/${examId}/answers`);
};

// 微信登录
const wechatLoginApi = code => {
  return _api__WEBPACK_IMPORTED_MODULE_0__.request.post(`${NORMAL_PREFIX}/auth/wechat/miniprogram`, {
    code
  });
};

// 获取用户信息
const getUserInfoApi = () => {
  return _api__WEBPACK_IMPORTED_MODULE_0__.request.get(`${NORMAL_PREFIX}/user/info`);
};

// 获取支付信息
const getPaymentInfoApi = () => {
  return _api__WEBPACK_IMPORTED_MODULE_0__.request.get(`${PAYMENT_PREFIX}/products`);
};

// 创建支付订单
const createPaymentOrderApi = productId => {
  return _api__WEBPACK_IMPORTED_MODULE_0__.request.post(`${PAYMENT_PREFIX}/orders`, {
    productId
  });
};

// 检查支付状态
const checkPaymentStatusApi = orderId => {
  return _api__WEBPACK_IMPORTED_MODULE_0__.request.get(`${PAYMENT_PREFIX}/orders/${orderId}/status`);
};

// 上传文件
const uploadFileApi = filePath => {
  return new Promise((resolve, reject) => {
    Taro.uploadFile({
      url: _api__WEBPACK_IMPORTED_MODULE_0__.request.baseUrl + '/upload',
      filePath,
      name: 'file',
      success: res => {
        try {
          const data = JSON.parse(res.data);
          resolve(data);
        } catch (e) {
          reject(new Error('上传失败'));
        }
      },
      fail: reject
    });
  });
};

/***/ }),

/***/ "./src/store/user.ts":
/*!***************************!*\
  !*** ./src/store/user.ts ***!
  \***************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ "webpack/container/remote/zustand");
/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(zustand__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/storage */ "./src/utils/storage.ts");
/* harmony import */ var _global_consts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../global/consts */ "./src/global/consts.ts");



const useUserStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get) => ({
  token: (0,_utils_storage__WEBPACK_IMPORTED_MODULE_1__.getStorage)(_global_consts__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.TOKEN) || null,
  userInfo: (0,_utils_storage__WEBPACK_IMPORTED_MODULE_1__.getStorage)(_global_consts__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.USER_INFO) ? JSON.parse((0,_utils_storage__WEBPACK_IMPORTED_MODULE_1__.getStorage)(_global_consts__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.USER_INFO)) : null,
  isLoggedIn: !!(0,_utils_storage__WEBPACK_IMPORTED_MODULE_1__.getStorage)(_global_consts__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.TOKEN),
  // Actions
  setToken: token => {
    (0,_utils_storage__WEBPACK_IMPORTED_MODULE_1__.setStorage)(_global_consts__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.TOKEN, token);
    set({
      token,
      isLoggedIn: true
    });
  },
  setUserInfo: userInfo => {
    (0,_utils_storage__WEBPACK_IMPORTED_MODULE_1__.setStorage)(_global_consts__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo));
    set({
      userInfo
    });
  },
  logout: () => {
    (0,_utils_storage__WEBPACK_IMPORTED_MODULE_1__.setStorage)(_global_consts__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.TOKEN, '');
    (0,_utils_storage__WEBPACK_IMPORTED_MODULE_1__.setStorage)(_global_consts__WEBPACK_IMPORTED_MODULE_2__.STORAGE_KEYS.USER_INFO, '');
    set({
      token: null,
      userInfo: null,
      isLoggedIn: false
    });
  },
  // Getters
  hasProduct: productId => {
    const {
      userInfo
    } = get();
    return userInfo?.products?.includes(productId) || false;
  }
}));
/* harmony default export */ __webpack_exports__["default"] = (useUserStore);

/***/ }),

/***/ "./src/utils/storage.ts":
/*!******************************!*\
  !*** ./src/utils/storage.ts ***!
  \******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getStorage: function() { return /* binding */ getStorage; },
/* harmony export */   getStorageJson: function() { return /* binding */ getStorageJson; },
/* harmony export */   removeStorage: function() { return /* binding */ removeStorage; },
/* harmony export */   setStorage: function() { return /* binding */ setStorage; },
/* harmony export */   setStorageJson: function() { return /* binding */ setStorageJson; }
/* harmony export */ });
/* unused harmony export clearStorage */
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/taro */ "webpack/container/remote/@tarojs/taro");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__);


// 设置存储
const setStorage = (key, value) => {
  try {
    _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().setStorageSync(key, value);
  } catch (error) {
    console.error('设置存储失败:', error);
  }
};

// 获取存储
const getStorage = key => {
  try {
    return _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().getStorageSync(key);
  } catch (error) {
    console.error('获取存储失败:', error);
    return null;
  }
};

// 获取JSON格式存储
const getStorageJson = key => {
  try {
    const value = _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().getStorageSync(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    console.error('获取JSON存储失败:', error);
    return null;
  }
};

// 设置JSON格式存储
const setStorageJson = (key, value) => {
  try {
    _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().setStorageSync(key, JSON.stringify(value));
  } catch (error) {
    console.error('设置JSON存储失败:', error);
  }
};

// 移除存储
const removeStorage = key => {
  try {
    _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().removeStorageSync(key);
  } catch (error) {
    console.error('移除存储失败:', error);
  }
};

// 清空存储
const clearStorage = () => {
  try {
    _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().clearStorageSync();
  } catch (error) {
    console.error('清空存储失败:', error);
  }
};

/***/ })

}]);
//# sourceMappingURL=common.js.map