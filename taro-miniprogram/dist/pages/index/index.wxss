/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[0].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[0].use[2]!./node_modules/resolve-url-loader/index.js!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[0].use[4]!./src/pages/index/index.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
.index {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 40rpx 30rpx;
  background-color: #f8f9fa;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.header .logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.header .title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.header .subtitle {
  font-size: 18rpx;
  color: #666;
}

.exam-section {
  margin-bottom: 40rpx;
}

.exam-section .section-title {
  font-size: 20rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.exam-section .exam-cards {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.exam-section .exam-cards .exam-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.exam-section .exam-cards .exam-card .card-header {
  margin-bottom: 15rpx;
}

.exam-section .exam-cards .exam-card .card-header .card-title {
  font-size: 18rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
  display: block;
}

.exam-section .exam-cards .exam-card .card-header .card-desc {
  font-size: 14rpx;
  color: #666;
  display: block;
}

.exam-section .exam-cards .exam-card .card-content {
  margin-bottom: 20rpx;
}

.exam-section .exam-cards .exam-card .card-content .card-info {
  font-size: 14rpx;
  color: #666;
  line-height: 1.6;
  display: block;
}

.exam-section .exam-cards .exam-card .start-btn {
  width: 100%;
  height: 45rpx;
  border-radius: 8rpx;
  font-size: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quick-actions {
  margin-top: 20rpx;
}

.quick-actions .history-btn {
  width: 100%;
  height: 45rpx;
  border-radius: 8rpx;
  font-size: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
