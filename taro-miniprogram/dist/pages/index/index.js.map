{"version": 3, "file": "pages/index/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AClIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://taro-miniprogram/./src/pages/index/index.tsx?63ff", "webpack://taro-miniprogram/._src_pages_index_index.tsx"], "sourcesContent": ["import { View, Text, Image } from '@tarojs/components';\nimport Taro, { useLoad } from '@tarojs/taro';\nimport { useEffect } from 'react';\nimport useUserStore from '../../store/user';\nimport Button from '../../components/Button';\nimport { PAGE_PATHS, EXAM_TYPES } from '../../global/consts';\nimport './index.scss';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default function Index() {\n  const {\n    isLoggedIn\n  } = useUserStore();\n  useLoad(() => {\n    console.log('Page loaded.');\n  });\n  useEffect(() => {\n    // 检查登录状态\n    if (!isLoggedIn) {\n      Taro.navigateTo({\n        url: PAGE_PATHS.LOGIN\n      });\n    }\n  }, [isLoggedIn]);\n  const handleStartExam = examType => {\n    if (!isLoggedIn) {\n      Taro.navigateTo({\n        url: PAGE_PATHS.LOGIN\n      });\n      return;\n    }\n    Taro.navigateTo({\n      url: `${PAGE_PATHS.QUIZ}?type=${examType}`\n    });\n  };\n  const handleViewHistory = () => {\n    Taro.navigateTo({\n      url: PAGE_PATHS.MINE\n    });\n  };\n  return /*#__PURE__*/_jsxs(View, {\n    className: \"index\",\n    children: [/*#__PURE__*/_jsxs(View, {\n      className: \"header\",\n      children: [/*#__PURE__*/_jsx(Image, {\n        className: \"logo\",\n        src: \"/images/logo.png\",\n        mode: \"aspectFit\"\n      }), /*#__PURE__*/_jsx(Text, {\n        className: \"title\",\n        children: \"Gusto English\"\n      }), /*#__PURE__*/_jsx(Text, {\n        className: \"subtitle\",\n        children: \"\\u9AD8\\u62D3\\u82F1\\u8BED\\u6C34\\u5E73\\u6D4B\\u8BD5\"\n      })]\n    }), /*#__PURE__*/_jsxs(View, {\n      className: \"exam-section\",\n      children: [/*#__PURE__*/_jsx(Text, {\n        className: \"section-title\",\n        children: \"\\u9009\\u62E9\\u6D4B\\u8BD5\\u7C7B\\u578B\"\n      }), /*#__PURE__*/_jsxs(View, {\n        className: \"exam-cards\",\n        children: [/*#__PURE__*/_jsxs(View, {\n          className: \"exam-card\",\n          children: [/*#__PURE__*/_jsxs(View, {\n            className: \"card-header\",\n            children: [/*#__PURE__*/_jsx(Text, {\n              className: \"card-title\",\n              children: \"\\u6708\\u5EA6\\u8BC4\\u4F30\\u6D4B\\u8BD5\"\n            }), /*#__PURE__*/_jsx(Text, {\n              className: \"card-desc\",\n              children: \"\\u5168\\u9762\\u8BC4\\u4F30\\u60A8\\u7684\\u82F1\\u8BED\\u6C34\\u5E73\"\n            })]\n          }), /*#__PURE__*/_jsxs(View, {\n            className: \"card-content\",\n            children: [/*#__PURE__*/_jsx(Text, {\n              className: \"card-info\",\n              children: \"\\u2022 \\u5305\\u542B\\u542C\\u8BF4\\u8BFB\\u5199\\u56DB\\u9879\\u6280\\u80FD\"\n            }), /*#__PURE__*/_jsx(Text, {\n              className: \"card-info\",\n              children: \"\\u2022 \\u6D4B\\u8BD5\\u65F6\\u95F4\\u7EA630\\u5206\\u949F\"\n            }), /*#__PURE__*/_jsx(Text, {\n              className: \"card-info\",\n              children: \"\\u2022 \\u63D0\\u4F9B\\u8BE6\\u7EC6\\u8BC4\\u4F30\\u62A5\\u544A\"\n            })]\n          }), /*#__PURE__*/_jsx(Button, {\n            className: \"start-btn\",\n            onClick: () => handleStartExam(EXAM_TYPES.MONTH_ASSESSMENT),\n            children: \"\\u5F00\\u59CB\\u6D4B\\u8BD5\"\n          })]\n        }), /*#__PURE__*/_jsxs(View, {\n          className: \"exam-card\",\n          children: [/*#__PURE__*/_jsxs(View, {\n            className: \"card-header\",\n            children: [/*#__PURE__*/_jsx(Text, {\n              className: \"card-title\",\n              children: \"\\u6A21\\u62DF\\u6D4B\\u8BD5\"\n            }), /*#__PURE__*/_jsx(Text, {\n              className: \"card-desc\",\n              children: \"\\u5FEB\\u901F\\u4F53\\u9A8C\\u6D4B\\u8BD5\\u6D41\\u7A0B\"\n            })]\n          }), /*#__PURE__*/_jsxs(View, {\n            className: \"card-content\",\n            children: [/*#__PURE__*/_jsx(Text, {\n              className: \"card-info\",\n              children: \"\\u2022 \\u7CBE\\u9009\\u6838\\u5FC3\\u9898\\u578B\"\n            }), /*#__PURE__*/_jsx(Text, {\n              className: \"card-info\",\n              children: \"\\u2022 \\u6D4B\\u8BD5\\u65F6\\u95F4\\u7EA615\\u5206\\u949F\"\n            }), /*#__PURE__*/_jsx(Text, {\n              className: \"card-info\",\n              children: \"\\u2022 \\u5FEB\\u901F\\u4E86\\u89E3\\u6D4B\\u8BD5\\u5F62\\u5F0F\"\n            })]\n          }), /*#__PURE__*/_jsx(Button, {\n            className: \"start-btn\",\n            variant: \"outline\",\n            onClick: () => handleStartExam(EXAM_TYPES.DEV_MOCK),\n            children: \"\\u5F00\\u59CB\\u6D4B\\u8BD5\"\n          })]\n        })]\n      })]\n    }), /*#__PURE__*/_jsx(View, {\n      className: \"quick-actions\",\n      children: /*#__PURE__*/_jsx(Button, {\n        className: \"history-btn\",\n        variant: \"ghost\",\n        onClick: handleViewHistory,\n        children: \"\\u67E5\\u770B\\u5386\\u53F2\\u6210\\u7EE9\"\n      })\n    })]\n  });\n}", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/index/index!./index.tsx\"\nvar config = {\"navigationBarTitleText\":\"首页\"};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/index/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n"], "names": [], "sourceRoot": ""}