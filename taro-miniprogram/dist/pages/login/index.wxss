/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[0].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[0].use[2]!./node_modules/resolve-url-loader/index.js!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[1].oneOf[0].use[4]!./src/pages/login/index.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
.login-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 40rpx 30rpx;
  background-color: #f8f9fa;
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.login-header .logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.login-header .title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.login-header .subtitle {
  font-size: 18rpx;
  color: #666;
}

.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.login-content .welcome-text {
  margin-bottom: 40rpx;
}

.login-content .welcome-text .welcome-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.login-content .welcome-text .welcome-desc {
  font-size: 16rpx;
  color: #666;
  line-height: 1.5;
}

.login-content .login-form .login-btn {
  width: 100%;
  height: 50rpx;
  background-color: #07c160;
  color: #fff;
  font-size: 18rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-content .login-form .agreement-section {
  margin-top: 20rpx;
}

.login-content .login-form .agreement-section .checkbox-container {
  display: flex;
  align-items: center;
}

.login-content .login-form .agreement-section .checkbox-container .checkbox {
  width: 20rpx;
  height: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 4rpx;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-content .login-form .agreement-section .checkbox-container .checkbox.checked {
  background-color: #07c160;
  border-color: #07c160;
}

.login-content .login-form .agreement-section .checkbox-container .checkbox .checkmark {
  color: #fff;
  font-size: 14rpx;
}

.login-content .login-form .agreement-section .checkbox-container .agreement-text {
  font-size: 14rpx;
  color: #666;
}

.login-content .login-form .agreement-section .checkbox-container .agreement-text .agreement-link {
  color: #07c160;
}

.login-footer {
  margin-top: 40rpx;
  text-align: center;
}

.login-footer .footer-text {
  font-size: 12rpx;
  color: #999;
}
