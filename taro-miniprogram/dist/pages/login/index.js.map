{"version": 3, "file": "pages/login/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACxGA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AAAA;AAAA;;AAEA;AACA;AAEA;AACA;AACA;;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACvEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://taro-miniprogram/./src/pages/login/index.tsx?56bb", "webpack://taro-miniprogram/._src_hooks_useLogin.ts", "webpack://taro-miniprogram/._src_pages_login_index.tsx"], "sourcesContent": ["import { View, Text, Image } from '@tarojs/components';\nimport Taro from '@tarojs/taro';\nimport { useState } from 'react';\nimport useLogin from '../../hooks/useLogin';\nimport Button from '../../components/Button';\nimport { PAGE_PATHS } from '../../global/consts';\nimport './index.scss';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default function Login() {\n  const {\n    login,\n    loading\n  } = useLogin();\n  const [agreementChecked, setAgreementChecked] = useState(false);\n  const handleLogin = async () => {\n    if (!agreementChecked) {\n      Taro.showToast({\n        title: '请先同意用户协议',\n        icon: 'none'\n      });\n      return;\n    }\n    const success = await login();\n    if (success) {\n      // 登录成功，跳转到首页\n      Taro.switchTab({\n        url: PAGE_PATHS.INDEX\n      });\n    }\n  };\n  const handleAgreementChange = () => {\n    setAgreementChecked(!agreementChecked);\n  };\n  const handleViewAgreement = () => {\n    // 查看用户协议\n    Taro.showModal({\n      title: '用户协议',\n      content: '这里是用户协议内容...',\n      showCancel: false\n    });\n  };\n  return /*#__PURE__*/_jsxs(View, {\n    className: \"login-container\",\n    children: [/*#__PURE__*/_jsxs(View, {\n      className: \"login-header\",\n      children: [/*#__PURE__*/_jsx(Image, {\n        className: \"logo\",\n        src: \"/images/logo.png\",\n        mode: \"aspectFit\"\n      }), /*#__PURE__*/_jsx(Text, {\n        className: \"title\",\n        children: \"Gusto English\"\n      }), /*#__PURE__*/_jsx(Text, {\n        className: \"subtitle\",\n        children: \"\\u9AD8\\u62D3\\u82F1\\u8BED\\u6C34\\u5E73\\u6D4B\\u8BD5\"\n      })]\n    }), /*#__PURE__*/_jsxs(View, {\n      className: \"login-content\",\n      children: [/*#__PURE__*/_jsxs(View, {\n        className: \"welcome-text\",\n        children: [/*#__PURE__*/_jsx(Text, {\n          className: \"welcome-title\",\n          children: \"\\u6B22\\u8FCE\\u4F7F\\u7528\"\n        }), /*#__PURE__*/_jsx(Text, {\n          className: \"welcome-desc\",\n          children: \"\\u4E13\\u4E1A\\u7684\\u82F1\\u8BED\\u6C34\\u5E73\\u6D4B\\u8BD5\\u5E73\\u53F0\\uFF0C\\u4E3A\\u60A8\\u63D0\\u4F9B\\u51C6\\u786E\\u7684\\u82F1\\u8BED\\u80FD\\u529B\\u8BC4\\u4F30\"\n        })]\n      }), /*#__PURE__*/_jsxs(View, {\n        className: \"login-form\",\n        children: [/*#__PURE__*/_jsx(Button, {\n          className: \"login-btn\",\n          onClick: handleLogin,\n          disabled: loading,\n          children: loading ? '登录中...' : '微信快速登录'\n        }), /*#__PURE__*/_jsx(View, {\n          className: \"agreement-section\",\n          children: /*#__PURE__*/_jsxs(View, {\n            className: \"checkbox-container\",\n            onClick: handleAgreementChange,\n            children: [/*#__PURE__*/_jsx(View, {\n              className: `checkbox ${agreementChecked ? 'checked' : ''}`,\n              children: agreementChecked && /*#__PURE__*/_jsx(Text, {\n                className: \"checkmark\",\n                children: \"\\u2713\"\n              })\n            }), /*#__PURE__*/_jsxs(Text, {\n              className: \"agreement-text\",\n              children: [\"\\u6211\\u5DF2\\u9605\\u8BFB\\u5E76\\u540C\\u610F\", /*#__PURE__*/_jsx(Text, {\n                className: \"agreement-link\",\n                onClick: handleViewAgreement,\n                children: \"\\u300A\\u7528\\u6237\\u534F\\u8BAE\\u300B\"\n              })]\n            })]\n          })\n        })]\n      })]\n    }), /*#__PURE__*/_jsx(View, {\n      className: \"login-footer\",\n      children: /*#__PURE__*/_jsx(Text, {\n        className: \"footer-text\",\n        children: \"\\u767B\\u5F55\\u5373\\u8868\\u793A\\u60A8\\u540C\\u610F\\u6211\\u4EEC\\u7684\\u670D\\u52A1\\u6761\\u6B3E\\u548C\\u9690\\u79C1\\u653F\\u7B56\"\n      })\n    })]\n  });\n}", "import { useState } from 'react'\nimport Taro from '@tarojs/taro'\nimport { wechatLoginApi, getUserInfoApi } from '../http/http'\nimport useUserStore from '../store/user'\nimport { PAGE_PATHS } from '../global/consts'\n\nexport default function useLogin() {\n  const [loading, setLoading] = useState(false)\n  const { setToken, setUserInfo } = useUserStore()\n\n  // 微信登录\n  const login = async () => {\n    try {\n      setLoading(true)\n      \n      // 获取微信登录凭证\n      const { code } = await Taro.login()\n      \n      // 调用后端登录接口\n      const res: any = await wechatLoginApi(code)\n      \n      if (res.code === '0' && res.data?.token) {\n        // 保存token\n        setToken(res.data.token)\n        \n        // 获取用户信息\n        const userInfoRes: any = await getUserInfoApi()\n        \n        if (userInfoRes.code === '0' && userInfoRes.data) {\n          setUserInfo(userInfoRes.data)\n        }\n        \n        return true\n      } else {\n        Taro.showToast({\n          title: '登录失败',\n          icon: 'none'\n        })\n        return false\n      }\n    } catch (error) {\n      console.error('登录失败:', error)\n      Taro.showToast({\n        title: '登录失败',\n        icon: 'none'\n      })\n      return false\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // 检查登录状态\n  const checkLogin = () => {\n    const { isLoggedIn } = useUserStore.getState()\n    \n    if (!isLoggedIn) {\n      Taro.navigateTo({\n        url: PAGE_PATHS.LOGIN\n      })\n      return false\n    }\n    \n    return true\n  }\n\n  return {\n    login,\n    checkLogin,\n    loading\n  }\n}\n", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/login/index!./index.tsx\"\nvar config = {\"navigationBarTitleText\":\"登录\"};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/login/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n"], "names": [], "sourceRoot": ""}