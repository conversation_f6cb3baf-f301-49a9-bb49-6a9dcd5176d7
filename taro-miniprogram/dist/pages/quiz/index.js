"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["pages/quiz/index"],{

/***/ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/quiz/index!./src/pages/quiz/index.tsx":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/quiz/index!./src/pages/quiz/index.tsx ***!
  \**************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ Quiz; }
/* harmony export */ });
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/taro */ "webpack/container/remote/@tarojs/taro");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "webpack/container/remote/react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _store_quiz__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../store/quiz */ "./src/store/quiz.ts");
/* harmony import */ var _hooks_useInitQuestion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useInitQuestion */ "./src/hooks/useInitQuestion.ts");
/* harmony import */ var _hooks_useRecorder__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/useRecorder */ "./src/hooks/useRecorder.ts");
/* harmony import */ var _components_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/Button */ "./src/components/Button/index.tsx");
/* harmony import */ var _http_http__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../http/http */ "./src/http/http.ts");
/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../utils/storage */ "./src/utils/storage.ts");
/* harmony import */ var _global_consts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../global/consts */ "./src/global/consts.ts");
Object(function webpackMissingModule() { var e = new Error("Cannot find module './index.scss'"); e.code = 'MODULE_NOT_FOUND'; throw e; }());
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react/jsx-runtime */ "webpack/container/remote/react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__);












function Quiz() {
  const router = (0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__.useRouter)();
  const examType = router.params.type || 'month-assessment';
  const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  const [currentAnswer, setCurrentAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
  const {
    examInfo,
    currentQuestionIndex,
    answers,
    isFinished,
    getCurrentQuestion,
    getTotalQuestions,
    getProgress,
    setCurrentQuestionIndex,
    addAnswer,
    updateAnswer,
    finishExam
  } = (0,_store_quiz__WEBPACK_IMPORTED_MODULE_2__["default"])();
  const {
    loading,
    error
  } = (0,_hooks_useInitQuestion__WEBPACK_IMPORTED_MODULE_3__["default"])(examType);
  const {
    isRecording,
    recordingPath,
    startRecord,
    stopRecord,
    playRecord
  } = (0,_hooks_useRecorder__WEBPACK_IMPORTED_MODULE_4__["default"])();
  const currentQuestion = getCurrentQuestion();
  const totalQuestions = getTotalQuestions();
  const progress = getProgress();
  (0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__.useLoad)(() => {
    console.log('Quiz page loaded');
  });

  // 提交答案并进入下一题
  const handleNextQuestion = async () => {
    if (!currentQuestion) return;

    // 保存当前答案
    const answer = {
      questionId: currentQuestion.id,
      answer: currentAnswer,
      audioUrl: recordingPath || undefined
    };
    const existingAnswerIndex = answers.findIndex(a => a.questionId === currentQuestion.id);
    if (existingAnswerIndex >= 0) {
      updateAnswer(currentQuestion.id, answer);
    } else {
      addAnswer(answer);
    }

    // 保存进度到本地存储
    const nextIndex = currentQuestionIndex + 1;
    (0,_utils_storage__WEBPACK_IMPORTED_MODULE_7__.setStorageJson)(_global_consts__WEBPACK_IMPORTED_MODULE_8__.STORAGE_KEYS.CURRENT_QUESTION, nextIndex);
    (0,_utils_storage__WEBPACK_IMPORTED_MODULE_7__.setStorageJson)(_global_consts__WEBPACK_IMPORTED_MODULE_8__.STORAGE_KEYS.ANSWERS, [...answers, answer]);
    if (nextIndex >= totalQuestions) {
      // 考试结束
      await handleSubmitExam([...answers, answer]);
    } else {
      // 进入下一题
      setCurrentQuestionIndex(nextIndex);
      setCurrentAnswer('');
    }
  };

  // 提交整个考试
  const handleSubmitExam = async (finalAnswers = answers) => {
    if (!examInfo) return;
    try {
      setIsSubmitting(true);
      await (0,_http_http__WEBPACK_IMPORTED_MODULE_6__.publishAnswerApi)(examInfo.id, finalAnswers);

      // 清除本地缓存
      (0,_utils_storage__WEBPACK_IMPORTED_MODULE_7__.removeStorage)(_global_consts__WEBPACK_IMPORTED_MODULE_8__.STORAGE_KEYS.EXAM_DATA);
      (0,_utils_storage__WEBPACK_IMPORTED_MODULE_7__.removeStorage)(_global_consts__WEBPACK_IMPORTED_MODULE_8__.STORAGE_KEYS.CURRENT_QUESTION);
      (0,_utils_storage__WEBPACK_IMPORTED_MODULE_7__.removeStorage)(_global_consts__WEBPACK_IMPORTED_MODULE_8__.STORAGE_KEYS.ANSWERS);
      finishExam();
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().showToast({
        title: '提交成功',
        icon: 'success'
      });

      // 跳转到结果页面
      setTimeout(() => {
        _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().navigateTo({
          url: `${_global_consts__WEBPACK_IMPORTED_MODULE_8__.PAGE_PATHS.ANSWER}?examId=${examInfo.id}`
        });
      }, 1500);
    } catch (error) {
      console.error('提交考试失败:', error);
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 录音控制
  const handleRecordToggle = async () => {
    if (isRecording) {
      stopRecord();
    } else {
      const success = await startRecord();
      if (!success) {
        _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().showToast({
          title: '录音权限未开启',
          icon: 'none'
        });
      }
    }
  };
  if (loading) {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.View, {
      className: "quiz-loading",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.Text, {
        children: "\u52A0\u8F7D\u4E2D..."
      })
    });
  }
  if (error) {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.View, {
      className: "quiz-error",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.Text, {
        children: error
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_components_Button__WEBPACK_IMPORTED_MODULE_5__["default"], {
        onClick: () => _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().navigateBack(),
        children: "\u8FD4\u56DE"
      })]
    });
  }
  if (isFinished) {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.View, {
      className: "quiz-finished",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.Text, {
        className: "finish-title",
        children: "\u8003\u8BD5\u5DF2\u5B8C\u6210"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.Text, {
        className: "finish-desc",
        children: "\u6B63\u5728\u8DF3\u8F6C\u5230\u7ED3\u679C\u9875\u9762..."
      })]
    });
  }
  if (!currentQuestion) {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.View, {
      className: "quiz-error",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.Text, {
        children: "\u6CA1\u6709\u627E\u5230\u9898\u76EE"
      })
    });
  }
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.View, {
    className: "quiz-container",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.View, {
      className: "progress-section",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.View, {
        className: "progress-info",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.Text, {
          className: "progress-text",
          children: ["\u7B2C ", currentQuestionIndex + 1, " \u9898 / \u5171 ", totalQuestions, " \u9898"]
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.Text, {
          className: "progress-percent",
          children: [Math.round(progress), "%"]
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.Progress, {
        percent: progress,
        strokeWidth: 4,
        activeColor: "#07c160",
        backgroundColor: "#e5e5e5"
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.View, {
      className: "question-section",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.Text, {
        className: "question-title",
        children: currentQuestion.title
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.Text, {
        className: "question-content",
        children: currentQuestion.content
      }), currentQuestion.imageUrl && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.Image, {
        className: "question-image",
        src: currentQuestion.imageUrl,
        mode: "aspectFit"
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.View, {
      className: "answer-section",
      children: [currentQuestion.type === 'text' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.Textarea, {
        className: "answer-input",
        placeholder: "\u8BF7\u8F93\u5165\u60A8\u7684\u7B54\u6848",
        value: currentAnswer,
        onInput: e => setCurrentAnswer(e.detail.value)
      }), (currentQuestion.type === 'audio' || currentQuestion.type === 'speaking') && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.View, {
        className: "record-section",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_components_Button__WEBPACK_IMPORTED_MODULE_5__["default"], {
          className: `record-btn ${isRecording ? 'recording' : ''}`,
          onClick: handleRecordToggle,
          children: isRecording ? '停止录音' : '开始录音'
        }), recordingPath && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_components_Button__WEBPACK_IMPORTED_MODULE_5__["default"], {
          className: "play-btn",
          variant: "outline",
          onClick: () => playRecord(),
          children: "\u64AD\u653E\u5F55\u97F3"
        })]
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_11__.View, {
      className: "action-section",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_components_Button__WEBPACK_IMPORTED_MODULE_5__["default"], {
        className: "next-btn",
        onClick: handleNextQuestion,
        disabled: isSubmitting || !currentAnswer && !recordingPath,
        children: currentQuestionIndex === totalQuestions - 1 ? isSubmitting ? '提交中...' : '提交考试' : '下一题'
      })
    })]
  });
}

/***/ }),

/***/ "./src/hooks/useInitQuestion.ts":
/*!**************************************!*\
  !*** ./src/hooks/useInitQuestion.ts ***!
  \**************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useInitQuestion; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "webpack/container/remote/react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/taro */ "webpack/container/remote/@tarojs/taro");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _http_http__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../http/http */ "./src/http/http.ts");
/* harmony import */ var _store_quiz__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../store/quiz */ "./src/store/quiz.ts");
/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/storage */ "./src/utils/storage.ts");
/* harmony import */ var _global_consts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../global/consts */ "./src/global/consts.ts");






function useInitQuestion(examType) {
  const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);
  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const {
    setQuestions,
    setExamInfo,
    setCurrentQuestionIndex,
    reset
  } = (0,_store_quiz__WEBPACK_IMPORTED_MODULE_3__["default"])();
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    const initExam = async () => {
      try {
        setLoading(true);

        // 检查本地是否有缓存的考试数据
        const cachedData = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_4__.getStorageJson)(_global_consts__WEBPACK_IMPORTED_MODULE_5__.STORAGE_KEYS.EXAM_DATA);
        const cachedCurrentQuestion = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_4__.getStorageJson)(_global_consts__WEBPACK_IMPORTED_MODULE_5__.STORAGE_KEYS.CURRENT_QUESTION);
        const cachedAnswers = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_4__.getStorageJson)(_global_consts__WEBPACK_IMPORTED_MODULE_5__.STORAGE_KEYS.ANSWERS);
        if (cachedData && cachedData.type === examType) {
          // 使用缓存数据
          setExamInfo({
            id: cachedData.id,
            type: cachedData.type,
            title: cachedData.title,
            description: cachedData.description,
            totalQuestions: cachedData.questions.length,
            duration: cachedData.duration
          });
          setQuestions(cachedData.questions);
          if (cachedCurrentQuestion !== null) {
            setCurrentQuestionIndex(cachedCurrentQuestion);
          }

          // 恢复已保存的答案
          if (cachedAnswers && cachedAnswers.length > 0) {
            cachedAnswers.forEach(answer => {
              _store_quiz__WEBPACK_IMPORTED_MODULE_3__["default"].getState().addAnswer(answer);
            });
          }
        } else {
          // 获取新的考试数据
          const res = await (0,_http_http__WEBPACK_IMPORTED_MODULE_2__.getQuestionApi)({
            exam_type: examType
          });
          if (res.code === '0' && res.data) {
            const examData = res.data;
            setExamInfo({
              id: examData.id,
              type: examData.type,
              title: examData.title,
              description: examData.description,
              totalQuestions: examData.questions.length,
              duration: examData.duration
            });
            setQuestions(examData.questions);
            setCurrentQuestionIndex(0);

            // 缓存考试数据
            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_4__.setStorageJson)(_global_consts__WEBPACK_IMPORTED_MODULE_5__.STORAGE_KEYS.EXAM_DATA, examData);
            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_4__.setStorageJson)(_global_consts__WEBPACK_IMPORTED_MODULE_5__.STORAGE_KEYS.CURRENT_QUESTION, 0);
            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_4__.setStorageJson)(_global_consts__WEBPACK_IMPORTED_MODULE_5__.STORAGE_KEYS.ANSWERS, []);
          } else {
            throw new Error(res.msg || '获取考试数据失败');
          }
        }
      } catch (err) {
        console.error('初始化考试失败:', err);
        setError('获取考试数据失败，请重试');
        _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().showToast({
          title: '获取考试数据失败',
          icon: 'none'
        });
      } finally {
        setLoading(false);
      }
    };

    // 重置考试状态
    reset();

    // 初始化考试
    initExam();

    // 组件卸载时清理
    return () => {
      // 可以在这里做一些清理工作
    };
  }, [examType]);
  return {
    loading,
    error
  };
}

/***/ }),

/***/ "./src/hooks/useRecorder.ts":
/*!**********************************!*\
  !*** ./src/hooks/useRecorder.ts ***!
  \**********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ useRecorder; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "webpack/container/remote/react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/taro */ "webpack/container/remote/@tarojs/taro");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_1__);


function useRecorder() {
  const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [recordingPath, setRecordingPath] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');
  const recorderManager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);

  // 初始化录音管理器
  const initRecorder = () => {
    if (!recorderManager.current) {
      recorderManager.current = _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().getRecorderManager();
      recorderManager.current.onStart(() => {
        console.log('录音开始');
        setIsRecording(true);
      });
      recorderManager.current.onStop(res => {
        console.log('录音结束', res);
        setIsRecording(false);
        setRecordingPath(res.tempFilePath);
      });
      recorderManager.current.onError(err => {
        console.error('录音错误', err);
        setIsRecording(false);
        _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().showToast({
          title: '录音失败',
          icon: 'none'
        });
      });
    }
  };

  // 检查录音权限
  const checkRecordPermission = async () => {
    try {
      const res = await _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().getSetting();
      if (res.authSetting['scope.record'] === false) {
        // 用户拒绝了录音权限，引导用户去设置页面
        const modalRes = await _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().showModal({
          title: '录音权限',
          content: '需要录音权限才能进行口语测试，请在设置中开启录音权限',
          confirmText: '去设置',
          cancelText: '取消'
        });
        if (modalRes.confirm) {
          await _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().openSetting();
        }
        return false;
      } else if (res.authSetting['scope.record'] === undefined) {
        // 用户还没有授权，请求授权
        try {
          await _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().authorize({
            scope: 'scope.record'
          });
          return true;
        } catch (error) {
          console.error('录音授权失败', error);
          return false;
        }
      } else {
        // 用户已经授权
        return true;
      }
    } catch (error) {
      console.error('检查录音权限失败', error);
      return false;
    }
  };

  // 开始录音
  const startRecord = async () => {
    const hasPermission = await checkRecordPermission();
    if (!hasPermission) {
      return false;
    }
    initRecorder();
    try {
      recorderManager.current.start({
        duration: 60000,
        // 最长录音时间60秒
        sampleRate: 16000,
        numberOfChannels: 1,
        encodeBitRate: 96000,
        format: 'mp3'
      });
      return true;
    } catch (error) {
      console.error('开始录音失败', error);
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().showToast({
        title: '录音失败',
        icon: 'none'
      });
      return false;
    }
  };

  // 停止录音
  const stopRecord = () => {
    if (recorderManager.current && isRecording) {
      recorderManager.current.stop();
    }
  };

  // 播放录音
  const playRecord = filePath => {
    const audioPath = filePath || recordingPath;
    if (!audioPath) {
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().showToast({
        title: '没有录音文件',
        icon: 'none'
      });
      return;
    }
    const audioContext = _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().createInnerAudioContext();
    audioContext.src = audioPath;
    audioContext.play();
    audioContext.onError(err => {
      console.error('播放录音失败', err);
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().showToast({
        title: '播放失败',
        icon: 'none'
      });
    });
  };
  return {
    isRecording,
    recordingPath,
    startRecord,
    stopRecord,
    playRecord,
    checkRecordPermission
  };
}

/***/ }),

/***/ "./src/pages/quiz/index.tsx":
/*!**********************************!*\
  !*** ./src/pages/quiz/index.tsx ***!
  \**********************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "webpack/container/remote/@tarojs/runtime");
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_quiz_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/quiz/index!./index.tsx */ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/quiz/index!./src/pages/quiz/index.tsx");


var config = {"navigationBarTitleText":"英语水平测试"};



var taroOption = (0,_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__.createPageConfig)(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_quiz_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"], 'pages/quiz/index', {root:{cn:[]}}, config || {})
if (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_quiz_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"] && _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_quiz_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"].behaviors) {
  taroOption.behaviors = (taroOption.behaviors || []).concat(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_quiz_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"].behaviors)
}
var inst = Page(taroOption)



/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_quiz_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"]);


/***/ }),

/***/ "./src/store/quiz.ts":
/*!***************************!*\
  !*** ./src/store/quiz.ts ***!
  \***************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ "webpack/container/remote/zustand");
/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(zustand__WEBPACK_IMPORTED_MODULE_0__);

const useQuizStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get) => ({
  // Initial state
  examInfo: null,
  questions: [],
  currentQuestionIndex: 0,
  answers: [],
  isStarted: false,
  isFinished: false,
  isSubmitting: false,
  readingTimeLeft: 0,
  answerTimeLeft: 0,
  isReading: false,
  isRecording: false,
  recordingPath: '',
  // Actions
  setExamInfo: examInfo => set({
    examInfo
  }),
  setQuestions: questions => set({
    questions
  }),
  setCurrentQuestionIndex: index => set({
    currentQuestionIndex: index
  }),
  addAnswer: answer => set(state => ({
    answers: [...state.answers, answer]
  })),
  updateAnswer: (questionId, answerUpdate) => set(state => ({
    answers: state.answers.map(answer => answer.questionId === questionId ? {
      ...answer,
      ...answerUpdate
    } : answer)
  })),
  startExam: () => set({
    isStarted: true
  }),
  finishExam: () => set({
    isFinished: true
  }),
  setSubmitting: isSubmitting => set({
    isSubmitting
  }),
  setReadingTimeLeft: time => set({
    readingTimeLeft: time
  }),
  setAnswerTimeLeft: time => set({
    answerTimeLeft: time
  }),
  setIsReading: isReading => set({
    isReading
  }),
  setRecording: isRecording => set({
    isRecording
  }),
  setRecordingPath: path => set({
    recordingPath: path
  }),
  reset: () => set({
    examInfo: null,
    questions: [],
    currentQuestionIndex: 0,
    answers: [],
    isStarted: false,
    isFinished: false,
    isSubmitting: false,
    readingTimeLeft: 0,
    answerTimeLeft: 0,
    isReading: false,
    isRecording: false,
    recordingPath: ''
  }),
  // Getters
  getCurrentQuestion: () => {
    const state = get();
    return state.questions[state.currentQuestionIndex] || null;
  },
  getTotalQuestions: () => get().questions.length,
  getProgress: () => {
    const state = get();
    return state.questions.length > 0 ? (state.currentQuestionIndex + 1) / state.questions.length * 100 : 0;
  }
}));
/* harmony default export */ __webpack_exports__["default"] = (useQuizStore);

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["taro","common"], function() { return __webpack_exec__("./src/pages/quiz/index.tsx"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=index.js.map