{"version": 3, "file": "pages/quiz/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;ACpOA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AAAA;AAEA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AC3GA;AACA;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC7IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACdA;AAiFA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AAAA;AAAA;AAGA;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAGA;AACA;AAEA", "sources": ["webpack://taro-miniprogram/./src/pages/quiz/index.tsx?cc82", "webpack://taro-miniprogram/._src_hooks_useInitQuestion.ts", "webpack://taro-miniprogram/._src_hooks_useRecorder.ts", "webpack://taro-miniprogram/._src_pages_quiz_index.tsx", "webpack://taro-miniprogram/._src_store_quiz.ts"], "sourcesContent": ["import { View, Text, Progress, Image, Textarea } from '@tarojs/components';\nimport Taro, { useRouter, useLoad } from '@tarojs/taro';\nimport { useState } from 'react';\nimport useQuizStore from '../../store/quiz';\nimport useInitQuestion from '../../hooks/useInitQuestion';\nimport useRecorder from '../../hooks/useRecorder';\nimport Button from '../../components/Button';\nimport { publishAnswerApi } from '../../http/http';\nimport { setStorageJson, removeStorage } from '../../utils/storage';\nimport { STORAGE_KEYS, PAGE_PATHS } from '../../global/consts';\nimport './index.scss';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default function Quiz() {\n  const router = useRouter();\n  const examType = router.params.type || 'month-assessment';\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [currentAnswer, setCurrentAnswer] = useState('');\n  const {\n    examInfo,\n    currentQuestionIndex,\n    answers,\n    isFinished,\n    getCurrentQuestion,\n    getTotalQuestions,\n    getProgress,\n    setCurrentQuestionIndex,\n    addAnswer,\n    updateAnswer,\n    finishExam\n  } = useQuizStore();\n  const {\n    loading,\n    error\n  } = useInitQuestion(examType);\n  const {\n    isRecording,\n    recordingPath,\n    startRecord,\n    stopRecord,\n    playRecord\n  } = useRecorder();\n  const currentQuestion = getCurrentQuestion();\n  const totalQuestions = getTotalQuestions();\n  const progress = getProgress();\n  useLoad(() => {\n    console.log('Quiz page loaded');\n  });\n\n  // 提交答案并进入下一题\n  const handleNextQuestion = async () => {\n    if (!currentQuestion) return;\n\n    // 保存当前答案\n    const answer = {\n      questionId: currentQuestion.id,\n      answer: currentAnswer,\n      audioUrl: recordingPath || undefined\n    };\n    const existingAnswerIndex = answers.findIndex(a => a.questionId === currentQuestion.id);\n    if (existingAnswerIndex >= 0) {\n      updateAnswer(currentQuestion.id, answer);\n    } else {\n      addAnswer(answer);\n    }\n\n    // 保存进度到本地存储\n    const nextIndex = currentQuestionIndex + 1;\n    setStorageJson(STORAGE_KEYS.CURRENT_QUESTION, nextIndex);\n    setStorageJson(STORAGE_KEYS.ANSWERS, [...answers, answer]);\n    if (nextIndex >= totalQuestions) {\n      // 考试结束\n      await handleSubmitExam([...answers, answer]);\n    } else {\n      // 进入下一题\n      setCurrentQuestionIndex(nextIndex);\n      setCurrentAnswer('');\n    }\n  };\n\n  // 提交整个考试\n  const handleSubmitExam = async (finalAnswers = answers) => {\n    if (!examInfo) return;\n    try {\n      setIsSubmitting(true);\n      await publishAnswerApi(examInfo.id, finalAnswers);\n\n      // 清除本地缓存\n      removeStorage(STORAGE_KEYS.EXAM_DATA);\n      removeStorage(STORAGE_KEYS.CURRENT_QUESTION);\n      removeStorage(STORAGE_KEYS.ANSWERS);\n      finishExam();\n      Taro.showToast({\n        title: '提交成功',\n        icon: 'success'\n      });\n\n      // 跳转到结果页面\n      setTimeout(() => {\n        Taro.navigateTo({\n          url: `${PAGE_PATHS.ANSWER}?examId=${examInfo.id}`\n        });\n      }, 1500);\n    } catch (error) {\n      console.error('提交考试失败:', error);\n      Taro.showToast({\n        title: '提交失败，请重试',\n        icon: 'none'\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // 录音控制\n  const handleRecordToggle = async () => {\n    if (isRecording) {\n      stopRecord();\n    } else {\n      const success = await startRecord();\n      if (!success) {\n        Taro.showToast({\n          title: '录音权限未开启',\n          icon: 'none'\n        });\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsx(View, {\n      className: \"quiz-loading\",\n      children: /*#__PURE__*/_jsx(Text, {\n        children: \"\\u52A0\\u8F7D\\u4E2D...\"\n      })\n    });\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxs(View, {\n      className: \"quiz-error\",\n      children: [/*#__PURE__*/_jsx(Text, {\n        children: error\n      }), /*#__PURE__*/_jsx(Button, {\n        onClick: () => Taro.navigateBack(),\n        children: \"\\u8FD4\\u56DE\"\n      })]\n    });\n  }\n  if (isFinished) {\n    return /*#__PURE__*/_jsxs(View, {\n      className: \"quiz-finished\",\n      children: [/*#__PURE__*/_jsx(Text, {\n        className: \"finish-title\",\n        children: \"\\u8003\\u8BD5\\u5DF2\\u5B8C\\u6210\"\n      }), /*#__PURE__*/_jsx(Text, {\n        className: \"finish-desc\",\n        children: \"\\u6B63\\u5728\\u8DF3\\u8F6C\\u5230\\u7ED3\\u679C\\u9875\\u9762...\"\n      })]\n    });\n  }\n  if (!currentQuestion) {\n    return /*#__PURE__*/_jsx(View, {\n      className: \"quiz-error\",\n      children: /*#__PURE__*/_jsx(Text, {\n        children: \"\\u6CA1\\u6709\\u627E\\u5230\\u9898\\u76EE\"\n      })\n    });\n  }\n  return /*#__PURE__*/_jsxs(View, {\n    className: \"quiz-container\",\n    children: [/*#__PURE__*/_jsxs(View, {\n      className: \"progress-section\",\n      children: [/*#__PURE__*/_jsxs(View, {\n        className: \"progress-info\",\n        children: [/*#__PURE__*/_jsxs(Text, {\n          className: \"progress-text\",\n          children: [\"\\u7B2C \", currentQuestionIndex + 1, \" \\u9898 / \\u5171 \", totalQuestions, \" \\u9898\"]\n        }), /*#__PURE__*/_jsxs(Text, {\n          className: \"progress-percent\",\n          children: [Math.round(progress), \"%\"]\n        })]\n      }), /*#__PURE__*/_jsx(Progress, {\n        percent: progress,\n        strokeWidth: 4,\n        activeColor: \"#07c160\",\n        backgroundColor: \"#e5e5e5\"\n      })]\n    }), /*#__PURE__*/_jsxs(View, {\n      className: \"question-section\",\n      children: [/*#__PURE__*/_jsx(Text, {\n        className: \"question-title\",\n        children: currentQuestion.title\n      }), /*#__PURE__*/_jsx(Text, {\n        className: \"question-content\",\n        children: currentQuestion.content\n      }), currentQuestion.imageUrl && /*#__PURE__*/_jsx(Image, {\n        className: \"question-image\",\n        src: currentQuestion.imageUrl,\n        mode: \"aspectFit\"\n      })]\n    }), /*#__PURE__*/_jsxs(View, {\n      className: \"answer-section\",\n      children: [currentQuestion.type === 'text' && /*#__PURE__*/_jsx(Textarea, {\n        className: \"answer-input\",\n        placeholder: \"\\u8BF7\\u8F93\\u5165\\u60A8\\u7684\\u7B54\\u6848\",\n        value: currentAnswer,\n        onInput: e => setCurrentAnswer(e.detail.value)\n      }), (currentQuestion.type === 'audio' || currentQuestion.type === 'speaking') && /*#__PURE__*/_jsxs(View, {\n        className: \"record-section\",\n        children: [/*#__PURE__*/_jsx(Button, {\n          className: `record-btn ${isRecording ? 'recording' : ''}`,\n          onClick: handleRecordToggle,\n          children: isRecording ? '停止录音' : '开始录音'\n        }), recordingPath && /*#__PURE__*/_jsx(Button, {\n          className: \"play-btn\",\n          variant: \"outline\",\n          onClick: () => playRecord(),\n          children: \"\\u64AD\\u653E\\u5F55\\u97F3\"\n        })]\n      })]\n    }), /*#__PURE__*/_jsx(View, {\n      className: \"action-section\",\n      children: /*#__PURE__*/_jsx(Button, {\n        className: \"next-btn\",\n        onClick: handleNextQuestion,\n        disabled: isSubmitting || !currentAnswer && !recordingPath,\n        children: currentQuestionIndex === totalQuestions - 1 ? isSubmitting ? '提交中...' : '提交考试' : '下一题'\n      })\n    })]\n  });\n}", "import { useEffect, useState } from 'react'\nimport Taro from '@tarojs/taro'\nimport { getQuestionApi } from '../http/http'\nimport useQuizStore from '../store/quiz'\nimport { getStorageJson, setStorageJson } from '../utils/storage'\nimport { STORAGE_KEYS } from '../global/consts'\n\nexport default function useInitQuestion(examType: string) {\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n  \n  const { \n    setQuestions, \n    setExamInfo, \n    setCurrentQuestionIndex,\n    reset\n  } = useQuizStore()\n\n  useEffect(() => {\n    const initExam = async () => {\n      try {\n        setLoading(true)\n        \n        // 检查本地是否有缓存的考试数据\n        const cachedData = getStorageJson(STORAGE_KEYS.EXAM_DATA)\n        const cachedCurrentQuestion = getStorageJson(STORAGE_KEYS.CURRENT_QUESTION)\n        const cachedAnswers = getStorageJson(STORAGE_KEYS.ANSWERS)\n        \n        if (cachedData && cachedData.type === examType) {\n          // 使用缓存数据\n          setExamInfo({\n            id: cachedData.id,\n            type: cachedData.type,\n            title: cachedData.title,\n            description: cachedData.description,\n            totalQuestions: cachedData.questions.length,\n            duration: cachedData.duration\n          })\n          \n          setQuestions(cachedData.questions)\n          \n          if (cachedCurrentQuestion !== null) {\n            setCurrentQuestionIndex(cachedCurrentQuestion)\n          }\n          \n          // 恢复已保存的答案\n          if (cachedAnswers && cachedAnswers.length > 0) {\n            cachedAnswers.forEach(answer => {\n              useQuizStore.getState().addAnswer(answer)\n            })\n          }\n        } else {\n          // 获取新的考试数据\n          const res: any = await getQuestionApi({ exam_type: examType })\n          \n          if (res.code === '0' && res.data) {\n            const examData = res.data\n            \n            setExamInfo({\n              id: examData.id,\n              type: examData.type,\n              title: examData.title,\n              description: examData.description,\n              totalQuestions: examData.questions.length,\n              duration: examData.duration\n            })\n            \n            setQuestions(examData.questions)\n            setCurrentQuestionIndex(0)\n            \n            // 缓存考试数据\n            setStorageJson(STORAGE_KEYS.EXAM_DATA, examData)\n            setStorageJson(STORAGE_KEYS.CURRENT_QUESTION, 0)\n            setStorageJson(STORAGE_KEYS.ANSWERS, [])\n          } else {\n            throw new Error(res.msg || '获取考试数据失败')\n          }\n        }\n      } catch (err) {\n        console.error('初始化考试失败:', err)\n        setError('获取考试数据失败，请重试')\n        \n        Taro.showToast({\n          title: '获取考试数据失败',\n          icon: 'none'\n        })\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    // 重置考试状态\n    reset()\n    \n    // 初始化考试\n    initExam()\n    \n    // 组件卸载时清理\n    return () => {\n      // 可以在这里做一些清理工作\n    }\n  }, [examType])\n\n  return {\n    loading,\n    error\n  }\n}\n", "import { useState, useRef } from 'react'\nimport Taro from '@tarojs/taro'\n\nexport default function useRecorder() {\n  const [isRecording, setIsRecording] = useState(false)\n  const [recordingPath, setRecordingPath] = useState('')\n  const recorderManager = useRef<any>(null)\n\n  // 初始化录音管理器\n  const initRecorder = () => {\n    if (!recorderManager.current) {\n      recorderManager.current = Taro.getRecorderManager()\n      \n      recorderManager.current.onStart(() => {\n        console.log('录音开始')\n        setIsRecording(true)\n      })\n      \n      recorderManager.current.onStop((res) => {\n        console.log('录音结束', res)\n        setIsRecording(false)\n        setRecordingPath(res.tempFilePath)\n      })\n      \n      recorderManager.current.onError((err) => {\n        console.error('录音错误', err)\n        setIsRecording(false)\n        Taro.showToast({\n          title: '录音失败',\n          icon: 'none'\n        })\n      })\n    }\n  }\n\n  // 检查录音权限\n  const checkRecordPermission = async () => {\n    try {\n      const res = await Taro.getSetting()\n      \n      if (res.authSetting['scope.record'] === false) {\n        // 用户拒绝了录音权限，引导用户去设置页面\n        const modalRes = await Taro.showModal({\n          title: '录音权限',\n          content: '需要录音权限才能进行口语测试，请在设置中开启录音权限',\n          confirmText: '去设置',\n          cancelText: '取消'\n        })\n        \n        if (modalRes.confirm) {\n          await Taro.openSetting()\n        }\n        return false\n      } else if (res.authSetting['scope.record'] === undefined) {\n        // 用户还没有授权，请求授权\n        try {\n          await Taro.authorize({ scope: 'scope.record' })\n          return true\n        } catch (error) {\n          console.error('录音授权失败', error)\n          return false\n        }\n      } else {\n        // 用户已经授权\n        return true\n      }\n    } catch (error) {\n      console.error('检查录音权限失败', error)\n      return false\n    }\n  }\n\n  // 开始录音\n  const startRecord = async () => {\n    const hasPermission = await checkRecordPermission()\n    \n    if (!hasPermission) {\n      return false\n    }\n    \n    initRecorder()\n    \n    try {\n      recorderManager.current.start({\n        duration: 60000, // 最长录音时间60秒\n        sampleRate: 16000,\n        numberOfChannels: 1,\n        encodeBitRate: 96000,\n        format: 'mp3'\n      })\n      return true\n    } catch (error) {\n      console.error('开始录音失败', error)\n      Taro.showToast({\n        title: '录音失败',\n        icon: 'none'\n      })\n      return false\n    }\n  }\n\n  // 停止录音\n  const stopRecord = () => {\n    if (recorderManager.current && isRecording) {\n      recorderManager.current.stop()\n    }\n  }\n\n  // 播放录音\n  const playRecord = (filePath?: string) => {\n    const audioPath = filePath || recordingPath\n    \n    if (!audioPath) {\n      Taro.showToast({\n        title: '没有录音文件',\n        icon: 'none'\n      })\n      return\n    }\n    \n    const audioContext = Taro.createInnerAudioContext()\n    audioContext.src = audioPath\n    audioContext.play()\n    \n    audioContext.onError((err) => {\n      console.error('播放录音失败', err)\n      Taro.showToast({\n        title: '播放失败',\n        icon: 'none'\n      })\n    })\n  }\n\n  return {\n    isRecording,\n    recordingPath,\n    startRecord,\n    stopRecord,\n    playRecord,\n    checkRecordPermission\n  }\n}\n", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/quiz/index!./index.tsx\"\nvar config = {\"navigationBarTitleText\":\"英语水平测试\"};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/quiz/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n", "import { create } from 'zustand'\n\nexport interface Answer {\n  questionId: string\n  answer: string\n  audioUrl?: string\n  duration?: number\n}\n\nexport interface Question {\n  id: string\n  type: string\n  title: string\n  content: string\n  options?: string[]\n  audioUrl?: string\n  imageUrl?: string\n  readingTime?: number\n  answerTime?: number\n  tipInfo?: {\n    content: string\n  }\n  introduction?: string\n}\n\nexport interface ExamInfo {\n  id: string\n  type: string\n  title: string\n  description: string\n  totalQuestions: number\n  duration: number\n}\n\ninterface QuizState {\n  // 考试信息\n  examInfo: ExamInfo | null\n  questions: Question[]\n  currentQuestionIndex: number\n  answers: Answer[]\n  \n  // 考试状态\n  isStarted: boolean\n  isFinished: boolean\n  isSubmitting: boolean\n  \n  // 计时器\n  readingTimeLeft: number\n  answerTimeLeft: number\n  isReading: boolean\n  \n  // 录音状态\n  isRecording: boolean\n  recordingPath: string\n  \n  // Actions\n  setExamInfo: (examInfo: ExamInfo) => void\n  setQuestions: (questions: Question[]) => void\n  setCurrentQuestionIndex: (index: number) => void\n  addAnswer: (answer: Answer) => void\n  updateAnswer: (questionId: string, answer: Partial<Answer>) => void\n  \n  startExam: () => void\n  finishExam: () => void\n  setSubmitting: (isSubmitting: boolean) => void\n  \n  setReadingTimeLeft: (time: number) => void\n  setAnswerTimeLeft: (time: number) => void\n  setIsReading: (isReading: boolean) => void\n  \n  setRecording: (isRecording: boolean) => void\n  setRecordingPath: (path: string) => void\n  \n  reset: () => void\n  \n  // Getters\n  getCurrentQuestion: () => Question | null\n  getTotalQuestions: () => number\n  getProgress: () => number\n}\n\nconst useQuizStore = create<QuizState>((set, get) => ({\n  // Initial state\n  examInfo: null,\n  questions: [],\n  currentQuestionIndex: 0,\n  answers: [],\n  \n  isStarted: false,\n  isFinished: false,\n  isSubmitting: false,\n  \n  readingTimeLeft: 0,\n  answerTimeLeft: 0,\n  isReading: false,\n  \n  isRecording: false,\n  recordingPath: '',\n  \n  // Actions\n  setExamInfo: (examInfo) => set({ examInfo }),\n  setQuestions: (questions) => set({ questions }),\n  setCurrentQuestionIndex: (index) => set({ currentQuestionIndex: index }),\n  \n  addAnswer: (answer) => set((state) => ({\n    answers: [...state.answers, answer]\n  })),\n  \n  updateAnswer: (questionId, answerUpdate) => set((state) => ({\n    answers: state.answers.map(answer => \n      answer.questionId === questionId \n        ? { ...answer, ...answerUpdate }\n        : answer\n    )\n  })),\n  \n  startExam: () => set({ isStarted: true }),\n  finishExam: () => set({ isFinished: true }),\n  setSubmitting: (isSubmitting) => set({ isSubmitting }),\n  \n  setReadingTimeLeft: (time) => set({ readingTimeLeft: time }),\n  setAnswerTimeLeft: (time) => set({ answerTimeLeft: time }),\n  setIsReading: (isReading) => set({ isReading }),\n  \n  setRecording: (isRecording) => set({ isRecording }),\n  setRecordingPath: (path) => set({ recordingPath: path }),\n  \n  reset: () => set({\n    examInfo: null,\n    questions: [],\n    currentQuestionIndex: 0,\n    answers: [],\n    isStarted: false,\n    isFinished: false,\n    isSubmitting: false,\n    readingTimeLeft: 0,\n    answerTimeLeft: 0,\n    isReading: false,\n    isRecording: false,\n    recordingPath: ''\n  }),\n  \n  // Getters\n  getCurrentQuestion: () => {\n    const state = get()\n    return state.questions[state.currentQuestionIndex] || null\n  },\n  \n  getTotalQuestions: () => get().questions.length,\n  \n  getProgress: () => {\n    const state = get()\n    return state.questions.length > 0 \n      ? (state.currentQuestionIndex + 1) / state.questions.length * 100 \n      : 0\n  }\n}))\n\nexport default useQuizStore\n"], "names": [], "sourceRoot": ""}