import { request } from './api'

const PRODUCT_PREFIX = '/gustoenglish'
const PAYMENT_PREFIX = '/payment-go'
const NORMAL_PREFIX = '/community-x'

export type BaseResponse<T> = {
  code: string
  data: T
  msg: string
}

export interface Answer {
  questionId: string
  answer: string
  audioUrl?: string
  duration?: number
}

// 提交答案
export const publishAnswerApi = (id: string, answers: Answer[]) => {
  return request.post(`${PRODUCT_PREFIX}/exams/${id}/submit`, { answers })
}

// 获取题目
export const getQuestionApi = (params: { exam_type: string }) => {
  return request.get(`${PRODUCT_PREFIX}/exams/questions`, params)
}

// 获取考试列表
export const getExamListApi = () => {
  return request.get(`${PRODUCT_PREFIX}/exams`)
}

// 获取历史考试结果
export const getExamScoreApi = () => {
  return request.get(`${PRODUCT_PREFIX}/exams/scores/top`)
}

// 获取答案详情
export const getAnswerDetailApi = (examId: string) => {
  return request.get(`${PRODUCT_PREFIX}/exams/${examId}/answers`)
}

// 微信登录
export const wechatLoginApi = (code: string) => {
  return request.post(`${NORMAL_PREFIX}/auth/wechat/miniprogram`, { code })
}

// 获取用户信息
export const getUserInfoApi = () => {
  return request.get(`${NORMAL_PREFIX}/user/info`)
}

// 获取支付信息
export const getPaymentInfoApi = () => {
  return request.get(`${PAYMENT_PREFIX}/products`)
}

// 创建支付订单
export const createPaymentOrderApi = (productId: string) => {
  return request.post(`${PAYMENT_PREFIX}/orders`, { productId })
}

// 检查支付状态
export const checkPaymentStatusApi = (orderId: string) => {
  return request.get(`${PAYMENT_PREFIX}/orders/${orderId}/status`)
}

// 上传文件
export const uploadFileApi = (filePath: string) => {
  return new Promise((resolve, reject) => {
    Taro.uploadFile({
      url: request.baseUrl + '/upload',
      filePath,
      name: 'file',
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          resolve(data)
        } catch (e) {
          reject(new Error('上传失败'))
        }
      },
      fail: reject
    })
  })
}
