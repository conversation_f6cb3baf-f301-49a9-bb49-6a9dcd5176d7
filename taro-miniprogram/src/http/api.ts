import Taro from '@tarojs/taro'
import { getStorage } from '../utils/storage'

// 创建请求配置
const request = {
  baseUrl: process.env.NODE_ENV === 'production' 
    ? 'https://api.wemore.com' 
    : 'https://apitest.wemore.com',

  get(url: string, data?: any) {
    return this.request(url, 'GET', data)
  },

  post(url: string, data?: any) {
    return this.request(url, 'POST', data)
  },

  put(url: string, data?: any) {
    return this.request(url, 'PUT', data)
  },

  delete(url: string, data?: any) {
    return this.request(url, 'DELETE', data)
  },

  request(url: string, method: any, data?: any) {
    const token = getStorage('token')
    
    return new Promise((resolve, reject) => {
      Taro.request({
        url: this.baseUrl + url,
        data,
        method,
        header: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        },
        success: (res) => {
          if (res.statusCode === 401) {
            // 未授权，跳转到登录页
            Taro.navigateTo({
              url: '/pages/login/index'
            })
            reject(new Error('未授权，请重新登录'))
            return
          }
          
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data)
          } else {
            Taro.showToast({
              title: res.data.msg || '请求失败',
              icon: 'none',
              duration: 2000
            })
            reject(new Error(res.data.msg || '请求失败'))
          }
        },
        fail: (err) => {
          Taro.showToast({
            title: '网络请求失败',
            icon: 'none',
            duration: 2000
          })
          reject(err)
        }
      })
    })
  }
}

export { request }
