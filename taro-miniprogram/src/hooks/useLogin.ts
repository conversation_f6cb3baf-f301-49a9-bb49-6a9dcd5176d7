import { useState } from 'react'
import Taro from '@tarojs/taro'
import { wechatLoginApi, getUserInfoApi } from '../http/http'
import useUserStore from '../store/user'
import { PAGE_PATHS } from '../global/consts'

export default function useLogin() {
  const [loading, setLoading] = useState(false)
  const { setToken, setUserInfo } = useUserStore()

  // 微信登录
  const login = async () => {
    try {
      setLoading(true)
      
      // 获取微信登录凭证
      const { code } = await Taro.login()
      
      // 调用后端登录接口
      const res: any = await wechatLoginApi(code)
      
      if (res.code === '0' && res.data?.token) {
        // 保存token
        setToken(res.data.token)
        
        // 获取用户信息
        const userInfoRes: any = await getUserInfoApi()
        
        if (userInfoRes.code === '0' && userInfoRes.data) {
          setUserInfo(userInfoRes.data)
        }
        
        return true
      } else {
        Taro.showToast({
          title: '登录失败',
          icon: 'none'
        })
        return false
      }
    } catch (error) {
      console.error('登录失败:', error)
      Taro.showToast({
        title: '登录失败',
        icon: 'none'
      })
      return false
    } finally {
      setLoading(false)
    }
  }

  // 检查登录状态
  const checkLogin = () => {
    const { isLoggedIn } = useUserStore.getState()
    
    if (!isLoggedIn) {
      Taro.navigateTo({
        url: PAGE_PATHS.LOGIN
      })
      return false
    }
    
    return true
  }

  return {
    login,
    checkLogin,
    loading
  }
}
