import { useState, useRef } from 'react'
import Taro from '@tarojs/taro'

export default function useRecorder() {
  const [isRecording, setIsRecording] = useState(false)
  const [recordingPath, setRecordingPath] = useState('')
  const recorderManager = useRef<any>(null)

  // 初始化录音管理器
  const initRecorder = () => {
    if (!recorderManager.current) {
      recorderManager.current = Taro.getRecorderManager()
      
      recorderManager.current.onStart(() => {
        console.log('录音开始')
        setIsRecording(true)
      })
      
      recorderManager.current.onStop((res) => {
        console.log('录音结束', res)
        setIsRecording(false)
        setRecordingPath(res.tempFilePath)
      })
      
      recorderManager.current.onError((err) => {
        console.error('录音错误', err)
        setIsRecording(false)
        Taro.showToast({
          title: '录音失败',
          icon: 'none'
        })
      })
    }
  }

  // 检查录音权限
  const checkRecordPermission = async () => {
    try {
      const res = await Taro.getSetting()
      
      if (res.authSetting['scope.record'] === false) {
        // 用户拒绝了录音权限，引导用户去设置页面
        const modalRes = await Taro.showModal({
          title: '录音权限',
          content: '需要录音权限才能进行口语测试，请在设置中开启录音权限',
          confirmText: '去设置',
          cancelText: '取消'
        })
        
        if (modalRes.confirm) {
          await Taro.openSetting()
        }
        return false
      } else if (res.authSetting['scope.record'] === undefined) {
        // 用户还没有授权，请求授权
        try {
          await Taro.authorize({ scope: 'scope.record' })
          return true
        } catch (error) {
          console.error('录音授权失败', error)
          return false
        }
      } else {
        // 用户已经授权
        return true
      }
    } catch (error) {
      console.error('检查录音权限失败', error)
      return false
    }
  }

  // 开始录音
  const startRecord = async () => {
    const hasPermission = await checkRecordPermission()
    
    if (!hasPermission) {
      return false
    }
    
    initRecorder()
    
    try {
      recorderManager.current.start({
        duration: 60000, // 最长录音时间60秒
        sampleRate: 16000,
        numberOfChannels: 1,
        encodeBitRate: 96000,
        format: 'mp3'
      })
      return true
    } catch (error) {
      console.error('开始录音失败', error)
      Taro.showToast({
        title: '录音失败',
        icon: 'none'
      })
      return false
    }
  }

  // 停止录音
  const stopRecord = () => {
    if (recorderManager.current && isRecording) {
      recorderManager.current.stop()
    }
  }

  // 播放录音
  const playRecord = (filePath?: string) => {
    const audioPath = filePath || recordingPath
    
    if (!audioPath) {
      Taro.showToast({
        title: '没有录音文件',
        icon: 'none'
      })
      return
    }
    
    const audioContext = Taro.createInnerAudioContext()
    audioContext.src = audioPath
    audioContext.play()
    
    audioContext.onError((err) => {
      console.error('播放录音失败', err)
      Taro.showToast({
        title: '播放失败',
        icon: 'none'
      })
    })
  }

  return {
    isRecording,
    recordingPath,
    startRecord,
    stopRecord,
    playRecord,
    checkRecordPermission
  }
}
