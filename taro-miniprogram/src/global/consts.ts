export const isProd = process.env.NODE_ENV === 'production'

export const BaseHttpUrl = isProd 
  ? 'https://api.wemore.com' 
  : 'https://apitest.wemore.com'

export const LoginUrl = isProd 
  ? 'https://test.gustoenglish.com/login-quiz' 
  : 'https://gusto-english-exam-test.wemore.com/login-quiz'

// 考试类型
export const EXAM_TYPES = {
  MONTH_ASSESSMENT: 'month-assessment',
  DEV_MOCK: 'dev-mock'
}

// 题目类型
export const QUESTION_TYPES = {
  SINGLE_IMAGE_CHOICE: 'single-image-choice',
  FILL_IN_BLANK: 'fill-in-blank',
  READ_ALOUD: 'read-aloud',
  REPEAT_SENTENCE: 'repeat-sentence',
  WRITE_FROM_DICTATION: 'write-from-dictation',
  SUMMARIZE_WRITTEN_TEXT: 'summarize-written-text',
  QUICK_QA: 'quick-qa',
  DESCRIBE_IMAGE: 'describe-image'
}

// 存储键名
export const STORAGE_KEYS = {
  TOKEN: 'token',
  USER_INFO: 'userInfo',
  EXAM_DATA: 'examData',
  CURRENT_QUESTION: 'currentQuestion',
  ANSWERS: 'answers'
}

// 页面路径
export const PAGE_PATHS = {
  INDEX: '/pages/index/index',
  LOGIN: '/pages/login/index',
  QUIZ: '/pages/quiz/index',
  PAY: '/pages/pay/index',
  MINE: '/pages/mine/index',
  CERTIFICATE: '/pages/certificate/index',
  ANSWER: '/pages/answer/index',
  WAITING: '/pages/waiting/index'
}
