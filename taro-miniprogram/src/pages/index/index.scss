.index {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 40px 30px;
  background-color: #f8f9fa;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60px;

  .logo {
    width: 120px;
    height: 120px;
    margin-bottom: 20px;
  }

  .title {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
  }

  .subtitle {
    font-size: 18px;
    color: #666;
  }
}

.exam-section {
  margin-bottom: 40px;

  .section-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
    display: block;
  }

  .exam-cards {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .exam-card {
      background-color: #fff;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      .card-header {
        margin-bottom: 15px;

        .card-title {
          font-size: 18px;
          font-weight: bold;
          color: #333;
          margin-bottom: 5px;
          display: block;
        }

        .card-desc {
          font-size: 14px;
          color: #666;
          display: block;
        }
      }

      .card-content {
        margin-bottom: 20px;

        .card-info {
          font-size: 14px;
          color: #666;
          line-height: 1.6;
          display: block;
        }
      }

      .start-btn {
        width: 100%;
        height: 45px;
        border-radius: 8px;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.quick-actions {
  margin-top: 20px;

  .history-btn {
    width: 100%;
    height: 45px;
    border-radius: 8px;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
