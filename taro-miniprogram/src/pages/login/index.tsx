import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { useState } from 'react'
import useLogin from '../../hooks/useLogin'
import Button from '../../components/Button'
import { PAGE_PATHS } from '../../global/consts'
import './index.scss'

export default function Login() {
  const { login, loading } = useLogin()
  const [agreementChecked, setAgreementChecked] = useState(false)

  const handleLogin = async () => {
    if (!agreementChecked) {
      Taro.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    const success = await login()
    
    if (success) {
      // 登录成功，跳转到首页
      Taro.switchTab({
        url: PAGE_PATHS.INDEX
      })
    }
  }

  const handleAgreementChange = () => {
    setAgreementChecked(!agreementChecked)
  }

  const handleViewAgreement = () => {
    // 查看用户协议
    Taro.showModal({
      title: '用户协议',
      content: '这里是用户协议内容...',
      showCancel: false
    })
  }

  return (
    <View className="login-container">
      <View className="login-header">
        <Image 
          className="logo"
          src="/images/logo.png"
          mode="aspectFit"
        />
        <Text className="title">Gusto English</Text>
        <Text className="subtitle">高拓英语水平测试</Text>
      </View>

      <View className="login-content">
        <View className="welcome-text">
          <Text className="welcome-title">欢迎使用</Text>
          <Text className="welcome-desc">
            专业的英语水平测试平台，为您提供准确的英语能力评估
          </Text>
        </View>

        <View className="login-form">
          <Button
            className="login-btn"
            onClick={handleLogin}
            disabled={loading}
          >
            {loading ? '登录中...' : '微信快速登录'}
          </Button>

          <View className="agreement-section">
            <View 
              className="checkbox-container"
              onClick={handleAgreementChange}
            >
              <View className={`checkbox ${agreementChecked ? 'checked' : ''}`}>
                {agreementChecked && <Text className="checkmark">✓</Text>}
              </View>
              <Text className="agreement-text">
                我已阅读并同意
                <Text 
                  className="agreement-link"
                  onClick={handleViewAgreement}
                >
                  《用户协议》
                </Text>
              </Text>
            </View>
          </View>
        </View>
      </View>

      <View className="login-footer">
        <Text className="footer-text">
          登录即表示您同意我们的服务条款和隐私政策
        </Text>
      </View>
    </View>
  )
}
