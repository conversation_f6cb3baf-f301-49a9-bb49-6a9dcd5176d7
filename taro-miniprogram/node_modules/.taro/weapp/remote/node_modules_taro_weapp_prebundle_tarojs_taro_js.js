(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["node_modules_taro_weapp_prebundle_tarojs_taro_js"],{

/***/ "./node_modules/.taro/weapp/prebundle/@tarojs_taro.core.js":
/*!*****************************************************************!*\
  !*** ./node_modules/.taro/weapp/prebundle/@tarojs_taro.core.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-4JFQ53LR.js */ "./node_modules/.taro/weapp/prebundle/chunk-4JFQ53LR.js");
/* harmony import */ var _chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-7MJDXN2B.js */ "./node_modules/.taro/weapp/prebundle/chunk-7MJDXN2B.js");
/* harmony import */ var _chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-QRPWKJ4C.js */ "./node_modules/.taro/weapp/prebundle/chunk-QRPWKJ4C.js");



// node_modules/@tarojs/api/dist/env.js
function getEnv() {
    if (true) {
        return ENV_TYPE.WEAPP;
    } else {}
}
var ENV_TYPE;
var init_env = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_2__.__esm)({
    "node_modules/@tarojs/api/dist/env.js" () {
        ENV_TYPE = {
            ASCF: "ASCF",
            WEAPP: "WEAPP",
            SWAN: "SWAN",
            ALIPAY: "ALIPAY",
            TT: "TT",
            QQ: "QQ",
            JD: "JD",
            WEB: "WEB",
            RN: "RN",
            HARMONY: "HARMONY",
            QUICKAPP: "QUICKAPP",
            HARMONYHYBRID: "HARMONYHYBRID"
        };
    }
});
// node_modules/@tarojs/api/dist/interceptor/chain.js
var Chain;
var init_chain = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_2__.__esm)({
    "node_modules/@tarojs/api/dist/interceptor/chain.js" () {
        (0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.init_dist)();
        Chain = class _Chain {
            proceed(requestParams = {}) {
                this.requestParams = requestParams;
                if (this.index >= this.interceptors.length) {
                    throw new Error("chain \u53C2\u6570\u9519\u8BEF, \u8BF7\u52FF\u76F4\u63A5\u4FEE\u6539 request.chain");
                }
                const nextInterceptor = this._getNextInterceptor();
                const nextChain = this._getNextChain();
                const p = nextInterceptor(nextChain);
                const res = p.catch((err)=>Promise.reject(err));
                Object.keys(p).forEach((k)=>(0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.isFunction)(p[k]) && (res[k] = p[k]));
                return res;
            }
            _getNextInterceptor() {
                return this.interceptors[this.index];
            }
            _getNextChain() {
                return new _Chain(this.requestParams, this.interceptors, this.index + 1);
            }
            constructor(requestParams, interceptors, index){
                this.index = index || 0;
                this.requestParams = requestParams || {};
                this.interceptors = interceptors || [];
            }
        };
    }
});
// node_modules/@tarojs/api/dist/interceptor/index.js
function interceptorify(promiseifyApi) {
    return new Link(function(chain) {
        return promiseifyApi(chain.requestParams);
    });
}
var Link;
var init_interceptor = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_2__.__esm)({
    "node_modules/@tarojs/api/dist/interceptor/index.js" () {
        init_chain();
        Link = class {
            request(requestParams) {
                const chain = this.chain;
                const taroInterceptor = this.taroInterceptor;
                chain.interceptors = chain.interceptors.filter((interceptor)=>interceptor !== taroInterceptor).concat(taroInterceptor);
                return chain.proceed(Object.assign({}, requestParams));
            }
            addInterceptor(interceptor) {
                this.chain.interceptors.push(interceptor);
            }
            cleanInterceptors() {
                this.chain = new Chain();
            }
            constructor(interceptor){
                this.taroInterceptor = interceptor;
                this.chain = new Chain();
            }
        };
    }
});
// node_modules/@tarojs/api/dist/interceptor/interceptors.js
var interceptors_exports = {};
(0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_2__.__export)(interceptors_exports, {
    logInterceptor: ()=>logInterceptor,
    timeoutInterceptor: ()=>timeoutInterceptor
});
function timeoutInterceptor(chain) {
    const requestParams = chain.requestParams;
    let p;
    const res = new Promise((resolve, reject)=>{
        const timeout = setTimeout(()=>{
            clearTimeout(timeout);
            reject(new Error("\u7F51\u7EDC\u94FE\u63A5\u8D85\u65F6,\u8BF7\u7A0D\u540E\u518D\u8BD5\uFF01"));
        }, requestParams && requestParams.timeout || 6e4);
        p = chain.proceed(requestParams);
        p.then((res2)=>{
            if (!timeout) return;
            clearTimeout(timeout);
            resolve(res2);
        }).catch((err)=>{
            timeout && clearTimeout(timeout);
            reject(err);
        });
    });
    if (!(0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.isUndefined)(p) && (0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.isFunction)(p.abort)) res.abort = p.abort;
    return res;
}
function logInterceptor(chain) {
    const requestParams = chain.requestParams;
    const { method, data, url } = requestParams;
    console.log(`http ${method || "GET"} --> ${url} data: `, data);
    const p = chain.proceed(requestParams);
    const res = p.then((res2)=>{
        console.log(`http <-- ${url} result:`, res2);
        return res2;
    });
    if ((0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.isFunction)(p.abort)) res.abort = p.abort;
    return res;
}
var init_interceptors = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_2__.__esm)({
    "node_modules/@tarojs/api/dist/interceptor/interceptors.js" () {
        (0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.init_dist)();
    }
});
// node_modules/@tarojs/api/dist/tools.js
function Behavior(options2) {
    return options2;
}
function getPreload(current) {
    return function(key, val) {
        current.preloadData = (0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.isObject)(key) ? key : {
            [key]: val
        };
    };
}
function getInitPxTransform(taro) {
    return function(config) {
        const { designWidth = defaultDesignWidth, deviceRatio = defaultDesignRatio, baseFontSize = defaultBaseFontSize, targetUnit = defaultTargetUnit, unitPrecision = defaultUnitPrecision } = config;
        taro.config = taro.config || {};
        taro.config.designWidth = designWidth;
        taro.config.deviceRatio = deviceRatio;
        taro.config.baseFontSize = baseFontSize;
        taro.config.targetUnit = targetUnit;
        taro.config.unitPrecision = unitPrecision;
    };
}
function getPxTransform(taro) {
    return function(size) {
        const config = taro.config || {};
        const baseFontSize = config.baseFontSize;
        const deviceRatio = config.deviceRatio || defaultDesignRatio;
        const designWidth = ((input = 0)=>(0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.isFunction)(config.designWidth) ? config.designWidth(input) : config.designWidth || defaultDesignWidth)(size);
        if (!(designWidth in deviceRatio)) {
            throw new Error(`deviceRatio \u914D\u7F6E\u4E2D\u4E0D\u5B58\u5728 ${designWidth} \u7684\u8BBE\u7F6E\uFF01`);
        }
        const targetUnit = config.targetUnit || defaultTargetUnit;
        const unitPrecision = config.unitPrecision || defaultUnitPrecision;
        const formatSize = ~~size;
        let rootValue = 1 / deviceRatio[designWidth];
        switch(targetUnit){
            case "rem":
                rootValue *= baseFontSize * 2;
                break;
            case "px":
                rootValue *= 2;
                break;
        }
        let val = formatSize / rootValue;
        if (unitPrecision >= 0 && unitPrecision <= 100) {
            val = Number(val.toFixed(unitPrecision));
        }
        return val + targetUnit;
    };
}
var defaultDesignWidth, defaultDesignRatio, defaultBaseFontSize, defaultUnitPrecision, defaultTargetUnit;
var init_tools = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_2__.__esm)({
    "node_modules/@tarojs/api/dist/tools.js" () {
        (0,_chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.init_dist)();
        defaultDesignWidth = 750;
        defaultDesignRatio = {
            640: 2.34 / 2,
            750: 1,
            828: 1.81 / 2
        };
        defaultBaseFontSize = 20;
        defaultUnitPrecision = 5;
        defaultTargetUnit = "rpx";
    }
});
// node_modules/@tarojs/api/dist/index.js
var dist_exports2 = {};
(0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_2__.__export)(dist_exports2, {
    default: ()=>Taro
});
var Taro;
var init_dist3 = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_2__.__esm)({
    "node_modules/@tarojs/api/dist/index.js" () {
        (0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.init_dist)();
        init_env();
        init_interceptor();
        init_interceptors();
        init_tools();
        Taro = {
            Behavior,
            getEnv,
            ENV_TYPE,
            Link,
            interceptors: interceptors_exports,
            Current: _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current,
            getCurrentInstance: _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.getCurrentInstance,
            options: _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.options,
            nextTick: _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.nextTick,
            eventCenter: _chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.eventCenter,
            Events: _chunk_7MJDXN2B_js__WEBPACK_IMPORTED_MODULE_1__.Events,
            getInitPxTransform,
            interceptorify
        };
        Taro.initPxTransform = getInitPxTransform(Taro);
        Taro.preload = getPreload(_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.Current);
        Taro.pxTransform = getPxTransform(Taro);
    }
});
// node_modules/@tarojs/taro/index.js
var require_taro = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_2__.__commonJS)({
    "node_modules/@tarojs/taro/index.js" (exports, module) {
        var { hooks } = ((0,_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.init_dist)(), (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_2__.__toCommonJS)(_chunk_4JFQ53LR_js__WEBPACK_IMPORTED_MODULE_0__.dist_exports));
        var taro = (init_dist3(), (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_2__.__toCommonJS)(dist_exports2)).default;
        if (hooks.isExist("initNativeApi")) {
            hooks.call("initNativeApi", taro);
        }
        module.exports = taro;
        module.exports.default = module.exports;
    }
});
// entry:@tarojs_taro
var require_tarojs_taro = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_2__.__commonJS)({
    "entry:@tarojs_taro" (exports, module) {
        module.exports = require_taro();
    }
});
/* harmony default export */ __webpack_exports__["default"] = (require_tarojs_taro());


/***/ }),

/***/ "./node_modules/.taro/weapp/prebundle/@tarojs_taro.js":
/*!************************************************************!*\
  !*** ./node_modules/.taro/weapp/prebundle/@tarojs_taro.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

var m = __webpack_require__(/*! ./@tarojs_taro.core.js */ "./node_modules/.taro/weapp/prebundle/@tarojs_taro.core.js");
                   module.exports = m.default;
                   exports["default"] = module.exports;
                  

/***/ })

}]);