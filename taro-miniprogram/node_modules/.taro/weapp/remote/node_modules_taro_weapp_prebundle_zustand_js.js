"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["node_modules_taro_weapp_prebundle_zustand_js"],{

/***/ "./node_modules/.taro/weapp/prebundle/chunk-QRPWKJ4C.js":
/*!**************************************************************!*\
  !*** ./node_modules/.taro/weapp/prebundle/chunk-QRPWKJ4C.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   __commonJS: function() { return /* binding */ __commonJS; },
/* harmony export */   __esm: function() { return /* binding */ __esm; },
/* harmony export */   __export: function() { return /* binding */ __export; },
/* harmony export */   __toCommonJS: function() { return /* binding */ __toCommonJS; },
/* harmony export */   __toESM: function() { return /* binding */ __toESM; }
/* harmony export */ });
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __esm = (fn, res)=>function __init() {
        return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;
    };
var __commonJS = (cb, mod)=>function __require() {
        return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = {
            exports: {}
        }).exports, mod), mod.exports;
    };
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);



/***/ }),

/***/ "./node_modules/.taro/weapp/prebundle/zustand.js":
/*!*******************************************************!*\
  !*** ./node_modules/.taro/weapp/prebundle/zustand.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   create: function() { return /* binding */ create; },
/* harmony export */   createStore: function() { return /* binding */ createStore; },
/* harmony export */   useStore: function() { return /* binding */ useStore; }
/* harmony export */ });
/* harmony import */ var _chunk_XDFXK7K5_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-XDFXK7K5.js */ "./node_modules/.taro/weapp/prebundle/chunk-XDFXK7K5.js");
/* harmony import */ var _chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-QRPWKJ4C.js */ "./node_modules/.taro/weapp/prebundle/chunk-QRPWKJ4C.js");


// node_modules/zustand/esm/vanilla.mjs
var createStoreImpl = (createState)=>{
    let state;
    const listeners = /* @__PURE__ */ new Set();
    const setState = (partial, replace)=>{
        const nextState = typeof partial === "function" ? partial(state) : partial;
        if (!Object.is(nextState, state)) {
            const previousState = state;
            state = (replace != null ? replace : typeof nextState !== "object" || nextState === null) ? nextState : Object.assign({}, state, nextState);
            listeners.forEach((listener)=>listener(state, previousState));
        }
    };
    const getState = ()=>state;
    const getInitialState = ()=>initialState;
    const subscribe = (listener)=>{
        listeners.add(listener);
        return ()=>listeners.delete(listener);
    };
    const api = {
        setState,
        getState,
        getInitialState,
        subscribe
    };
    const initialState = state = createState(setState, getState, api);
    return api;
};
var createStore = (createState)=>createState ? createStoreImpl(createState) : createStoreImpl;
// node_modules/zustand/esm/react.mjs
var import_react = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_1__.__toESM)((0,_chunk_XDFXK7K5_js__WEBPACK_IMPORTED_MODULE_0__.require_react)(), 1);
var identity = (arg)=>arg;
function useStore(api, selector = identity) {
    const slice = import_react.default.useSyncExternalStore(api.subscribe, ()=>selector(api.getState()), ()=>selector(api.getInitialState()));
    import_react.default.useDebugValue(slice);
    return slice;
}
var createImpl = (createState)=>{
    const api = createStore(createState);
    const useBoundStore = (selector)=>useStore(api, selector);
    Object.assign(useBoundStore, api);
    return useBoundStore;
};
var create = (createState)=>createState ? createImpl(createState) : createImpl;



/***/ })

}]);