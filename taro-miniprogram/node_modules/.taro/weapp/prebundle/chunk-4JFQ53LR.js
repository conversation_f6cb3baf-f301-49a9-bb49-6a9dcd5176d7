import { EMPTY_OBJ, Events, PLATFORM_TYPE, controlledComponent, ensure, getComponents<PERSON>lias, hooks, init_dist, internalComponents, isArray, isFunction, isNull, isNumber, isObject, isString, isUndefined, noop, toCamelCase, toDashed, warn } from "./chunk-7MJDXN2B.js";
import { __esm, __export } from "./chunk-QRPWKJ4C.js";
// node_modules/@tarojs/runtime/dist/constants/index.js
var PROPERTY_THRESHOLD, TARO_RUNTIME, HOOKS_APP_ID, SET_DATA, PAGE_INIT, ROOT_STR, HTML, HEAD, BODY, APP, CONTAINER, DOCUMENT_ELEMENT_NAME, DOCUMENT_FRAGMENT, ID, UID, CLASS, STYLE, FOCUS, VIEW, STATIC_VIEW, PURE_VIEW, CLICK_VIEW, PROPS, DAT<PERSON><PERSON>, OBJECT, VALUE, INPUT, CHANGE, CUS<PERSON>M_WRAPPER, TARGET, CURRENT_TARGET, TYPE, CONFIRM, TIME_STAMP, KEY_CODE, TOUCHMOVE, DATE, SET_TIMEOUT, COMPILE_MODE, CATCHMOVE, CATCH_VIEW, COMMENT, ON_LOAD, ON_READY, ON_SHOW, ON_HIDE, OPTIONS, EXTERNAL_CLASSES, EVENT_CALLBACK_RESULT, BEHAVIORS, A, CONTEXT_ACTIONS;
var init_constants = __esm({
    "node_modules/@tarojs/runtime/dist/constants/index.js" () {
        PROPERTY_THRESHOLD = 2046;
        TARO_RUNTIME = "Taro runtime";
        HOOKS_APP_ID = "taro-app";
        SET_DATA = "\u5C0F\u7A0B\u5E8F setData";
        PAGE_INIT = "\u9875\u9762\u521D\u59CB\u5316";
        ROOT_STR = "root";
        HTML = "html";
        HEAD = "head";
        BODY = "body";
        APP = "app";
        CONTAINER = "container";
        DOCUMENT_ELEMENT_NAME = "#document";
        DOCUMENT_FRAGMENT = "document-fragment";
        ID = "id";
        UID = "uid";
        CLASS = "class";
        STYLE = "style";
        FOCUS = "focus";
        VIEW = "view";
        STATIC_VIEW = "static-view";
        PURE_VIEW = "pure-view";
        CLICK_VIEW = "click-view";
        PROPS = "props";
        DATASET = "dataset";
        OBJECT = "object";
        VALUE = "value";
        INPUT = "input";
        CHANGE = "change";
        CUSTOM_WRAPPER = "custom-wrapper";
        TARGET = "target";
        CURRENT_TARGET = "currentTarget";
        TYPE = "type";
        CONFIRM = "confirm";
        TIME_STAMP = "timeStamp";
        KEY_CODE = "keyCode";
        TOUCHMOVE = "touchmove";
        DATE = "Date";
        SET_TIMEOUT = "setTimeout";
        COMPILE_MODE = "compileMode";
        CATCHMOVE = "catchMove";
        CATCH_VIEW = "catch-view";
        COMMENT = "comment";
        ON_LOAD = "onLoad";
        ON_READY = "onReady";
        ON_SHOW = "onShow";
        ON_HIDE = "onHide";
        OPTIONS = "options";
        EXTERNAL_CLASSES = "externalClasses";
        EVENT_CALLBACK_RESULT = "e_result";
        BEHAVIORS = "behaviors";
        A = "a";
        (function(CONTEXT_ACTIONS2) {
            CONTEXT_ACTIONS2["INIT"] = "0";
            CONTEXT_ACTIONS2["RESTORE"] = "1";
            CONTEXT_ACTIONS2["RECOVER"] = "2";
            CONTEXT_ACTIONS2["DESTORY"] = "3";
        })(CONTEXT_ACTIONS || (CONTEXT_ACTIONS = {}));
    }
});
// node_modules/@tarojs/runtime/dist/dom-external/mutation-observer/implements.js
function logMutation(observer, record) {
    observer.records.push(record);
    if (!pendingMuatations) {
        pendingMuatations = true;
        Promise.resolve().then(()=>{
            pendingMuatations = false;
            observers.forEach((observer2)=>{
                return observer2.callback(observer2.takeRecords());
            });
        });
    }
}
function recordMutation(record) {
    observers.forEach((observer)=>{
        const { options: options2 } = observer;
        for(let t = record.target; t; t = t.parentNode){
            if (sidMatches(observer.target, t) && isConcerned(record, options2)) {
                logMutation(observer, record);
                break;
            }
            if (!options2.subtree) break;
        }
    });
}
var observers, MutationObserverImpl, sidMatches, isConcerned, pendingMuatations;
var init_implements = __esm({
    "node_modules/@tarojs/runtime/dist/dom-external/mutation-observer/implements.js" () {
        observers = [];
        MutationObserverImpl = class {
            /**
       * Configures the MutationObserver
       * to begin receiving notifications
       * through its callback function
       * when DOM changes matching the given options occur.
       *
       * Options matching is to be implemented.
       */ observe(target, options2) {
                this.disconnect();
                this.target = target;
                this.options = options2 || {};
                observers.push(this);
            }
            /**
       * Stop the MutationObserver instance
       * from receiving further notifications
       * until and unless observe() is called again.
       */ disconnect() {
                this.target = null;
                const index = observers.indexOf(this);
                if (index >= 0) {
                    observers.splice(index, 1);
                }
            }
            /**
       * Removes all pending notifications
       * from the MutationObserver's notification queue
       * and returns them in a new Array of MutationRecord objects.
       */ takeRecords() {
                return this.records.splice(0, this.records.length);
            }
            constructor(callback){
                this.records = [];
                this.callback = callback;
            }
        };
        sidMatches = (observerTarget, target)=>{
            return !!observerTarget && observerTarget.sid === (target === null || target === void 0 ? void 0 : target.sid);
        };
        isConcerned = (record, options2)=>{
            const { characterData, characterDataOldValue, attributes, attributeOldValue, childList } = options2;
            switch(record.type){
                case "characterData":
                    if (characterData) {
                        if (!characterDataOldValue) record.oldValue = null;
                        return true;
                    }
                    return false;
                case "attributes":
                    if (attributes) {
                        if (!attributeOldValue) record.oldValue = null;
                        return true;
                    }
                    return false;
                case "childList":
                    if (childList) {
                        return true;
                    }
                    return false;
            }
        };
        pendingMuatations = false;
    }
});
// node_modules/@tarojs/runtime/dist/dom-external/mutation-observer/index.js
var MutationObserver2;
var init_mutation_observer = __esm({
    "node_modules/@tarojs/runtime/dist/dom-external/mutation-observer/index.js" () {
        init_dist();
        init_implements();
        MutationObserver2 = class {
            observe(...args) {
                this.core.observe(...args);
            }
            disconnect() {
                this.core.disconnect();
            }
            takeRecords() {
                return this.core.takeRecords();
            }
            static record(record) {
                recordMutation(record);
            }
            constructor(callback){
                if (ENABLE_MUTATION_OBSERVER) {
                    this.core = new MutationObserverImpl(callback);
                } else {
                    if (true) {
                        console.warn("[Taro Warning] \u82E5\u8981\u4F7F\u7528 MutationObserver\uFF0C\u8BF7\u5728 Taro \u7F16\u8BD1\u914D\u7F6E\u4E2D\u8BBE\u7F6E 'mini.runtime.enableMutationObserver: true'");
                    }
                    this.core = {
                        observe: noop,
                        disconnect: noop,
                        takeRecords: noop
                    };
                }
            }
        };
    }
});
// node_modules/@tarojs/runtime/dist/emitter/emitter.js
var eventCenter;
var init_emitter = __esm({
    "node_modules/@tarojs/runtime/dist/emitter/emitter.js" () {
        init_dist();
        init_dist();
        eventCenter = hooks.call("getEventCenter", Events);
    }
});
// node_modules/@tarojs/runtime/dist/env.js
var env;
var init_env = __esm({
    "node_modules/@tarojs/runtime/dist/env.js" () {
        init_dist();
        env = {
            window: false ? window : EMPTY_OBJ,
            document: false ? document : EMPTY_OBJ
        };
    }
});
// node_modules/@tarojs/runtime/dist/bom/getComputedStyle.js
var taroGetComputedStyleProvider;
var init_getComputedStyle = __esm({
    "node_modules/@tarojs/runtime/dist/bom/getComputedStyle.js" () {
        init_env();
        taroGetComputedStyleProvider = false ? env.window.getComputedStyle : function(element) {
            return element.style;
        };
    }
});
// node_modules/tslib/tslib.es6.mjs
function __classPrivateFieldGet(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
}
function __classPrivateFieldSet(receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
}
var init_tslib_es6 = __esm({
    "node_modules/tslib/tslib.es6.mjs" () {}
});
// node_modules/@tarojs/runtime/dist/utils/cache.js
var RuntimeCache;
var init_cache = __esm({
    "node_modules/@tarojs/runtime/dist/utils/cache.js" () {
        RuntimeCache = class {
            has(identifier) {
                return this.cache.has(identifier);
            }
            set(identifier, ctx) {
                if (identifier && ctx) {
                    this.cache.set(identifier, ctx);
                }
            }
            get(identifier) {
                if (this.has(identifier)) return this.cache.get(identifier);
            }
            delete(identifier) {
                this.cache.delete(identifier);
            }
            constructor(name){
                this.cache = /* @__PURE__ */ new Map();
                this.name = name;
            }
        };
    }
});
// node_modules/@tarojs/runtime/dist/bom/history.js
var _TaroHistory_instances, _TaroHistory_location, _TaroHistory_stack, _TaroHistory_cur, _TaroHistory_window, _TaroHistory_reset, cache, TaroHistory, History;
var init_history = __esm({
    "node_modules/@tarojs/runtime/dist/bom/history.js" () {
        init_tslib_es6();
        init_dist();
        init_constants();
        init_emitter();
        init_env();
        init_cache();
        cache = new RuntimeCache("history");
        TaroHistory = class extends Events {
            /* public property */ get length() {
                return __classPrivateFieldGet(this, _TaroHistory_stack, "f").length;
            }
            get state() {
                return __classPrivateFieldGet(this, _TaroHistory_stack, "f")[__classPrivateFieldGet(this, _TaroHistory_cur, "f")].state;
            }
            /* public method */ go(delta) {
                if (!isNumber(delta) || isNaN(delta)) return;
                let targetIdx = __classPrivateFieldGet(this, _TaroHistory_cur, "f") + delta;
                targetIdx = Math.min(Math.max(targetIdx, 0), this.length - 1);
                __classPrivateFieldSet(this, _TaroHistory_cur, targetIdx, "f");
                __classPrivateFieldGet(this, _TaroHistory_location, "f").trigger("__set_href_without_history__", __classPrivateFieldGet(this, _TaroHistory_stack, "f")[__classPrivateFieldGet(this, _TaroHistory_cur, "f")].url);
                __classPrivateFieldGet(this, _TaroHistory_window, "f").trigger("popstate", __classPrivateFieldGet(this, _TaroHistory_stack, "f")[__classPrivateFieldGet(this, _TaroHistory_cur, "f")]);
            }
            back() {
                this.go(-1);
            }
            forward() {
                this.go(1);
            }
            pushState(state, title, url) {
                if (!url || !isString(url)) return;
                __classPrivateFieldSet(this, _TaroHistory_stack, __classPrivateFieldGet(this, _TaroHistory_stack, "f").slice(0, __classPrivateFieldGet(this, _TaroHistory_cur, "f") + 1), "f");
                __classPrivateFieldGet(this, _TaroHistory_stack, "f").push({
                    state,
                    title,
                    url
                });
                __classPrivateFieldSet(this, _TaroHistory_cur, this.length - 1, "f");
                __classPrivateFieldGet(this, _TaroHistory_location, "f").trigger("__set_href_without_history__", url);
            }
            replaceState(state, title, url) {
                if (!url || !isString(url)) return;
                __classPrivateFieldGet(this, _TaroHistory_stack, "f")[__classPrivateFieldGet(this, _TaroHistory_cur, "f")] = {
                    state,
                    title,
                    url
                };
                __classPrivateFieldGet(this, _TaroHistory_location, "f").trigger("__set_href_without_history__", url);
            }
            // For debug
            get cache() {
                return cache;
            }
            constructor(location, options2){
                super();
                _TaroHistory_instances.add(this);
                _TaroHistory_location.set(this, void 0);
                _TaroHistory_stack.set(this, []);
                _TaroHistory_cur.set(this, 0);
                _TaroHistory_window.set(this, void 0);
                __classPrivateFieldSet(this, _TaroHistory_window, options2.window, "f");
                __classPrivateFieldSet(this, _TaroHistory_location, location, "f");
                __classPrivateFieldGet(this, _TaroHistory_location, "f").on("__record_history__", (href)=>{
                    var _a2;
                    __classPrivateFieldSet(this, _TaroHistory_cur, (_a2 = __classPrivateFieldGet(this, _TaroHistory_cur, "f"), _a2++, _a2), "f");
                    __classPrivateFieldSet(this, _TaroHistory_stack, __classPrivateFieldGet(this, _TaroHistory_stack, "f").slice(0, __classPrivateFieldGet(this, _TaroHistory_cur, "f")), "f");
                    __classPrivateFieldGet(this, _TaroHistory_stack, "f").push({
                        state: null,
                        title: "",
                        url: href
                    });
                }, null);
                __classPrivateFieldGet(this, _TaroHistory_location, "f").on("__reset_history__", (href)=>{
                    __classPrivateFieldGet(this, _TaroHistory_instances, "m", _TaroHistory_reset).call(this, href);
                }, null);
                this.on(CONTEXT_ACTIONS.INIT, ()=>{
                    __classPrivateFieldGet(this, _TaroHistory_instances, "m", _TaroHistory_reset).call(this);
                }, null);
                this.on(CONTEXT_ACTIONS.RESTORE, (pageId2)=>{
                    cache.set(pageId2, {
                        location: __classPrivateFieldGet(this, _TaroHistory_location, "f"),
                        stack: __classPrivateFieldGet(this, _TaroHistory_stack, "f").slice(),
                        cur: __classPrivateFieldGet(this, _TaroHistory_cur, "f")
                    });
                }, null);
                this.on(CONTEXT_ACTIONS.RECOVER, (pageId2)=>{
                    if (cache.has(pageId2)) {
                        const ctx = cache.get(pageId2);
                        __classPrivateFieldSet(this, _TaroHistory_location, ctx.location, "f");
                        __classPrivateFieldSet(this, _TaroHistory_stack, ctx.stack, "f");
                        __classPrivateFieldSet(this, _TaroHistory_cur, ctx.cur, "f");
                    }
                }, null);
                this.on(CONTEXT_ACTIONS.DESTORY, (pageId2)=>{
                    cache.delete(pageId2);
                }, null);
                __classPrivateFieldGet(this, _TaroHistory_instances, "m", _TaroHistory_reset).call(this);
            }
        };
        _TaroHistory_location = /* @__PURE__ */ new WeakMap(), _TaroHistory_stack = /* @__PURE__ */ new WeakMap(), _TaroHistory_cur = /* @__PURE__ */ new WeakMap(), _TaroHistory_window = /* @__PURE__ */ new WeakMap(), _TaroHistory_instances = /* @__PURE__ */ new WeakSet(), _TaroHistory_reset = function _TaroHistory_reset2(href = "") {
            __classPrivateFieldSet(this, _TaroHistory_stack, [
                {
                    state: null,
                    title: "",
                    url: href || __classPrivateFieldGet(this, _TaroHistory_location, "f").href
                }
            ], "f");
            __classPrivateFieldSet(this, _TaroHistory_cur, 0, "f");
        };
        History = false ? env.window.History : TaroHistory;
    }
});
// node_modules/@tarojs/runtime/dist/current.js
var Current, getCurrentInstance;
var init_current = __esm({
    "node_modules/@tarojs/runtime/dist/current.js" () {
        Current = {
            app: null,
            router: null,
            page: null
        };
        getCurrentInstance = ()=>Current;
    }
});
// node_modules/@tarojs/runtime/dist/bom/URLSearchParams.js
function replacer(match) {
    return replaceCharMap[match];
}
function appendTo(dict, name, value) {
    const res = isArray(value) ? value.join(",") : value;
    if (name in dict) dict[name].push(res);
    else dict[name] = [
        res
    ];
}
function addEach(value, key) {
    appendTo(this, key, value);
}
function decode(str) {
    return decodeURIComponent(str.replace(plusReg, " "));
}
function encode(str) {
    return encodeURIComponent(str).replace(findReg, replacer);
}
var _dict, _a, findReg, plusReg, replaceCharMap, URLSearchParams;
var init_URLSearchParams = __esm({
    "node_modules/@tarojs/runtime/dist/bom/URLSearchParams.js" () {
        init_tslib_es6();
        init_dist();
        init_env();
        findReg = /[!'()~]|%20|%00/g;
        plusReg = /\+/g;
        replaceCharMap = {
            "!": "%21",
            "'": "%27",
            "(": "%28",
            ")": "%29",
            "~": "%7E",
            "%20": "+",
            "%00": "\0"
        };
        URLSearchParams = false ? env.window.URLSearchParams : (_a = class {
            append(name, value) {
                appendTo(__classPrivateFieldGet(this, _dict, "f"), name, value);
            }
            delete(name) {
                delete __classPrivateFieldGet(this, _dict, "f")[name];
            }
            get(name) {
                const dict = __classPrivateFieldGet(this, _dict, "f");
                return name in dict ? dict[name][0] : null;
            }
            getAll(name) {
                const dict = __classPrivateFieldGet(this, _dict, "f");
                return name in dict ? dict[name].slice(0) : [];
            }
            has(name) {
                return name in __classPrivateFieldGet(this, _dict, "f");
            }
            keys() {
                return Object.keys(__classPrivateFieldGet(this, _dict, "f"));
            }
            set(name, value) {
                __classPrivateFieldGet(this, _dict, "f")[name] = [
                    "" + value
                ];
            }
            forEach(callback, thisArg) {
                const dict = __classPrivateFieldGet(this, _dict, "f");
                Object.getOwnPropertyNames(dict).forEach(function(name) {
                    dict[name].forEach(function(value) {
                        callback.call(thisArg, value, name, this);
                    }, this);
                }, this);
            }
            toJSON() {
                return {};
            }
            toString() {
                const dict = __classPrivateFieldGet(this, _dict, "f");
                const query = [];
                for(const key in dict){
                    const name = encode(key);
                    for(let i = 0, value = dict[key]; i < value.length; i++){
                        query.push(name + "=" + encode(value[i]));
                    }
                }
                return query.join("&");
            }
            constructor(query){
                _dict.set(this, /* @__PURE__ */ Object.create(null));
                query !== null && query !== void 0 ? query : query = "";
                const dict = __classPrivateFieldGet(this, _dict, "f");
                if (typeof query === "string") {
                    if (query.charAt(0) === "?") {
                        query = query.slice(1);
                    }
                    for(let pairs = query.split("&"), i = 0, length = pairs.length; i < length; i++){
                        const value = pairs[i];
                        const index = value.indexOf("=");
                        try {
                            if (index > -1) {
                                appendTo(dict, decode(value.slice(0, index)), decode(value.slice(index + 1)));
                            } else if (value.length) {
                                appendTo(dict, decode(value), "");
                            }
                        } catch (err) {
                            if (true) {
                                console.warn(`[Taro warn] URL \u53C2\u6570 ${value} decode \u5F02\u5E38`);
                            }
                        }
                    }
                } else {
                    if (isArray(query)) {
                        for(let i = 0, length = query.length; i < length; i++){
                            const value = query[i];
                            appendTo(dict, value[0], value[1]);
                        }
                    } else if (query.forEach) {
                        query.forEach(addEach, dict);
                    } else {
                        for(const key in query){
                            appendTo(dict, key, query[key]);
                        }
                    }
                }
            }
        }, _dict = /* @__PURE__ */ new WeakMap(), _a);
    }
});
// node_modules/@tarojs/runtime/dist/bom/URL.js
function parseUrl(url = "") {
    const result = {
        href: "",
        origin: "",
        protocol: "",
        hostname: "",
        host: "",
        port: "",
        pathname: "",
        search: "",
        hash: ""
    };
    if (!url || !isString(url)) return result;
    url = url.trim();
    const PATTERN = /^(([^:/?#]+):)?\/\/(([^/?#]+):(.+)@)?([^/?#:]*)(:(\d+))?([^?#]*)(\?([^#]*))?(#(.*))?/;
    const matches = url.match(PATTERN);
    if (!matches) return result;
    result.protocol = matches[1] || "https:";
    result.hostname = matches[6] || "taro.com";
    result.port = matches[8] || "";
    result.pathname = matches[9] || "/";
    result.search = matches[10] || "";
    result.hash = matches[12] || "";
    result.href = url;
    result.origin = result.protocol + "//" + result.hostname;
    result.host = result.hostname + (result.port ? `:${result.port}` : "");
    return result;
}
function parseUrlBase(url, base) {
    const VALID_URL = /^(https?:)\/\//i;
    let fullUrl = "";
    let parsedBase = null;
    if (!isUndefined(base)) {
        base = String(base).trim();
        if (!VALID_URL.test(base)) throw new TypeError(`Failed to construct 'URL': Invalid base URL`);
        parsedBase = parseUrl(base);
    }
    url = String(url).trim();
    if (VALID_URL.test(url)) {
        fullUrl = url;
    } else if (parsedBase) {
        if (url) {
            if (url.startsWith("//")) {
                fullUrl = parsedBase.protocol + url;
            } else {
                fullUrl = parsedBase.origin + (url.startsWith("/") ? url : `/${url}`);
            }
        } else {
            fullUrl = parsedBase.href;
        }
    } else {
        throw new TypeError(`Failed to construct 'URL': Invalid URL`);
    }
    return parseUrl(fullUrl);
}
var _TaroURL_hash, _TaroURL_hostname, _TaroURL_pathname, _TaroURL_port, _TaroURL_protocol, _TaroURL_search, TaroURL, TaroURLProvider;
var init_URL = __esm({
    "node_modules/@tarojs/runtime/dist/bom/URL.js" () {
        init_tslib_es6();
        init_dist();
        init_env();
        init_URLSearchParams();
        TaroURL = class {
            static createObjectURL() {
                throw new Error("Oops, not support URL.createObjectURL() in miniprogram.");
            }
            static revokeObjectURL() {
                throw new Error("Oops, not support URL.revokeObjectURL() in miniprogram.");
            }
            /* public property */ get protocol() {
                return __classPrivateFieldGet(this, _TaroURL_protocol, "f");
            }
            set protocol(val) {
                isString(val) && __classPrivateFieldSet(this, _TaroURL_protocol, val.trim(), "f");
            }
            get host() {
                return this.hostname + (this.port ? ":" + this.port : "");
            }
            set host(val) {
                if (val && isString(val)) {
                    val = val.trim();
                    const { hostname, port } = parseUrl(`//${val}`);
                    this.hostname = hostname;
                    this.port = port;
                }
            }
            get hostname() {
                return __classPrivateFieldGet(this, _TaroURL_hostname, "f");
            }
            set hostname(val) {
                val && isString(val) && __classPrivateFieldSet(this, _TaroURL_hostname, val.trim(), "f");
            }
            get port() {
                return __classPrivateFieldGet(this, _TaroURL_port, "f");
            }
            set port(val) {
                isString(val) && __classPrivateFieldSet(this, _TaroURL_port, val.trim(), "f");
            }
            get pathname() {
                return __classPrivateFieldGet(this, _TaroURL_pathname, "f");
            }
            set pathname(val) {
                if (isString(val)) {
                    val = val.trim();
                    const HEAD_REG = /^(\/|\.\/|\.\.\/)/;
                    let temp = val;
                    while(HEAD_REG.test(temp)){
                        temp = temp.replace(HEAD_REG, "");
                    }
                    if (temp) __classPrivateFieldSet(this, _TaroURL_pathname, "/" + temp, "f");
                    else __classPrivateFieldSet(this, _TaroURL_pathname, "/", "f");
                }
            }
            get search() {
                const val = __classPrivateFieldGet(this, _TaroURL_search, "f").toString();
                return val.length === 0 || val.startsWith("?") ? val : `?${val}`;
            }
            set search(val) {
                if (isString(val)) {
                    val = val.trim();
                    __classPrivateFieldSet(this, _TaroURL_search, new URLSearchParams(val), "f");
                }
            }
            get hash() {
                return __classPrivateFieldGet(this, _TaroURL_hash, "f");
            }
            set hash(val) {
                if (isString(val)) {
                    val = val.trim();
                    if (val) __classPrivateFieldSet(this, _TaroURL_hash, val.startsWith("#") ? val : `#${val}`, "f");
                    else __classPrivateFieldSet(this, _TaroURL_hash, "", "f");
                }
            }
            get href() {
                return `${this.protocol}//${this.host}${this.pathname}${this.search}${this.hash}`;
            }
            set href(val) {
                if (val && isString(val)) {
                    val = val.trim();
                    const { protocol, hostname, port, hash, search, pathname } = parseUrl(val);
                    this.protocol = protocol;
                    this.hostname = hostname;
                    this.pathname = pathname;
                    this.port = port;
                    this.hash = hash;
                    this.search = search;
                }
            }
            get origin() {
                return `${this.protocol}//${this.host}`;
            }
            set origin(val) {
                if (val && isString(val)) {
                    val = val.trim();
                    const { protocol, hostname, port } = parseUrl(val);
                    this.protocol = protocol;
                    this.hostname = hostname;
                    this.port = port;
                }
            }
            get searchParams() {
                return __classPrivateFieldGet(this, _TaroURL_search, "f");
            }
            // public method
            toString() {
                return this.href;
            }
            toJSON() {
                return this.toString();
            }
            // convenient for deconstructor
            _toRaw() {
                return {
                    protocol: this.protocol,
                    port: this.port,
                    host: this.host,
                    hostname: this.hostname,
                    pathname: this.pathname,
                    hash: this.hash,
                    search: this.search,
                    origin: this.origin,
                    href: this.href
                };
            }
            constructor(url, base){
                _TaroURL_hash.set(this, "");
                _TaroURL_hostname.set(this, "");
                _TaroURL_pathname.set(this, "");
                _TaroURL_port.set(this, "");
                _TaroURL_protocol.set(this, "");
                _TaroURL_search.set(this, void 0);
                if (!isString(url)) url = String(url);
                const parseResult = parseUrlBase(url, base);
                const { hash, hostname, pathname, port, protocol, search } = parseResult;
                __classPrivateFieldSet(this, _TaroURL_hash, hash, "f");
                __classPrivateFieldSet(this, _TaroURL_hostname, hostname, "f");
                __classPrivateFieldSet(this, _TaroURL_pathname, pathname || "/", "f");
                __classPrivateFieldSet(this, _TaroURL_port, port, "f");
                __classPrivateFieldSet(this, _TaroURL_protocol, protocol, "f");
                __classPrivateFieldSet(this, _TaroURL_search, new URLSearchParams(search), "f");
            }
        };
        _TaroURL_hash = /* @__PURE__ */ new WeakMap(), _TaroURL_hostname = /* @__PURE__ */ new WeakMap(), _TaroURL_pathname = /* @__PURE__ */ new WeakMap(), _TaroURL_port = /* @__PURE__ */ new WeakMap(), _TaroURL_protocol = /* @__PURE__ */ new WeakMap(), _TaroURL_search = /* @__PURE__ */ new WeakMap();
        TaroURLProvider = false ? env.window.URL : TaroURL;
    }
});
// node_modules/@tarojs/runtime/dist/bom/location.js
function generateFullUrl(val = "") {
    const origin = INIT_URL;
    if (/^[/?#]/.test(val)) {
        return origin + val;
    }
    return val;
}
var _TaroLocation_instances, _TaroLocation_url, _TaroLocation_noCheckUrl, _TaroLocation_window, _TaroLocation_reset, _TaroLocation_getPreValue, _TaroLocation_rollBack, _TaroLocation_recordHistory, _TaroLocation_checkUrlChange, INIT_URL, cache2, TaroLocation, Location;
var init_location = __esm({
    "node_modules/@tarojs/runtime/dist/bom/location.js" () {
        init_tslib_es6();
        init_dist();
        init_constants();
        init_current();
        init_emitter();
        init_env();
        init_cache();
        init_URL();
        INIT_URL = "https://taro.com";
        cache2 = new RuntimeCache("location");
        TaroLocation = class extends Events {
            /* public property */ get protocol() {
                return __classPrivateFieldGet(this, _TaroLocation_url, "f").protocol;
            }
            set protocol(val) {
                const REG = /^(http|https):$/i;
                if (!val || !isString(val) || !REG.test(val.trim())) return;
                val = val.trim();
                const preValue = __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_getPreValue).call(this);
                __classPrivateFieldGet(this, _TaroLocation_url, "f").protocol = val;
                if (__classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_checkUrlChange).call(this, preValue)) __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_recordHistory).call(this);
            }
            get host() {
                return __classPrivateFieldGet(this, _TaroLocation_url, "f").host;
            }
            set host(val) {
                if (!val || !isString(val)) return;
                val = val.trim();
                const preValue = __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_getPreValue).call(this);
                __classPrivateFieldGet(this, _TaroLocation_url, "f").host = val;
                if (__classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_checkUrlChange).call(this, preValue)) __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_recordHistory).call(this);
            }
            get hostname() {
                return __classPrivateFieldGet(this, _TaroLocation_url, "f").hostname;
            }
            set hostname(val) {
                if (!val || !isString(val)) return;
                val = val.trim();
                const preValue = __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_getPreValue).call(this);
                __classPrivateFieldGet(this, _TaroLocation_url, "f").hostname = val;
                if (__classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_checkUrlChange).call(this, preValue)) __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_recordHistory).call(this);
            }
            get port() {
                return __classPrivateFieldGet(this, _TaroLocation_url, "f").port;
            }
            set port(val) {
                const xVal = Number(val = val.trim());
                if (!isNumber(xVal) || xVal <= 0) return;
                const preValue = __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_getPreValue).call(this);
                __classPrivateFieldGet(this, _TaroLocation_url, "f").port = val;
                if (__classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_checkUrlChange).call(this, preValue)) __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_recordHistory).call(this);
            }
            get pathname() {
                return __classPrivateFieldGet(this, _TaroLocation_url, "f").pathname;
            }
            set pathname(val) {
                if (!val || !isString(val)) return;
                val = val.trim();
                const preValue = __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_getPreValue).call(this);
                __classPrivateFieldGet(this, _TaroLocation_url, "f").pathname = val;
                if (__classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_checkUrlChange).call(this, preValue)) __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_recordHistory).call(this);
            }
            get search() {
                return __classPrivateFieldGet(this, _TaroLocation_url, "f").search;
            }
            set search(val) {
                if (!val || !isString(val)) return;
                val = val.trim();
                val = val.startsWith("?") ? val : `?${val}`;
                const preValue = __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_getPreValue).call(this);
                __classPrivateFieldGet(this, _TaroLocation_url, "f").search = val;
                if (__classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_checkUrlChange).call(this, preValue)) __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_recordHistory).call(this);
            }
            get hash() {
                return __classPrivateFieldGet(this, _TaroLocation_url, "f").hash;
            }
            // 小程序的navigateTo存在截断hash字符串的问题
            set hash(val) {
                if (!val || !isString(val)) return;
                val = val.trim();
                val = val.startsWith("#") ? val : `#${val}`;
                const preValue = __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_getPreValue).call(this);
                __classPrivateFieldGet(this, _TaroLocation_url, "f").hash = val;
                if (__classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_checkUrlChange).call(this, preValue)) __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_recordHistory).call(this);
            }
            get href() {
                return __classPrivateFieldGet(this, _TaroLocation_url, "f").href;
            }
            set href(val) {
                const REG = /^(http:|https:)?\/\/.+/;
                if (!val || !isString(val) || !REG.test(val = val.trim())) return;
                const preValue = __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_getPreValue).call(this);
                __classPrivateFieldGet(this, _TaroLocation_url, "f").href = val;
                if (__classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_checkUrlChange).call(this, preValue)) __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_recordHistory).call(this);
            }
            get origin() {
                return __classPrivateFieldGet(this, _TaroLocation_url, "f").origin;
            }
            set origin(val) {
                const REG = /^(http:|https:)?\/\/.+/;
                if (!val || !isString(val) || !REG.test(val = val.trim())) return;
                const preValue = __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_getPreValue).call(this);
                __classPrivateFieldGet(this, _TaroLocation_url, "f").origin = val;
                if (__classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_checkUrlChange).call(this, preValue)) __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_recordHistory).call(this);
            }
            /* public method */ assign() {
                warn(true, "\u5C0F\u7A0B\u5E8F\u73AF\u5883\u4E2D\u8C03\u7528location.assign()\u65E0\u6548.");
            }
            reload() {
                warn(true, "\u5C0F\u7A0B\u5E8F\u73AF\u5883\u4E2D\u8C03\u7528location.reload()\u65E0\u6548.");
            }
            replace(url) {
                this.trigger("__set_href_without_history__", url);
            }
            toString() {
                return this.href;
            }
            // For debug
            get cache() {
                return cache2;
            }
            constructor(options2){
                super();
                _TaroLocation_instances.add(this);
                _TaroLocation_url.set(this, new TaroURLProvider(INIT_URL));
                _TaroLocation_noCheckUrl.set(this, false);
                _TaroLocation_window.set(this, void 0);
                __classPrivateFieldSet(this, _TaroLocation_window, options2.window, "f");
                __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_reset).call(this);
                this.on("__set_href_without_history__", (href)=>{
                    __classPrivateFieldSet(this, _TaroLocation_noCheckUrl, true, "f");
                    const lastHash = __classPrivateFieldGet(this, _TaroLocation_url, "f").hash;
                    __classPrivateFieldGet(this, _TaroLocation_url, "f").href = generateFullUrl(href);
                    if (lastHash !== __classPrivateFieldGet(this, _TaroLocation_url, "f").hash) {
                        __classPrivateFieldGet(this, _TaroLocation_window, "f").trigger("hashchange");
                    }
                    __classPrivateFieldSet(this, _TaroLocation_noCheckUrl, false, "f");
                }, null);
                this.on(CONTEXT_ACTIONS.INIT, ()=>{
                    __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_reset).call(this);
                }, null);
                this.on(CONTEXT_ACTIONS.RESTORE, (pageId2)=>{
                    cache2.set(pageId2, {
                        lastHref: this.href
                    });
                }, null);
                this.on(CONTEXT_ACTIONS.RECOVER, (pageId2)=>{
                    if (cache2.has(pageId2)) {
                        const ctx = cache2.get(pageId2);
                        __classPrivateFieldSet(this, _TaroLocation_noCheckUrl, true, "f");
                        __classPrivateFieldGet(this, _TaroLocation_url, "f").href = ctx.lastHref;
                        __classPrivateFieldSet(this, _TaroLocation_noCheckUrl, false, "f");
                    }
                }, null);
                this.on(CONTEXT_ACTIONS.DESTORY, (pageId2)=>{
                    cache2.delete(pageId2);
                }, null);
            }
        };
        _TaroLocation_url = /* @__PURE__ */ new WeakMap(), _TaroLocation_noCheckUrl = /* @__PURE__ */ new WeakMap(), _TaroLocation_window = /* @__PURE__ */ new WeakMap(), _TaroLocation_instances = /* @__PURE__ */ new WeakSet(), _TaroLocation_reset = function _TaroLocation_reset2() {
            const Current2 = getCurrentInstance();
            const router = Current2.router;
            if (router) {
                const { path, params } = router;
                const searchArr = Object.keys(params).map((key)=>{
                    return `${key}=${params[key]}`;
                });
                const searchStr = searchArr.length > 0 ? "?" + searchArr.join("&") : "";
                const url = `${INIT_URL}${path.startsWith("/") ? path : "/" + path}${searchStr}`;
                __classPrivateFieldSet(this, _TaroLocation_url, new TaroURLProvider(url), "f");
                this.trigger("__reset_history__", this.href);
            }
        }, _TaroLocation_getPreValue = function _TaroLocation_getPreValue2() {
            return __classPrivateFieldGet(this, _TaroLocation_url, "f")._toRaw();
        }, _TaroLocation_rollBack = function _TaroLocation_rollBack2(href) {
            __classPrivateFieldGet(this, _TaroLocation_url, "f").href = href;
        }, _TaroLocation_recordHistory = function _TaroLocation_recordHistory2() {
            this.trigger("__record_history__", this.href);
        }, _TaroLocation_checkUrlChange = function _TaroLocation_checkUrlChange2(preValue) {
            if (__classPrivateFieldGet(this, _TaroLocation_noCheckUrl, "f")) {
                return false;
            }
            const { protocol, hostname, port, pathname, search, hash } = __classPrivateFieldGet(this, _TaroLocation_url, "f")._toRaw();
            if (protocol !== preValue.protocol || hostname !== preValue.hostname || port !== preValue.port) {
                __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_rollBack).call(this, preValue.href);
                return false;
            }
            if (pathname !== preValue.pathname) {
                return true;
            }
            if (search !== preValue.search) {
                return true;
            }
            if (hash !== preValue.hash) {
                __classPrivateFieldGet(this, _TaroLocation_window, "f").trigger("hashchange");
                return true;
            }
            __classPrivateFieldGet(this, _TaroLocation_instances, "m", _TaroLocation_rollBack).call(this, preValue.href);
            return false;
        };
        Location = false ? env.window.Location : TaroLocation;
    }
});
// node_modules/@tarojs/runtime/dist/bom/navigator.js
var machine, arch, engine, msg, nav;
var init_navigator = __esm({
    "node_modules/@tarojs/runtime/dist/bom/navigator.js" () {
        init_env();
        machine = "Macintosh";
        arch = "Intel Mac OS X 10_14_5";
        engine = "AppleWebKit/534.36 (KHTML, like Gecko) NodeJS/v4.1.0 Chrome/76.0.3809.132 Safari/534.36";
        msg = "(" + machine + "; " + arch + ") " + engine;
        nav = false ? env.window.navigator : {
            appCodeName: "Mozilla",
            appName: "Netscape",
            appVersion: "5.0 " + msg,
            cookieEnabled: true,
            mimeTypes: [],
            onLine: true,
            platform: "MacIntel",
            plugins: [],
            product: "Taro",
            productSub: "20030107",
            userAgent: "Mozilla/5.0 " + msg,
            vendor: "Joyent",
            vendorSub: ""
        };
    }
});
// node_modules/@tarojs/runtime/dist/bom/raf.js
var now, lastTime, _raf, _caf;
var init_raf = __esm({
    "node_modules/@tarojs/runtime/dist/bom/raf.js" () {
        (function() {
            let loadTime;
            if (typeof performance !== "undefined" && performance !== null && performance.now) {
                now = ()=>performance.now();
            } else if (Date.now) {
                loadTime = Date.now();
                now = ()=>Date.now() - loadTime;
            } else {
                loadTime = /* @__PURE__ */ new Date().getTime();
                now = ()=>/* @__PURE__ */ new Date().getTime() - loadTime;
            }
        })();
        lastTime = 0;
        _raf = false ? requestAnimationFrame : function(callback) {
            const _now = now();
            const nextTime = Math.max(lastTime + 16, _now);
            return setTimeout(function() {
                callback(lastTime = nextTime);
            }, nextTime - _now);
        };
        _caf = false ? cancelAnimationFrame : function(seed) {
            clearTimeout(seed);
        };
    }
});
// node_modules/@tarojs/runtime/dist/bom/window.js
var TaroWindow, taroWindowProvider, taroLocationProvider, taroHistoryProvider;
var init_window = __esm({
    "node_modules/@tarojs/runtime/dist/bom/window.js" () {
        init_dist();
        init_constants();
        init_emitter();
        init_env();
        init_getComputedStyle();
        init_history();
        init_location();
        init_navigator();
        init_raf();
        TaroWindow = class extends Events {
            initEvent() {
                const _location = this.location;
                const _history = this.history;
                this.on(CONTEXT_ACTIONS.INIT, (pageId2)=>{
                    _location.trigger(CONTEXT_ACTIONS.INIT, pageId2);
                }, null);
                this.on(CONTEXT_ACTIONS.RECOVER, (pageId2)=>{
                    _location.trigger(CONTEXT_ACTIONS.RECOVER, pageId2);
                    _history.trigger(CONTEXT_ACTIONS.RECOVER, pageId2);
                }, null);
                this.on(CONTEXT_ACTIONS.RESTORE, (pageId2)=>{
                    _location.trigger(CONTEXT_ACTIONS.RESTORE, pageId2);
                    _history.trigger(CONTEXT_ACTIONS.RESTORE, pageId2);
                }, null);
                this.on(CONTEXT_ACTIONS.DESTORY, (pageId2)=>{
                    _location.trigger(CONTEXT_ACTIONS.DESTORY, pageId2);
                    _history.trigger(CONTEXT_ACTIONS.DESTORY, pageId2);
                }, null);
            }
            get document() {
                return env.document;
            }
            addEventListener(event, callback) {
                if (!isString(event)) return;
                this.on(event, callback, null);
            }
            removeEventListener(event, callback) {
                if (!isString(event)) return;
                this.off(event, callback, null);
            }
            setTimeout(...args) {
                return setTimeout(...args);
            }
            clearTimeout(...args) {
                return clearTimeout(...args);
            }
            constructor(){
                super();
                this.navigator = nav;
                this.requestAnimationFrame = _raf;
                this.cancelAnimationFrame = _caf;
                this.getComputedStyle = taroGetComputedStyleProvider;
                const globalProperties = [
                    ...Object.getOwnPropertyNames(global || {}),
                    ...Object.getOwnPropertySymbols(global || {})
                ];
                globalProperties.forEach((property)=>{
                    if (property === "atob" || property === "document") return;
                    if (!Object.prototype.hasOwnProperty.call(this, property)) {
                        try {
                            this[property] = global[property];
                        } catch (e) {
                            if (true) {
                                console.warn(`[Taro warn] window.${String(property)} \u5728\u8D4B\u503C\u5230 window \u65F6\u62A5\u9519`);
                            }
                        }
                    }
                });
                this.Date || (this.Date = Date);
                this.location = new Location({
                    window: this
                });
                this.history = new History(this.location, {
                    window: this
                });
                this.initEvent();
            }
        };
        taroWindowProvider = false ? env.window : env.window = new TaroWindow();
        taroLocationProvider = taroWindowProvider.location;
        taroHistoryProvider = taroWindowProvider.history;
    }
});
// node_modules/@tarojs/runtime/dist/utils/index.js
function isElement(node) {
    return node.nodeType === 1;
}
function isText(node) {
    return node.nodeType === 3;
}
function isComment(node) {
    return node.nodeName === COMMENT;
}
function isHasExtractProp(el) {
    const res = Object.keys(el.props).find((prop)=>{
        return !(/^(class|style|id)$/.test(prop) || prop.startsWith("data-"));
    });
    return Boolean(res);
}
function isParentBinded(node, type) {
    var _a2;
    while(node = (node === null || node === void 0 ? void 0 : node.parentElement) || null){
        if (!node || node.nodeName === ROOT_STR || node.nodeName === "root-portal") {
            return false;
        } else if ((_a2 = node.__handlers[type]) === null || _a2 === void 0 ? void 0 : _a2.length) {
            return true;
        }
    }
    return false;
}
function shortcutAttr(key) {
    switch(key){
        case STYLE:
            return "st";
        case ID:
            return UID;
        case CLASS:
            return "cl";
        default:
            return key;
    }
}
function extend(ctor, methodName, options2) {
    if (isFunction(options2)) {
        options2 = {
            value: options2
        };
    }
    Object.defineProperty(ctor.prototype, methodName, Object.assign({
        configurable: true,
        enumerable: true
    }, options2));
}
function getComponentsAlias2() {
    if (!componentsAlias) {
        componentsAlias = getComponentsAlias(internalComponents);
    }
    return componentsAlias;
}
function convertNumber2PX(value) {
    return value + "px";
}
var incrementId, customWrapperCache, componentsAlias;
var init_utils = __esm({
    "node_modules/@tarojs/runtime/dist/utils/index.js" () {
        init_dist();
        init_constants();
        init_window();
        incrementId = ()=>{
            const chatCodes = [];
            for(let i = 65; i <= 90; i++){
                chatCodes.push(i);
            }
            for(let i = 97; i <= 122; i++){
                chatCodes.push(i);
            }
            const chatCodesLen = chatCodes.length - 1;
            const list = [
                0,
                0
            ];
            return ()=>{
                const target = list.map((item)=>chatCodes[item]);
                const res = String.fromCharCode(...target);
                let tailIdx = list.length - 1;
                list[tailIdx]++;
                while(list[tailIdx] > chatCodesLen){
                    list[tailIdx] = 0;
                    tailIdx = tailIdx - 1;
                    if (tailIdx < 0) {
                        list.push(0);
                        break;
                    }
                    list[tailIdx]++;
                }
                return res;
            };
        };
        customWrapperCache = /* @__PURE__ */ new Map();
    }
});
// node_modules/@tarojs/runtime/dist/dom/event-source.js
var EventSource, eventSource;
var init_event_source = __esm({
    "node_modules/@tarojs/runtime/dist/dom/event-source.js" () {
        EventSource = class extends Map {
            removeNode(child) {
                const { sid, uid } = child;
                this.delete(sid);
                if (uid !== sid && uid) this.delete(uid);
            }
            removeNodeTree(child) {
                this.removeNode(child);
                const { childNodes } = child;
                childNodes.forEach((node)=>this.removeNodeTree(node));
            }
        };
        eventSource = new EventSource();
    }
});
// node_modules/@tarojs/runtime/dist/hydrate.js
function hydrate(node) {
    var _a2;
    componentsAlias2 || (componentsAlias2 = getComponentsAlias2());
    SPECIAL_NODES || (SPECIAL_NODES = hooks.call("getSpecialNodes"));
    const nodeName = node.nodeName;
    let compileModeName = null;
    if (isText(node)) {
        return {
            sid: node.sid,
            ["v"]: node.nodeValue,
            ["nn"]: ((_a2 = componentsAlias2[nodeName]) === null || _a2 === void 0 ? void 0 : _a2._num) || "8"
        };
    }
    const data = {
        ["nn"]: nodeName,
        sid: node.sid
    };
    if (node.uid !== node.sid) {
        data.uid = node.uid;
    }
    if (SPECIAL_NODES.indexOf(nodeName) > -1) {
        if (!node.isAnyEventBinded()) {
            data["nn"] = `static-${nodeName}`;
            if (nodeName === VIEW && !isHasExtractProp(node)) {
                data["nn"] = PURE_VIEW;
            }
        }
        if (nodeName === VIEW && node.isOnlyClickBinded() && !isHasExtractProp(node)) {
            data["nn"] = CLICK_VIEW;
        }
    }
    const { props } = node;
    for(const prop in props){
        const propInCamelCase = toCamelCase(prop);
        if (!prop.startsWith("data-") && // 在 node.dataset 的数据
        prop !== CLASS && prop !== STYLE && prop !== ID && propInCamelCase !== CATCHMOVE && propInCamelCase !== COMPILE_MODE) {
            data[propInCamelCase] = props[prop];
        }
        if (nodeName === VIEW && propInCamelCase === CATCHMOVE && props[prop] !== false) {
            data["nn"] = CATCH_VIEW;
        }
        if (propInCamelCase === COMPILE_MODE) {
            compileModeName = props[prop];
        }
    }
    data["cn"] = node.childNodes.filter((node2)=>!isComment(node2)).map(hydrate);
    if (node.className !== "") {
        data["cl"] = node.className;
    }
    const cssText = node.cssText;
    if (cssText !== "" && nodeName !== "swiper-item") {
        data["st"] = cssText;
    }
    hooks.call("modifyHydrateData", data, node);
    const nn = data["nn"];
    const componentAlias = componentsAlias2[nn];
    if (componentAlias) {
        data["nn"] = componentAlias._num;
        for(const prop in data){
            if (prop in componentAlias) {
                data[componentAlias[prop]] = data[prop];
                delete data[prop];
            }
        }
    }
    if (compileModeName !== null) {
        data["nn"] = compileModeName;
    }
    const resData = hooks.call("transferHydrateData", data, node, componentAlias);
    return resData || data;
}
var SPECIAL_NODES, componentsAlias2;
var init_hydrate = __esm({
    "node_modules/@tarojs/runtime/dist/hydrate.js" () {
        init_dist();
        init_constants();
        init_utils();
    }
});
// node_modules/@tarojs/runtime/dist/dom/event-target.js
var TaroEventTarget;
var init_event_target = __esm({
    "node_modules/@tarojs/runtime/dist/dom/event-target.js" () {
        init_dist();
        TaroEventTarget = class {
            addEventListener(type, handler, options2) {
                type = type.toLowerCase();
                hooks.call("onAddEvent", type, handler, options2, this);
                if (type === "regionchange") {
                    this.addEventListener("begin", handler, options2);
                    this.addEventListener("end", handler, options2);
                    return;
                }
                let isCapture = Boolean(options2);
                let isOnce = false;
                if (isObject(options2)) {
                    isCapture = Boolean(options2.capture);
                    isOnce = Boolean(options2.once);
                }
                if (isOnce) {
                    const wrapper = function() {
                        handler.apply(this, arguments);
                        this.removeEventListener(type, wrapper);
                    };
                    this.addEventListener(type, wrapper, Object.assign(Object.assign({}, options2), {
                        once: false
                    }));
                    return;
                }
                warn(isCapture, "Taro \u6682\u672A\u5B9E\u73B0 event \u7684 capture \u7279\u6027\u3002");
                const oldHandler = handler;
                handler = function() {
                    return oldHandler.apply(this, arguments);
                };
                handler.oldHandler = oldHandler;
                const handlers = this.__handlers[type];
                if (isArray(handlers)) {
                    handlers.push(handler);
                } else {
                    this.__handlers[type] = [
                        handler
                    ];
                }
            }
            removeEventListener(type, handler) {
                type = type.toLowerCase();
                if (type === "regionchange") {
                    this.removeEventListener("begin", handler);
                    this.removeEventListener("end", handler);
                    return;
                }
                if (!handler) {
                    return;
                }
                const handlers = this.__handlers[type];
                if (!isArray(handlers)) {
                    return;
                }
                const index = handlers.findIndex((item)=>{
                    if (item === handler || item.oldHandler === handler) return true;
                });
                warn(index === -1, `\u4E8B\u4EF6: '${type}' \u6CA1\u6709\u6CE8\u518C\u5728 DOM \u4E2D\uFF0C\u56E0\u6B64\u4E0D\u4F1A\u88AB\u79FB\u9664\u3002`);
                handlers.splice(index, 1);
            }
            isAnyEventBinded() {
                const handlers = this.__handlers;
                const isAnyEventBinded = Object.keys(handlers).find((key)=>handlers[key].length);
                return Boolean(isAnyEventBinded);
            }
            isOnlyClickBinded() {
                const handlers = this.__handlers;
                const isOnlyClickBinded = handlers.tap && Object.keys(handlers).length === 1;
                return Boolean(isOnlyClickBinded);
            }
            constructor(){
                this.__handlers = {};
            }
        };
    }
});
// node_modules/@tarojs/runtime/dist/dom/node.js
var CHILDNODES, nodeId, TaroNode;
var init_node = __esm({
    "node_modules/@tarojs/runtime/dist/dom/node.js" () {
        init_dist();
        init_constants();
        init_mutation_observer();
        init_env();
        init_hydrate();
        init_utils();
        init_event_source();
        init_event_target();
        CHILDNODES = "cn";
        nodeId = incrementId();
        TaroNode = class _TaroNode extends TaroEventTarget {
            updateChildNodes(isClean) {
                const cleanChildNodes = ()=>[];
                const rerenderChildNodes = ()=>{
                    const childNodes = this.childNodes.filter((node)=>!isComment(node));
                    return childNodes.map(hydrate);
                };
                this.enqueueUpdate({
                    path: `${this._path}.${CHILDNODES}`,
                    value: isClean ? cleanChildNodes : rerenderChildNodes
                });
            }
            updateSingleChild(index) {
                this.childNodes.forEach((child, childIndex)=>{
                    if (isComment(child)) return;
                    if (index && childIndex < index) return;
                    this.enqueueUpdate({
                        path: child._path,
                        value: this.hydrate(child)
                    });
                });
            }
            get _root() {
                var _a2;
                return ((_a2 = this.parentNode) === null || _a2 === void 0 ? void 0 : _a2._root) || null;
            }
            findIndex(refChild) {
                const index = this.childNodes.indexOf(refChild);
                ensure(index !== -1, "The node to be replaced is not a child of this node.");
                return index;
            }
            get _path() {
                const parentNode = this.parentNode;
                if (parentNode) {
                    const list = parentNode.childNodes.filter((node)=>!isComment(node));
                    const indexOfNode = list.indexOf(this);
                    const index = hooks.call("getPathIndex", indexOfNode);
                    return `${parentNode._path}.${CHILDNODES}.${index}`;
                }
                return "";
            }
            get nextSibling() {
                const parentNode = this.parentNode;
                return (parentNode === null || parentNode === void 0 ? void 0 : parentNode.childNodes[parentNode.findIndex(this) + 1]) || null;
            }
            get previousSibling() {
                const parentNode = this.parentNode;
                return (parentNode === null || parentNode === void 0 ? void 0 : parentNode.childNodes[parentNode.findIndex(this) - 1]) || null;
            }
            get parentElement() {
                const parentNode = this.parentNode;
                if ((parentNode === null || parentNode === void 0 ? void 0 : parentNode.nodeType) === 1) {
                    return parentNode;
                }
                return null;
            }
            get firstChild() {
                return this.childNodes[0] || null;
            }
            get lastChild() {
                const childNodes = this.childNodes;
                return childNodes[childNodes.length - 1] || null;
            }
            /**
       * @textContent 目前只能置空子元素
       * @TODO 等待完整 innerHTML 实现
       */ // eslint-disable-next-line accessor-pairs
            set textContent(text) {
                const removedNodes = this.childNodes.slice();
                const addedNodes = [];
                while(this.firstChild){
                    this.removeChild(this.firstChild, {
                        doUpdate: false
                    });
                }
                if (text === "") {
                    this.updateChildNodes(true);
                } else {
                    const newText = env.document.createTextNode(text);
                    addedNodes.push(newText);
                    this.appendChild(newText);
                    this.updateChildNodes();
                }
                MutationObserver2.record({
                    type: "childList",
                    target: this,
                    removedNodes,
                    addedNodes
                });
            }
            /**
       * @doc https://developer.mozilla.org/zh-CN/docs/Web/API/Node/insertBefore
       * @scenario
       * [A,B,C]
       *   1. insert D before C, D has no parent
       *   2. insert D before C, D has the same parent of C
       *   3. insert D before C, D has the different parent of C
       */ insertBefore(newChild, refChild, isReplace) {
                if (newChild.nodeName === DOCUMENT_FRAGMENT) {
                    newChild.childNodes.reduceRight((previousValue, currentValue)=>{
                        this.insertBefore(currentValue, previousValue);
                        return currentValue;
                    }, refChild);
                    return newChild;
                }
                newChild.remove({
                    cleanRef: false
                });
                let index = 0;
                newChild.parentNode = this;
                if (refChild) {
                    index = this.findIndex(refChild);
                    this.childNodes.splice(index, 0, newChild);
                } else {
                    this.childNodes.push(newChild);
                }
                const childNodesLength = this.childNodes.length;
                if (this._root) {
                    if (!refChild) {
                        const isOnlyChild = childNodesLength === 1;
                        if (isOnlyChild) {
                            this.updateChildNodes();
                        } else {
                            this.enqueueUpdate({
                                path: newChild._path,
                                value: this.hydrate(newChild)
                            });
                        }
                    } else if (isReplace) {
                        this.enqueueUpdate({
                            path: newChild._path,
                            value: this.hydrate(newChild)
                        });
                    } else {
                        const mark = childNodesLength * 2 / 3;
                        if (mark > index) {
                            this.updateChildNodes();
                        } else {
                            this.updateSingleChild(index);
                        }
                    }
                }
                MutationObserver2.record({
                    type: "childList",
                    target: this,
                    addedNodes: [
                        newChild
                    ],
                    removedNodes: isReplace ? [
                        refChild
                    ] : [],
                    nextSibling: isReplace ? refChild.nextSibling : refChild || null,
                    /** insertBefore & appendChild */ previousSibling: newChild.previousSibling
                });
                return newChild;
            }
            /**
       * @doc https://developer.mozilla.org/zh-CN/docs/Web/API/Node/appendChild
       * @scenario
       * [A,B,C]
       *   1. append C, C has no parent
       *   2. append C, C has the same parent of B
       *   3. append C, C has the different parent of B
       */ appendChild(newChild) {
                return this.insertBefore(newChild);
            }
            /**
       * @doc https://developer.mozilla.org/zh-CN/docs/Web/API/Node/replaceChild
       * @scenario
       * [A,B,C]
       *   1. replace B with C, C has no parent
       *   2. replace B with C, C has no parent, C has the same parent of B
       *   3. replace B with C, C has no parent, C has the different parent of B
       */ replaceChild(newChild, oldChild) {
                if (oldChild.parentNode !== this) return;
                this.insertBefore(newChild, oldChild, true);
                oldChild.remove({
                    doUpdate: false
                });
                return oldChild;
            }
            /**
       * @doc https://developer.mozilla.org/zh-CN/docs/Web/API/Node/removeChild
       * @scenario
       * [A,B,C]
       *   1. remove A or B
       *   2. remove C
       */ removeChild(child, options2 = {}) {
                const { cleanRef, doUpdate } = options2;
                if (cleanRef !== false && doUpdate !== false) {
                    MutationObserver2.record({
                        type: "childList",
                        target: this,
                        removedNodes: [
                            child
                        ],
                        nextSibling: child.nextSibling,
                        previousSibling: child.previousSibling
                    });
                }
                const index = this.findIndex(child);
                this.childNodes.splice(index, 1);
                child.parentNode = null;
                if (cleanRef !== false) {
                    eventSource.removeNodeTree(child);
                }
                if (this._root && doUpdate !== false) {
                    this.updateChildNodes();
                }
                return child;
            }
            remove(options2) {
                var _a2;
                (_a2 = this.parentNode) === null || _a2 === void 0 ? void 0 : _a2.removeChild(this, options2);
            }
            hasChildNodes() {
                return this.childNodes.length > 0;
            }
            enqueueUpdate(payload) {
                var _a2;
                (_a2 = this._root) === null || _a2 === void 0 ? void 0 : _a2.enqueueUpdate(payload);
            }
            get ownerDocument() {
                return env.document;
            }
            static extend(methodName, options2) {
                extend(_TaroNode, methodName, options2);
            }
            constructor(){
                super();
                this.parentNode = null;
                this.childNodes = [];
                this.hydrate = (node)=>()=>hydrate(node);
                this.uid = "_" + nodeId();
                this.sid = this.uid;
                eventSource.set(this.sid, this);
            }
        };
    }
});
// node_modules/@tarojs/runtime/dist/dom/style_properties.js
function combine(prefix, list, excludeSelf) {
    !excludeSelf && styleProperties.push(prefix);
    list.forEach((item)=>{
        styleProperties.push(prefix + item);
        if (prefix === WEBKIT) {
            styleProperties.push("Webkit" + item);
        }
    });
}
var WEBKIT, styleProperties, color, style, width, image, size, color_style_width, fitlength_fitwidth_image, fitlength_fitwidth_image_radius, color_style_width_fitlength_fitwidth_image, endRadius_startRadius, bottom_left_right_top, end_start, content_items_self, blockSize_height_inlineSize_width, after_before;
var init_style_properties = __esm({
    "node_modules/@tarojs/runtime/dist/dom/style_properties.js" () {
        WEBKIT = "webkit";
        styleProperties = [
            "all",
            "appearance",
            "backdropFilter",
            "blockOverflow",
            "blockSize",
            "bottom",
            "clear",
            "contain",
            "content",
            "continue",
            "cursor",
            "direction",
            "display",
            "filter",
            "float",
            "gap",
            "height",
            "inset",
            "isolation",
            "left",
            "letterSpacing",
            "lightingColor",
            "markerSide",
            "mixBlendMode",
            "opacity",
            "order",
            "position",
            "quotes",
            "resize",
            "right",
            "rowGap",
            "tabSize",
            "tableLayout",
            "top",
            "userSelect",
            "verticalAlign",
            "visibility",
            "voiceFamily",
            "volume",
            "whiteSpace",
            "widows",
            "width",
            "zIndex",
            "pointerEvents",
            "aspectRatio"
        ];
        color = "Color";
        style = "Style";
        width = "Width";
        image = "Image";
        size = "Size";
        color_style_width = [
            color,
            style,
            width
        ];
        fitlength_fitwidth_image = [
            "FitLength",
            "FitWidth",
            image
        ];
        fitlength_fitwidth_image_radius = [
            ...fitlength_fitwidth_image,
            "Radius"
        ];
        color_style_width_fitlength_fitwidth_image = [
            ...color_style_width,
            ...fitlength_fitwidth_image
        ];
        endRadius_startRadius = [
            "EndRadius",
            "StartRadius"
        ];
        bottom_left_right_top = [
            "Bottom",
            "Left",
            "Right",
            "Top"
        ];
        end_start = [
            "End",
            "Start"
        ];
        content_items_self = [
            "Content",
            "Items",
            "Self"
        ];
        blockSize_height_inlineSize_width = [
            "BlockSize",
            "Height",
            "InlineSize",
            width
        ];
        after_before = [
            "After",
            "Before"
        ];
        combine("borderBlock", color_style_width);
        combine("borderBlockEnd", color_style_width);
        combine("borderBlockStart", color_style_width);
        combine("outline", [
            ...color_style_width,
            "Offset"
        ]);
        combine("border", [
            ...color_style_width,
            "Boundary",
            "Break",
            "Collapse",
            "Radius",
            "Spacing"
        ]);
        combine("borderFit", [
            "Length",
            width
        ]);
        combine("borderInline", color_style_width);
        combine("borderInlineEnd", color_style_width);
        combine("borderInlineStart", color_style_width);
        combine("borderLeft", color_style_width_fitlength_fitwidth_image);
        combine("borderRight", color_style_width_fitlength_fitwidth_image);
        combine("borderTop", color_style_width_fitlength_fitwidth_image);
        combine("borderBottom", color_style_width_fitlength_fitwidth_image);
        combine("textDecoration", [
            color,
            style,
            "Line"
        ]);
        combine("textEmphasis", [
            color,
            style,
            "Position"
        ]);
        combine("scrollMargin", bottom_left_right_top);
        combine("scrollPadding", bottom_left_right_top);
        combine("padding", bottom_left_right_top);
        combine("margin", [
            ...bottom_left_right_top,
            "Trim"
        ]);
        combine("scrollMarginBlock", end_start);
        combine("scrollMarginInline", end_start);
        combine("scrollPaddingBlock", end_start);
        combine("scrollPaddingInline", end_start);
        combine("gridColumn", end_start);
        combine("gridRow", end_start);
        combine("insetBlock", end_start);
        combine("insetInline", end_start);
        combine("marginBlock", end_start);
        combine("marginInline", end_start);
        combine("paddingBlock", end_start);
        combine("paddingInline", end_start);
        combine("pause", after_before);
        combine("cue", after_before);
        combine("mask", [
            "Clip",
            "Composite",
            image,
            "Mode",
            "Origin",
            "Position",
            "Repeat",
            size,
            "Type"
        ]);
        combine("borderImage", [
            "Outset",
            "Repeat",
            "Slice",
            "Source",
            "Transform",
            width
        ]);
        combine("maskBorder", [
            "Mode",
            "Outset",
            "Repeat",
            "Slice",
            "Source",
            width
        ]);
        combine("font", [
            "Family",
            "FeatureSettings",
            "Kerning",
            "LanguageOverride",
            "MaxSize",
            "MinSize",
            "OpticalSizing",
            "Palette",
            size,
            "SizeAdjust",
            "Stretch",
            style,
            "Weight",
            "VariationSettings"
        ]);
        combine("transform", [
            "Box",
            "Origin",
            style
        ]);
        combine("background", [
            color,
            image,
            "Attachment",
            "BlendMode",
            "Clip",
            "Origin",
            "Position",
            "Repeat",
            size
        ]);
        combine("listStyle", [
            image,
            "Position",
            "Type"
        ]);
        combine("scrollSnap", [
            "Align",
            "Stop",
            "Type"
        ]);
        combine("grid", [
            "Area",
            "AutoColumns",
            "AutoFlow",
            "AutoRows"
        ]);
        combine("gridTemplate", [
            "Areas",
            "Columns",
            "Rows"
        ]);
        combine("overflow", [
            "Block",
            "Inline",
            "Wrap",
            "X",
            "Y"
        ]);
        combine("transition", [
            "Delay",
            "Duration",
            "Property",
            "TimingFunction"
        ]);
        combine("color", [
            "Adjust",
            "InterpolationFilters",
            "Scheme"
        ]);
        combine("textAlign", [
            "All",
            "Last"
        ]);
        combine("page", [
            "BreakAfter",
            "BreakBefore",
            "BreakInside"
        ]);
        combine("animation", [
            "Delay",
            "Direction",
            "Duration",
            "FillMode",
            "IterationCount",
            "Name",
            "PlayState",
            "TimingFunction"
        ]);
        combine("flex", [
            "Basis",
            "Direction",
            "Flow",
            "Grow",
            "Shrink",
            "Wrap"
        ]);
        combine("offset", [
            ...after_before,
            ...end_start,
            "Anchor",
            "Distance",
            "Path",
            "Position",
            "Rotate"
        ]);
        combine("perspective", [
            "Origin"
        ]);
        combine("clip", [
            "Path",
            "Rule"
        ]);
        combine("flow", [
            "From",
            "Into"
        ]);
        combine("align", [
            "Content",
            "Items",
            "Self"
        ], true);
        combine("alignment", [
            "Adjust",
            "Baseline"
        ], true);
        combine("borderStart", endRadius_startRadius, true);
        combine("borderEnd", endRadius_startRadius, true);
        combine("borderCorner", [
            "Fit",
            image,
            "ImageTransform"
        ], true);
        combine("borderTopLeft", fitlength_fitwidth_image_radius, true);
        combine("borderTopRight", fitlength_fitwidth_image_radius, true);
        combine("borderBottomLeft", fitlength_fitwidth_image_radius, true);
        combine("borderBottomRight", fitlength_fitwidth_image_radius, true);
        combine("column", [
            "s",
            "Count",
            "Fill",
            "Gap",
            "Rule",
            "RuleColor",
            "RuleStyle",
            "RuleWidth",
            "Span",
            width
        ], true);
        combine("break", [
            ...after_before,
            "Inside"
        ], true);
        combine("wrap", [
            ...after_before,
            "Flow",
            "Inside",
            "Through"
        ], true);
        combine("justify", content_items_self, true);
        combine("place", content_items_self, true);
        combine("max", [
            ...blockSize_height_inlineSize_width,
            "Lines"
        ], true);
        combine("min", blockSize_height_inlineSize_width, true);
        combine("line", [
            "Break",
            "Clamp",
            "Grid",
            "Height",
            "Padding",
            "Snap"
        ], true);
        combine("inline", [
            "BoxAlign",
            size,
            "Sizing"
        ], true);
        combine("text", [
            "CombineUpright",
            "GroupAlign",
            "Height",
            "Indent",
            "Justify",
            "Orientation",
            "Overflow",
            "Shadow",
            "SpaceCollapse",
            "SpaceTrim",
            "Spacing",
            "Transform",
            "UnderlinePosition",
            "Wrap"
        ], true);
        combine("shape", [
            "ImageThreshold",
            "Inside",
            "Margin",
            "Outside"
        ], true);
        combine("word", [
            "Break",
            "Spacing",
            "Wrap"
        ], true);
        combine("object", [
            "Fit",
            "Position"
        ], true);
        combine("box", [
            "DecorationBreak",
            "Shadow",
            "Sizing",
            "Snap"
        ], true);
        combine(WEBKIT, [
            "LineClamp",
            "BoxOrient",
            "TextFillColor",
            "TextStroke",
            "TextStrokeColor",
            "TextStrokeWidth"
        ], true);
    }
});
// node_modules/@tarojs/runtime/dist/dom/style.js
function recordCss(obj) {
    MutationObserver2.record({
        type: "attributes",
        target: obj._element,
        attributeName: "style",
        oldValue: obj.cssText
    });
}
function enqueueUpdate(obj) {
    const element = obj._element;
    if (element._root) {
        element.enqueueUpdate({
            path: `${element._path}.${"st"}`,
            value: obj.cssText
        });
    }
}
function setStyle(newVal, styleKey) {
    warn(isString(newVal) && newVal.length > PROPERTY_THRESHOLD, `Style \u5C5E\u6027 ${styleKey} \u7684\u503C\u6570\u636E\u91CF\u8FC7\u5927\uFF0C\u53EF\u80FD\u4F1A\u5F71\u54CD\u6E32\u67D3\u6027\u80FD\uFF0C\u8003\u8651\u4F7F\u7528 CSS \u7C7B\u6216\u5176\u5B83\u65B9\u6848\u66FF\u4EE3\u3002`);
    const old = this[styleKey];
    if (old === newVal) return;
    !this._pending && recordCss(this);
    if (isNull(newVal) || isUndefined(newVal) || newVal === "") {
        this._usedStyleProp.delete(styleKey);
        delete this._value[styleKey];
    } else {
        this._usedStyleProp.add(styleKey);
        this._value[styleKey] = newVal;
    }
    !this._pending && enqueueUpdate(this);
}
function initStyle(ctor, styleProperties2) {
    const properties = {};
    for(let i = 0; i < styleProperties2.length; i++){
        const styleKey = styleProperties2[i];
        if (ctor[styleKey]) return;
        properties[styleKey] = {
            get () {
                const val = this._value[styleKey];
                return isNull(val) || isUndefined(val) ? "" : val;
            },
            set (newVal) {
                setStyle.call(this, newVal, styleKey);
            }
        };
    }
    Object.defineProperties(ctor.prototype, properties);
}
function isCssVariable(propertyName) {
    return /^--/.test(propertyName);
}
var Style;
var init_style = __esm({
    "node_modules/@tarojs/runtime/dist/dom/style.js" () {
        init_dist();
        init_constants();
        init_mutation_observer();
        init_style_properties();
        Style = class {
            setCssVariables(styleKey) {
                this.hasOwnProperty(styleKey) || Object.defineProperty(this, styleKey, {
                    enumerable: true,
                    configurable: true,
                    get: ()=>{
                        return this._value[styleKey] || "";
                    },
                    set: (newVal)=>{
                        setStyle.call(this, newVal, styleKey);
                    }
                });
            }
            get cssText() {
                if (!this._usedStyleProp.size) return "";
                const texts = [];
                this._usedStyleProp.forEach((key)=>{
                    const val = this[key];
                    if (isNull(val) || isUndefined(val)) return;
                    let styleName = isCssVariable(key) ? key : toDashed(key);
                    if (styleName.indexOf("webkit") === 0 || styleName.indexOf("Webkit") === 0) {
                        styleName = `-${styleName}`;
                    }
                    texts.push(`${styleName}: ${val};`);
                });
                return texts.join(" ");
            }
            set cssText(str) {
                this._pending = true;
                recordCss(this);
                this._usedStyleProp.forEach((prop)=>{
                    this.removeProperty(prop);
                });
                if (str === "" || isUndefined(str) || isNull(str)) {
                    this._pending = false;
                    enqueueUpdate(this);
                    return;
                }
                const rules = str.split(";");
                for(let i = 0; i < rules.length; i++){
                    const rule = rules[i].trim();
                    if (rule === "") {
                        continue;
                    }
                    const [propName, ...valList] = rule.split(":");
                    const val = valList.join(":");
                    if (isUndefined(val)) {
                        continue;
                    }
                    this.setProperty(propName.trim(), val.trim());
                }
                this._pending = false;
                enqueueUpdate(this);
            }
            setProperty(propertyName, value) {
                if (propertyName[0] === "-") {
                    this.setCssVariables(propertyName);
                } else {
                    propertyName = toCamelCase(propertyName);
                }
                if (isNull(value) || isUndefined(value)) {
                    this.removeProperty(propertyName);
                } else {
                    this[propertyName] = value;
                }
            }
            removeProperty(propertyName) {
                propertyName = toCamelCase(propertyName);
                if (!this._usedStyleProp.has(propertyName)) {
                    return "";
                }
                const value = this[propertyName];
                this[propertyName] = void 0;
                return value;
            }
            getPropertyValue(propertyName) {
                propertyName = toCamelCase(propertyName);
                const value = this[propertyName];
                if (!value) {
                    return "";
                }
                return value;
            }
            constructor(element){
                this._element = element;
                this._usedStyleProp = /* @__PURE__ */ new Set();
                this._value = {};
            }
        };
        initStyle(Style, styleProperties);
        hooks.tap("injectNewStyleProperties", (newStyleProperties)=>{
            if (isArray(newStyleProperties)) {
                initStyle(Style, newStyleProperties);
            } else {
                if (typeof newStyleProperties !== "string") return;
                initStyle(Style, [
                    newStyleProperties
                ]);
            }
        });
    }
});
// node_modules/@tarojs/runtime/dist/dom/class-list.js
var ClassList;
var init_class_list = __esm({
    "node_modules/@tarojs/runtime/dist/dom/class-list.js" () {
        ClassList = class {
            get value() {
                return this.toString();
            }
            get length() {
                return this.tokenList.length;
            }
            add() {
                let index = 0;
                let updated = false;
                const tokens = arguments;
                const length = tokens.length;
                const tokenList = this.tokenList;
                do {
                    const token = tokens[index];
                    if (this.checkTokenIsValid(token) && !~tokenList.indexOf(token)) {
                        tokenList.push(token);
                        updated = true;
                    }
                }while (++index < length)
                if (updated) {
                    this._update();
                }
            }
            remove() {
                let i = 0;
                let updated = false;
                const tokens = arguments;
                const length = tokens.length;
                const tokenList = this.tokenList;
                do {
                    const token = tokens[i] + "";
                    if (!this.checkTokenIsValid(token)) continue;
                    const index = tokenList.indexOf(token);
                    if (~tokenList.indexOf(token)) {
                        tokenList.splice(index, 1);
                        updated = true;
                    }
                }while (++i < length)
                if (updated) {
                    this._update();
                }
            }
            contains(token) {
                if (!this.checkTokenIsValid(token)) return false;
                return !!~this.tokenList.indexOf(token);
            }
            toggle(token, force) {
                const result = this.contains(token);
                const method = result ? force !== true && "remove" : force !== false && "add";
                if (method) {
                    this[method](token);
                }
                if (force === true || force === false) {
                    return force;
                } else {
                    return !result;
                }
            }
            replace(token, replacement_token) {
                if (!this.checkTokenIsValid(token) || !this.checkTokenIsValid(replacement_token)) return;
                const index = this.tokenList.indexOf(token);
                if (~index) {
                    this.tokenList.splice(index, 1, replacement_token);
                    this._update();
                }
            }
            toString() {
                return this.tokenList.filter((v)=>v !== "").join(" ");
            }
            checkTokenIsValid(token) {
                if (token === "" || /\s/.test(token)) return false;
                return true;
            }
            _update() {
                this.el.className = this.value;
            }
            constructor(className, el){
                this.tokenList = [];
                this.el = el;
                className.trim().split(/\s+/).forEach((token)=>this.tokenList.push(token));
            }
        };
    }
});
// node_modules/@tarojs/runtime/dist/dom/tree.js
function returnTrue() {
    return true;
}
function treeToArray(root, predict) {
    const array = [];
    const filter = predict !== null && predict !== void 0 ? predict : returnTrue;
    let object = root;
    while(object){
        if (object.nodeType === 1 && filter(object)) {
            array.push(object);
        }
        object = following(object, root);
    }
    return array;
}
function following(el, root) {
    const firstChild = el.firstChild;
    const isElmentTypeValid = el.nodeType === 1 || el.nodeType === 9;
    if (firstChild && isElmentTypeValid) {
        return firstChild;
    }
    let current = el;
    do {
        if (current === root) {
            return null;
        }
        const nextSibling = current.nextSibling;
        if (nextSibling) {
            return nextSibling;
        }
        current = current.parentElement;
    }while (current)
    return null;
}
var init_tree = __esm({
    "node_modules/@tarojs/runtime/dist/dom/tree.js" () {}
});
// node_modules/@tarojs/runtime/dist/dom/element.js
var TaroElement;
var init_element = __esm({
    "node_modules/@tarojs/runtime/dist/dom/element.js" () {
        init_dist();
        init_constants();
        init_mutation_observer();
        init_utils();
        init_class_list();
        init_event_source();
        init_node();
        init_style();
        init_tree();
        TaroElement = class _TaroElement extends TaroNode {
            _stopPropagation(event) {
                let target = this;
                while(target = target.parentNode){
                    const listeners = target.__handlers[event.type];
                    if (!isArray(listeners)) {
                        continue;
                    }
                    for(let i = listeners.length; i--;){
                        const l = listeners[i];
                        l._stop = true;
                    }
                }
            }
            get id() {
                return this.getAttribute(ID);
            }
            set id(val) {
                this.setAttribute(ID, val);
            }
            get className() {
                return this.getAttribute(CLASS) || "";
            }
            set className(val) {
                this.setAttribute(CLASS, val);
            }
            get cssText() {
                return this.getAttribute(STYLE) || "";
            }
            get classList() {
                return new ClassList(this.className, this);
            }
            get children() {
                return this.childNodes.filter(isElement);
            }
            get attributes() {
                const props = this.props;
                const propKeys = Object.keys(props);
                const style2 = this.style.cssText;
                const attrs = propKeys.map((key)=>({
                        name: key,
                        value: props[key]
                    }));
                return attrs.concat(style2 ? {
                    name: STYLE,
                    value: style2
                } : []);
            }
            get textContent() {
                let text = "";
                const childNodes = this.childNodes;
                for(let i = 0; i < childNodes.length; i++){
                    text += childNodes[i].textContent;
                }
                return text;
            }
            set textContent(text) {
                super.textContent = text;
            }
            hasAttribute(qualifiedName) {
                return !isUndefined(this.props[qualifiedName]);
            }
            hasAttributes() {
                return this.attributes.length > 0;
            }
            get focus() {
                return function() {
                    this.setAttribute(FOCUS, true);
                };
            }
            // 兼容 Vue3，详情请见：https://github.com/NervJS/taro/issues/10579
            set focus(value) {
                this.setAttribute(FOCUS, value);
            }
            blur() {
                this.setAttribute(FOCUS, false);
            }
            setAttribute(qualifiedName, value) {
                warn(isString(value) && value.length > PROPERTY_THRESHOLD, `\u5143\u7D20 ${this.nodeName} \u7684 ${qualifiedName} \u5C5E\u6027\u503C\u6570\u636E\u91CF\u8FC7\u5927\uFF0C\u53EF\u80FD\u4F1A\u5F71\u54CD\u6E32\u67D3\u6027\u80FD\u3002\u8003\u8651\u964D\u4F4E\u56FE\u7247\u8F6C\u4E3A base64 \u7684\u9608\u503C\u6216\u5728 CSS \u4E2D\u4F7F\u7528 base64\u3002`);
                const isPureView = this.nodeName === VIEW && !isHasExtractProp(this) && !this.isAnyEventBinded();
                if (qualifiedName !== STYLE) {
                    MutationObserver2.record({
                        target: this,
                        type: "attributes",
                        attributeName: qualifiedName,
                        oldValue: this.getAttribute(qualifiedName)
                    });
                }
                switch(qualifiedName){
                    case STYLE:
                        this.style.cssText = value;
                        break;
                    case ID:
                        if (this.uid !== this.sid) {
                            eventSource.delete(this.uid);
                        }
                        value = String(value);
                        this.props[qualifiedName] = this.uid = value;
                        eventSource.set(value, this);
                        break;
                    default:
                        this.props[qualifiedName] = value;
                        if (qualifiedName.startsWith("data-")) {
                            if (this.dataset === EMPTY_OBJ) {
                                this.dataset = /* @__PURE__ */ Object.create(null);
                            }
                            this.dataset[toCamelCase(qualifiedName.replace(/^data-/, ""))] = value;
                        }
                        break;
                }
                if (!this._root) return;
                const componentsAlias3 = getComponentsAlias2();
                const _alias = componentsAlias3[this.nodeName];
                const viewAlias = componentsAlias3[VIEW]._num;
                const clickViewAlias = componentsAlias3[CLICK_VIEW]._num;
                const staticViewAlias = componentsAlias3[STATIC_VIEW]._num;
                const catchViewAlias = componentsAlias3[CATCH_VIEW]._num;
                const _path = this._path;
                qualifiedName = shortcutAttr(qualifiedName);
                const qualifiedNameInCamelCase = toCamelCase(qualifiedName);
                const payload = {
                    path: `${_path}.${qualifiedNameInCamelCase}`,
                    value: isFunction(value) ? ()=>value : value
                };
                hooks.call("modifySetAttrPayload", this, qualifiedName, payload, componentsAlias3);
                if (_alias) {
                    const qualifiedNameAlias = _alias[qualifiedNameInCamelCase] || qualifiedName;
                    payload.path = `${_path}.${toCamelCase(qualifiedNameAlias)}`;
                }
                this.enqueueUpdate(payload);
                if (this.nodeName === VIEW) {
                    if (qualifiedNameInCamelCase === CATCHMOVE) {
                        this.enqueueUpdate({
                            path: `${_path}.${"nn"}`,
                            value: value ? catchViewAlias : this.isOnlyClickBinded() && !isHasExtractProp(this) ? clickViewAlias : this.isAnyEventBinded() ? viewAlias : staticViewAlias
                        });
                    } else if (isPureView && isHasExtractProp(this)) {
                        this.enqueueUpdate({
                            path: `${_path}.${"nn"}`,
                            value: staticViewAlias
                        });
                    }
                }
            }
            removeAttribute(qualifiedName) {
                const isStaticView = this.nodeName === VIEW && isHasExtractProp(this) && !this.isAnyEventBinded();
                MutationObserver2.record({
                    target: this,
                    type: "attributes",
                    attributeName: qualifiedName,
                    oldValue: this.getAttribute(qualifiedName)
                });
                if (qualifiedName === STYLE) {
                    this.style.cssText = "";
                } else {
                    const isInterrupt = hooks.call("onRemoveAttribute", this, qualifiedName);
                    if (isInterrupt) {
                        return;
                    }
                    if (!this.props.hasOwnProperty(qualifiedName)) {
                        return;
                    }
                    delete this.props[qualifiedName];
                }
                if (!this._root) return;
                const componentsAlias3 = getComponentsAlias2();
                const _alias = componentsAlias3[this.nodeName];
                const viewAlias = componentsAlias3[VIEW]._num;
                const staticViewAlias = componentsAlias3[STATIC_VIEW]._num;
                const pureViewAlias = componentsAlias3[PURE_VIEW]._num;
                const clickViewAlias = componentsAlias3[CLICK_VIEW]._num;
                const _path = this._path;
                qualifiedName = shortcutAttr(qualifiedName);
                const qualifiedNameInCamelCase = toCamelCase(qualifiedName);
                const payload = {
                    path: `${_path}.${qualifiedNameInCamelCase}`,
                    value: ""
                };
                hooks.call("modifyRmAttrPayload", this, qualifiedName, payload, componentsAlias3);
                if (_alias) {
                    const qualifiedNameAlias = _alias[qualifiedNameInCamelCase] || qualifiedName;
                    payload.path = `${_path}.${toCamelCase(qualifiedNameAlias)}`;
                }
                this.enqueueUpdate(payload);
                if (this.nodeName === VIEW) {
                    if (qualifiedNameInCamelCase === CATCHMOVE) {
                        this.enqueueUpdate({
                            path: `${_path}.${"nn"}`,
                            value: this.isOnlyClickBinded() && !isHasExtractProp(this) ? clickViewAlias : this.isAnyEventBinded() ? viewAlias : isHasExtractProp(this) ? staticViewAlias : pureViewAlias
                        });
                    } else if (isStaticView && !isHasExtractProp(this)) {
                        this.enqueueUpdate({
                            path: `${_path}.${"nn"}`,
                            value: pureViewAlias
                        });
                    }
                }
            }
            getAttribute(qualifiedName) {
                const attr = qualifiedName === STYLE ? this.style.cssText : this.props[qualifiedName];
                return attr !== null && attr !== void 0 ? attr : "";
            }
            getElementsByTagName(tagName) {
                return treeToArray(this, (el)=>{
                    return el.nodeName === tagName || tagName === "*" && this !== el;
                });
            }
            getElementsByClassName(className) {
                const classNames = className.trim().split(/\s+/);
                return treeToArray(this, (el)=>{
                    const classList = el.classList;
                    return classNames.every((c)=>classList.contains(c));
                });
            }
            dispatchEvent(event) {
                const cancelable = event.cancelable;
                const listeners = this.__handlers[event.type];
                if (!isArray(listeners)) {
                    return false;
                }
                for(let i = listeners.length; i--;){
                    const listener = listeners[i];
                    let result;
                    if (listener._stop) {
                        listener._stop = false;
                    } else {
                        hooks.call("modifyDispatchEvent", event, this);
                        result = listener.call(this, event);
                    }
                    if ((result === false || event._end) && cancelable) {
                        event.defaultPrevented = true;
                    }
                    if (!isUndefined(result) && event.mpEvent) {
                        const res = hooks.call("modifyTaroEventReturn", this, event, result);
                        if (res) {
                            event.mpEvent[EVENT_CALLBACK_RESULT] = result;
                        }
                    }
                    if (event._end && event._stop) {
                        break;
                    }
                }
                if (event._stop) {
                    this._stopPropagation(event);
                }
                return listeners != null;
            }
            addEventListener(type, handler, options2) {
                const name = this.nodeName;
                const SPECIAL_NODES2 = hooks.call("getSpecialNodes");
                let sideEffect = true;
                if (isObject(options2) && options2.sideEffect === false) {
                    sideEffect = false;
                    delete options2.sideEffect;
                }
                hooks.call("modifyAddEventListener", this, sideEffect, getComponentsAlias2);
                if (sideEffect !== false && !this.isAnyEventBinded() && SPECIAL_NODES2.indexOf(name) > -1) {
                    const componentsAlias3 = getComponentsAlias2();
                    const alias = componentsAlias3[name]._num;
                    this.enqueueUpdate({
                        path: `${this._path}.${"nn"}`,
                        value: alias
                    });
                }
                super.addEventListener(type, handler, options2);
            }
            removeEventListener(type, handler, sideEffect = true) {
                super.removeEventListener(type, handler);
                const name = this.nodeName;
                const SPECIAL_NODES2 = hooks.call("getSpecialNodes");
                hooks.call("modifyRemoveEventListener", this, sideEffect, getComponentsAlias2);
                if (sideEffect !== false && !this.isAnyEventBinded() && SPECIAL_NODES2.indexOf(name) > -1) {
                    const componentsAlias3 = getComponentsAlias2();
                    const value = isHasExtractProp(this) ? `static-${name}` : `pure-${name}`;
                    const valueAlias = componentsAlias3[value]._num;
                    this.enqueueUpdate({
                        path: `${this._path}.${"nn"}`,
                        value: valueAlias
                    });
                }
            }
            static extend(methodName, options2) {
                extend(_TaroElement, methodName, options2);
            }
            constructor(){
                super();
                this.props = {};
                this.dataset = EMPTY_OBJ;
                this.nodeType = 1;
                this.style = new Style(this);
                hooks.call("patchElement", this);
            }
        };
    }
});
// node_modules/@tarojs/runtime/dist/options.js
var options;
var init_options = __esm({
    "node_modules/@tarojs/runtime/dist/options.js" () {
        options = {
            prerender: true,
            debug: false
        };
    }
});
// node_modules/@tarojs/runtime/dist/dom/event.js
function createEvent(event, node) {
    if (typeof event === "string") {
        return new TaroEvent(event, {
            bubbles: true,
            cancelable: true
        });
    }
    const domEv = new TaroEvent(event.type, {
        bubbles: true,
        cancelable: true
    }, event);
    for(const key in event){
        if (key === CURRENT_TARGET || key === TARGET || key === TYPE || key === TIME_STAMP) {
            continue;
        } else {
            domEv[key] = event[key];
        }
    }
    if (domEv.type === CONFIRM && (node === null || node === void 0 ? void 0 : node.nodeName) === INPUT) {
        domEv[KEY_CODE] = 13;
    }
    return domEv;
}
function getEventCBResult(event) {
    const result = event[EVENT_CALLBACK_RESULT];
    if (!isUndefined(result)) {
        delete event[EVENT_CALLBACK_RESULT];
    }
    return result;
}
function eventHandler(event) {
    var _a2, _b;
    event.type === void 0 && Object.defineProperty(event, "type", {
        value: event._type
    });
    event.detail === void 0 && Object.defineProperty(event, "detail", {
        value: event._detail || Object.assign({}, event)
    });
    event.currentTarget = event.currentTarget || event.target || Object.assign({}, event);
    hooks.call("modifyMpEventImpl", event);
    const currentTarget = event.currentTarget;
    const id = ((_a2 = currentTarget.dataset) === null || _a2 === void 0 ? void 0 : _a2.sid) || currentTarget.id || ((_b = event.detail) === null || _b === void 0 ? void 0 : _b.id) || "";
    const node = env.document.getElementById(id);
    if (node) {
        const dispatch = ()=>{
            const e = createEvent(event, node);
            hooks.call("modifyTaroEvent", e, node);
            hooks.call("dispatchTaroEvent", e, node);
            hooks.call("dispatchTaroEventFinish", e, node);
        };
        if (hooks.isExist("batchedEventUpdates")) {
            const type = event.type;
            if (!hooks.call("isBubbleEvents", type) || !isParentBinded(node, type) || type === TOUCHMOVE && !!node.props.catchMove) {
                hooks.call("batchedEventUpdates", ()=>{
                    if (eventsBatch[type]) {
                        eventsBatch[type].forEach((fn)=>fn());
                        delete eventsBatch[type];
                    }
                    dispatch();
                });
                return getEventCBResult(event);
            } else {
                (eventsBatch[type] || (eventsBatch[type] = [])).push(dispatch);
            }
        } else {
            dispatch();
            return getEventCBResult(event);
        }
    }
}
var TaroEvent, eventsBatch;
var init_event = __esm({
    "node_modules/@tarojs/runtime/dist/dom/event.js" () {
        init_dist();
        init_constants();
        init_env();
        init_utils();
        TaroEvent = class {
            stopPropagation() {
                this._stop = true;
            }
            stopImmediatePropagation() {
                this._end = this._stop = true;
            }
            preventDefault() {
                this.defaultPrevented = true;
            }
            get target() {
                var _a2, _b, _c, _d, _e;
                const cacheTarget = this.cacheTarget;
                if (!cacheTarget) {
                    const target = Object.create(((_a2 = this.mpEvent) === null || _a2 === void 0 ? void 0 : _a2.target) || null);
                    const currentEle = env.document.getElementById(((_b = target.dataset) === null || _b === void 0 ? void 0 : _b.sid) || target.id || null);
                    const element = env.document.getElementById(((_c = target.targetDataset) === null || _c === void 0 ? void 0 : _c.sid) || ((_d = target.dataset) === null || _d === void 0 ? void 0 : _d.sid) || target.id || null);
                    target.dataset = Object.assign(Object.assign({}, currentEle !== null ? currentEle.dataset : EMPTY_OBJ), element !== null ? element.dataset : EMPTY_OBJ);
                    for(const key in (_e = this.mpEvent) === null || _e === void 0 ? void 0 : _e.detail){
                        target[key] = this.mpEvent.detail[key];
                    }
                    this.cacheTarget = target;
                    return target;
                } else {
                    return cacheTarget;
                }
            }
            get currentTarget() {
                var _a2, _b, _c, _d, _e, _f, _g, _h;
                const cacheCurrentTarget = this.cacheCurrentTarget;
                if (!cacheCurrentTarget) {
                    const doc = env.document;
                    const currentTarget = Object.create(((_a2 = this.mpEvent) === null || _a2 === void 0 ? void 0 : _a2.currentTarget) || null);
                    const element = doc.getElementById(((_b = currentTarget.dataset) === null || _b === void 0 ? void 0 : _b.sid) || currentTarget.id || null);
                    const targetElement = doc.getElementById(((_e = (_d = (_c = this.mpEvent) === null || _c === void 0 ? void 0 : _c.target) === null || _d === void 0 ? void 0 : _d.dataset) === null || _e === void 0 ? void 0 : _e.sid) || ((_g = (_f = this.mpEvent) === null || _f === void 0 ? void 0 : _f.target) === null || _g === void 0 ? void 0 : _g.id) || null);
                    if (element === null || element && element === targetElement) {
                        this.cacheCurrentTarget = this.target;
                        return this.target;
                    }
                    currentTarget.dataset = element.dataset;
                    for(const key in (_h = this.mpEvent) === null || _h === void 0 ? void 0 : _h.detail){
                        currentTarget[key] = this.mpEvent.detail[key];
                    }
                    this.cacheCurrentTarget = currentTarget;
                    return currentTarget;
                } else {
                    return cacheCurrentTarget;
                }
            }
            constructor(type, opts, event){
                this._stop = false;
                this._end = false;
                this.defaultPrevented = false;
                this.button = 0;
                this.timeStamp = Date.now();
                this.type = type.toLowerCase();
                this.mpEvent = event;
                this.bubbles = Boolean(opts && opts.bubbles);
                this.cancelable = Boolean(opts && opts.cancelable);
            }
        };
        eventsBatch = {};
    }
});
// node_modules/@tarojs/runtime/dist/dom/form.js
var FormElement;
var init_form = __esm({
    "node_modules/@tarojs/runtime/dist/dom/form.js" () {
        init_constants();
        init_element();
        FormElement = class extends TaroElement {
            get type() {
                var _a2;
                return (_a2 = this.props[TYPE]) !== null && _a2 !== void 0 ? _a2 : "";
            }
            set type(val) {
                this.setAttribute(TYPE, val);
            }
            get value() {
                const val = this.props[VALUE];
                return val == null ? "" : val;
            }
            set value(val) {
                this.setAttribute(VALUE, val);
            }
            dispatchEvent(event) {
                if (event.mpEvent) {
                    const val = event.mpEvent.detail.value;
                    if (event.type === CHANGE) {
                        this.props.value = val;
                    } else if (event.type === INPUT) {
                        this.value = val;
                    }
                }
                return super.dispatchEvent(event);
            }
        };
    }
});
// node_modules/@tarojs/runtime/dist/utils/lodash.js
function throttle(fn, threshold = 250, scope) {
    let lastTime2 = 0;
    let deferTimer;
    return function(...args) {
        const context = scope || this;
        const now2 = Date.now();
        if (now2 - lastTime2 > threshold) {
            fn.apply(this, args);
            lastTime2 = now2;
        } else {
            clearTimeout(deferTimer);
            deferTimer = setTimeout(()=>{
                lastTime2 = now2;
                fn.apply(context, args);
            }, threshold);
        }
    };
}
function debounce(fn, ms = 250, scope) {
    let timer;
    return function(...args) {
        const context = scope || this;
        clearTimeout(timer);
        timer = setTimeout(function() {
            fn.apply(context, args);
        }, ms);
    };
}
var init_lodash = __esm({
    "node_modules/@tarojs/runtime/dist/utils/lodash.js" () {}
});
// node_modules/@tarojs/runtime/dist/perf.js
var _Performance_instances, _Performance_parseTime, Performance, perf;
var init_perf = __esm({
    "node_modules/@tarojs/runtime/dist/perf.js" () {
        init_tslib_es6();
        init_options();
        init_dist();
        init_constants();
        init_lodash();
        init_window();
        Performance = class {
            start(id) {
                if (!options.debug) {
                    return;
                }
                this.recorder.set(id, Date.now());
            }
            stop(id, now2 = Date.now()) {
                if (!options.debug) {
                    return;
                }
                const prev = this.recorder.get(id);
                if (!(prev >= 0)) return;
                this.recorder.delete(id);
                const time = now2 - prev;
                console.log(`${id} \u65F6\u957F\uFF1A ${time}ms \u5F00\u59CB\u65F6\u95F4\uFF1A${__classPrivateFieldGet(this, _Performance_instances, "m", _Performance_parseTime).call(this, prev)} \u7ED3\u675F\u65F6\u95F4\uFF1A${__classPrivateFieldGet(this, _Performance_instances, "m", _Performance_parseTime).call(this, now2)}`);
            }
            delayStop(id, delay = 500) {
                if (!options.debug) {
                    return;
                }
                return debounce((now2 = Date.now(), cb)=>{
                    this.stop(id, now2);
                    cb === null || cb === void 0 ? void 0 : cb();
                }, delay);
            }
            constructor(){
                _Performance_instances.add(this);
                this.recorder = /* @__PURE__ */ new Map();
            }
        };
        _Performance_instances = /* @__PURE__ */ new WeakSet(), _Performance_parseTime = function _Performance_parseTime2(time) {
            const d = new Date(time);
            return `${d.getHours()}:${d.getMinutes()}:${d.getSeconds()}.${`${d.getMilliseconds()}`.padStart(3, "0")}`;
        };
        perf = new Performance();
    }
});
// node_modules/@tarojs/runtime/dist/dom/root.js
function findCustomWrapper(root, dataPathArr) {
    const list = dataPathArr.slice(1);
    let currentData = root;
    let customWrapper;
    let splitedPath = "";
    list.some((item, i)=>{
        const key = item.replace(/^\[(.+)\]$/, "$1").replace(/\bcn\b/g, "childNodes");
        currentData = currentData[key];
        if (isArray(currentData)) {
            currentData = currentData.filter((el)=>!isComment(el));
        }
        if (isUndefined(currentData)) return true;
        if (currentData.nodeName === CUSTOM_WRAPPER) {
            const res = customWrapperCache.get(currentData.sid);
            if (res) {
                customWrapper = res;
                splitedPath = dataPathArr.slice(i + 2).join(".");
            }
        }
    });
    if (customWrapper) {
        return {
            customWrapper,
            splitedPath
        };
    }
}
var TaroRootElement;
var init_root = __esm({
    "node_modules/@tarojs/runtime/dist/dom/root.js" () {
        init_dist();
        init_constants();
        init_options();
        init_perf();
        init_utils();
        init_element();
        TaroRootElement = class extends TaroElement {
            get _path() {
                return ROOT_STR;
            }
            get _root() {
                return this;
            }
            scheduleTask(fn) {
                setTimeout(fn);
            }
            enqueueUpdate(payload) {
                this.updatePayloads.push(payload);
                if (!this.pendingUpdate && this.ctx) {
                    this.performUpdate();
                }
            }
            performUpdate(initRender = false, prerender) {
                this.pendingUpdate = true;
                const ctx = hooks.call("proxyToRaw", this.ctx);
                this.scheduleTask(()=>{
                    const setDataMark = `${SET_DATA} \u5F00\u59CB\u65F6\u95F4\u6233 ${Date.now()}`;
                    perf.start(setDataMark);
                    const data = /* @__PURE__ */ Object.create(null);
                    const resetPaths = new Set(initRender ? [
                        "root.cn.[0]",
                        "root.cn[0]"
                    ] : []);
                    while(this.updatePayloads.length > 0){
                        const { path, value } = this.updatePayloads.shift();
                        if (path.endsWith("cn")) {
                            resetPaths.add(path);
                        }
                        data[path] = value;
                    }
                    for(const path in data){
                        resetPaths.forEach((p)=>{
                            if (path.includes(p) && path !== p) {
                                delete data[path];
                            }
                        });
                        const value = data[path];
                        if (isFunction(value)) {
                            data[path] = value();
                        }
                    }
                    if (isFunction(prerender)) return prerender(data);
                    this.pendingUpdate = false;
                    let normalUpdate = {};
                    const customWrapperMap = /* @__PURE__ */ new Map();
                    if (initRender) {
                        normalUpdate = data;
                    } else {
                        for(const p in data){
                            const dataPathArr = p.split(".");
                            const found = findCustomWrapper(this, dataPathArr);
                            if (found) {
                                const { customWrapper, splitedPath } = found;
                                customWrapperMap.set(customWrapper, Object.assign(Object.assign({}, customWrapperMap.get(customWrapper) || {}), {
                                    [`i.${splitedPath}`]: data[p]
                                }));
                            } else {
                                normalUpdate[p] = data[p];
                            }
                        }
                    }
                    const customWrapperCount = customWrapperMap.size;
                    const isNeedNormalUpdate = Object.keys(normalUpdate).length > 0;
                    const updateArrLen = customWrapperCount + (isNeedNormalUpdate ? 1 : 0);
                    let executeTime = 0;
                    const cb = ()=>{
                        if (++executeTime === updateArrLen) {
                            perf.stop(setDataMark);
                            this.flushUpdateCallback();
                            initRender && perf.stop(PAGE_INIT);
                        }
                    };
                    if (customWrapperCount) {
                        customWrapperMap.forEach((data2, ctx2)=>{
                            if (options.debug) {
                                console.log("custom wrapper setData: ", data2);
                            }
                            ctx2.setData(data2, cb);
                        });
                    }
                    if (isNeedNormalUpdate) {
                        if (options.debug) {
                            console.log("page setData:", normalUpdate);
                        }
                        ctx.setData(normalUpdate, cb);
                    }
                });
            }
            enqueueUpdateCallback(cb, ctx) {
                this.updateCallbacks.push(()=>{
                    ctx ? cb.call(ctx) : cb();
                });
            }
            flushUpdateCallback() {
                const updateCallbacks = this.updateCallbacks;
                if (!updateCallbacks.length) return;
                const copies = updateCallbacks.slice(0);
                this.updateCallbacks.length = 0;
                for(let i = 0; i < copies.length; i++){
                    copies[i]();
                }
            }
            constructor(){
                super();
                this.updatePayloads = [];
                this.updateCallbacks = [];
                this.pendingUpdate = false;
                this.ctx = null;
                this.nodeName = ROOT_STR;
                this.tagName = ROOT_STR.toUpperCase();
            }
        };
    }
});
// node_modules/@tarojs/runtime/dist/dom/text.js
var TaroText;
var init_text = __esm({
    "node_modules/@tarojs/runtime/dist/dom/text.js" () {
        init_mutation_observer();
        init_node();
        TaroText = class extends TaroNode {
            set textContent(text) {
                MutationObserver2.record({
                    target: this,
                    type: "characterData",
                    oldValue: this._value
                });
                this._value = text;
                this.enqueueUpdate({
                    path: `${this._path}.${"v"}`,
                    value: text
                });
            }
            get textContent() {
                return this._value;
            }
            set nodeValue(text) {
                this.textContent = text;
            }
            get nodeValue() {
                return this._value;
            }
            set data(text) {
                this.textContent = text;
            }
            get data() {
                return this._value;
            }
            constructor(value){
                super();
                this.nodeType = 3;
                this.nodeName = "#text";
                this._value = value;
            }
        };
    }
});
// node_modules/@tarojs/runtime/dist/dom/anchor-element.js
var AnchorElement;
var init_anchor_element = __esm({
    "node_modules/@tarojs/runtime/dist/dom/anchor-element.js" () {
        init_URL();
        init_element();
        AnchorElement = class extends TaroElement {
            get href() {
                var _a2;
                return (_a2 = this.props["href"]) !== null && _a2 !== void 0 ? _a2 : "";
            }
            set href(val) {
                this.setAttribute("href", val);
            }
            get protocol() {
                var _a2;
                return (_a2 = this.props["protocol"]) !== null && _a2 !== void 0 ? _a2 : "";
            }
            get host() {
                var _a2;
                return (_a2 = this.props["host"]) !== null && _a2 !== void 0 ? _a2 : "";
            }
            get search() {
                var _a2;
                return (_a2 = this.props["search"]) !== null && _a2 !== void 0 ? _a2 : "";
            }
            get hash() {
                var _a2;
                return (_a2 = this.props["hash"]) !== null && _a2 !== void 0 ? _a2 : "";
            }
            get hostname() {
                var _a2;
                return (_a2 = this.props["hostname"]) !== null && _a2 !== void 0 ? _a2 : "";
            }
            get port() {
                var _a2;
                return (_a2 = this.props["port"]) !== null && _a2 !== void 0 ? _a2 : "";
            }
            get pathname() {
                var _a2;
                return (_a2 = this.props["pathname"]) !== null && _a2 !== void 0 ? _a2 : "";
            }
            setAttribute(qualifiedName, value) {
                if (qualifiedName === "href") {
                    const willSetAttr = parseUrl(value);
                    for(const k in willSetAttr){
                        super.setAttribute(k, willSetAttr[k]);
                    }
                } else {
                    super.setAttribute(qualifiedName, value);
                }
            }
        };
    }
});
// node_modules/@tarojs/runtime/dist/dom/transfer.js
var TransferElement;
var init_transfer = __esm({
    "node_modules/@tarojs/runtime/dist/dom/transfer.js" () {
        init_element();
        TransferElement = class extends TaroElement {
            get _path() {
                return this.dataName;
            }
            constructor(dataName){
                super();
                this.dataName = dataName;
                this.isTransferElement = true;
            }
        };
    }
});
// node_modules/@tarojs/runtime/dist/dom/document.js
var TaroDocument;
var init_document = __esm({
    "node_modules/@tarojs/runtime/dist/dom/document.js" () {
        init_dist();
        init_constants();
        init_element();
        init_event();
        init_event_source();
        init_form();
        init_root();
        init_text();
        init_env();
        init_anchor_element();
        init_transfer();
        TaroDocument = class extends TaroElement {
            createElement(type) {
                const nodeName = type.toLowerCase();
                let element;
                switch(true){
                    case nodeName === ROOT_STR:
                        element = new TaroRootElement();
                        return element;
                    case controlledComponent.has(nodeName):
                        element = new FormElement();
                        break;
                    case nodeName === A:
                        element = new AnchorElement();
                        break;
                    case nodeName === "page-meta":
                    case nodeName === "navigation-bar":
                        element = new TransferElement(toCamelCase(nodeName));
                        break;
                    default:
                        element = new TaroElement();
                        break;
                }
                element.nodeName = nodeName;
                element.tagName = type.toUpperCase();
                return element;
            }
            // an ugly fake createElementNS to deal with @vue/runtime-dom's
            // support mounting app to svg container since vue@3.0.8
            createElementNS(_svgNS, type) {
                return this.createElement(type);
            }
            createTextNode(text) {
                return new TaroText(text);
            }
            getElementById(id) {
                const el = eventSource.get(id);
                return isUndefined(el) ? null : el;
            }
            querySelector(query) {
                if (/^#/.test(query)) {
                    return this.getElementById(query.slice(1));
                }
                return null;
            }
            querySelectorAll() {
                return [];
            }
            // @TODO: @PERF: 在 hydrate 移除掉空的 node
            createComment() {
                const textnode = new TaroText("");
                textnode.nodeName = COMMENT;
                return textnode;
            }
            get defaultView() {
                return env.window;
            }
            constructor(){
                super();
                this.createEvent = createEvent;
                this.nodeType = 9;
                this.nodeName = DOCUMENT_ELEMENT_NAME;
            }
        };
    }
});
// node_modules/@tarojs/runtime/dist/bom/document.js
function createDocument() {
    const doc = new TaroDocument();
    const documentCreateElement = doc.createElement.bind(doc);
    const html = documentCreateElement(HTML);
    const head = documentCreateElement(HEAD);
    const body = documentCreateElement(BODY);
    const app = documentCreateElement(APP);
    app.id = APP;
    const container = documentCreateElement(CONTAINER);
    doc.appendChild(html);
    html.appendChild(head);
    html.appendChild(body);
    body.appendChild(container);
    container.appendChild(app);
    doc.documentElement = html;
    doc.head = head;
    doc.body = body;
    return doc;
}
var taroDocumentProvider;
var init_document2 = __esm({
    "node_modules/@tarojs/runtime/dist/bom/document.js" () {
        init_constants();
        init_document();
        init_env();
        taroDocumentProvider = false ? env.document : env.document = createDocument();
    }
});
// node_modules/@tarojs/runtime/dist/dom/svg.js
var SVGElement;
var init_svg = __esm({
    "node_modules/@tarojs/runtime/dist/dom/svg.js" () {
        init_element();
        SVGElement = class extends TaroElement {
        };
    }
});
// node_modules/@tarojs/runtime/dist/utils/router.js
var addLeadingSlash, hasBasename, stripBasename, stripTrailing, stripSuffix, getHomePage, getCurrentPage;
var init_router = __esm({
    "node_modules/@tarojs/runtime/dist/utils/router.js" () {
        init_window();
        addLeadingSlash = (url = "")=>url.charAt(0) === "/" ? url : "/" + url;
        hasBasename = (path = "", prefix = "")=>new RegExp("^" + prefix + "(\\/|\\?|#|$)", "i").test(path) || path === prefix;
        stripBasename = (path = "", prefix = "")=>hasBasename(path, prefix) ? path.substring(prefix.length) : path;
        stripTrailing = (str = "")=>str.replace(/[?#][\s\S]*$/, "");
        stripSuffix = (path = "", suffix = "")=>path.includes(suffix) ? path.substring(0, path.length - suffix.length) : path;
        getHomePage = (path = "", basename = "", customRoutes = {}, entryPagePath = "")=>{
            var _a2;
            const routePath = addLeadingSlash(stripBasename(path, basename));
            const alias = ((_a2 = Object.entries(customRoutes).find(([key])=>key === routePath)) === null || _a2 === void 0 ? void 0 : _a2[1]) || routePath;
            return entryPagePath || (typeof alias === "string" ? alias : alias[0]) || basename;
        };
        getCurrentPage = (routerMode = "hash", basename = "/")=>{
            const pagePath = routerMode === "hash" ? taroLocationProvider.hash.slice(1).split("?")[0] : taroLocationProvider.pathname;
            return addLeadingSlash(stripBasename(pagePath, basename));
        };
    }
});
// node_modules/@tarojs/runtime/dist/dsl/common.js
function injectPageInstance(inst, id) {
    hooks.call("mergePageInstance", instances.get(id), inst);
    instances.set(id, inst);
}
function getPageInstance(id) {
    return instances.get(id);
}
function removePageInstance(id) {
    instances.delete(id);
}
function safeExecute(path, lifecycle, ...args) {
    const instance = instances.get(path);
    if (instance == null) {
        return;
    }
    const func = hooks.call("getLifecycle", instance, lifecycle);
    if (isArray(func)) {
        const res = func.map((fn)=>fn.apply(instance, args));
        return res[0];
    }
    if (!isFunction(func)) {
        return;
    }
    return func.apply(instance, args);
}
function stringify(obj) {
    if (obj == null) {
        return "";
    }
    const path = Object.keys(obj).map((key)=>{
        return key + "=" + obj[key];
    }).join("&");
    return path === "" ? path : "?" + path;
}
function getPath(id, options2) {
    const idx = id.indexOf("?");
    if (false) {
        return `${idx > -1 ? id.substring(0, idx) : id}${stringify((options2 === null || options2 === void 0 ? void 0 : options2.stamp) ? {
            stamp: options2.stamp
        } : {})}`;
    } else {
        return `${idx > -1 ? id.substring(0, idx) : id}${stringify(options2)}`;
    }
}
function getOnReadyEventKey(path) {
    return path + "." + ON_READY;
}
function getOnShowEventKey(path) {
    return path + "." + ON_SHOW;
}
function getOnHideEventKey(path) {
    return path + "." + ON_HIDE;
}
function createPageConfig(component, pageName, data, pageConfig) {
    const id = pageName !== null && pageName !== void 0 ? pageName : `taro_page_${pageId()}`;
    const [ONLOAD, ONUNLOAD, ONREADY, ONSHOW, ONHIDE, LIFECYCLES, SIDE_EFFECT_LIFECYCLES] = hooks.call("getMiniLifecycleImpl").page;
    let pageElement = null;
    let unmounting = false;
    let prepareMountList = [];
    function setCurrentRouter(page) {
        const router = false ? page.$taroPath : page.route || page.__route__ || page.$taroPath;
        Current.router = {
            params: page.$taroParams,
            path: addLeadingSlash(router),
            $taroPath: page.$taroPath,
            onReady: getOnReadyEventKey(id),
            onShow: getOnShowEventKey(id),
            onHide: getOnHideEventKey(id)
        };
        if (!isUndefined(page.exitState)) {
            Current.router.exitState = page.exitState;
        }
    }
    let loadResolver;
    let hasLoaded;
    const config = {
        [ONLOAD] (options2 = {}, cb) {
            hasLoaded = new Promise((resolve)=>{
                loadResolver = resolve;
            });
            perf.start(PAGE_INIT);
            Current.page = this;
            this.config = pageConfig || {};
            const uniqueOptions = Object.assign({}, options2, {
                $taroTimestamp: Date.now()
            });
            const $taroPath = this.$taroPath = getPath(id, uniqueOptions);
            if (false) {
                config.path = $taroPath;
            }
            if (this.$taroParams == null) {
                this.$taroParams = uniqueOptions;
            }
            setCurrentRouter(this);
            if (true) {
                taroWindowProvider.trigger(CONTEXT_ACTIONS.INIT, $taroPath);
            }
            const mount = ()=>{
                Current.app.mount(component, $taroPath, ()=>{
                    pageElement = env.document.getElementById($taroPath);
                    ensure(pageElement !== null, "\u6CA1\u6709\u627E\u5230\u9875\u9762\u5B9E\u4F8B\u3002");
                    safeExecute($taroPath, ON_LOAD, this.$taroParams);
                    loadResolver();
                    if (true) {
                        pageElement.ctx = this;
                        pageElement.performUpdate(true, cb);
                    } else {
                        isFunction(cb) && cb();
                    }
                });
            };
            if (unmounting) {
                prepareMountList.push(mount);
            } else {
                mount();
            }
        },
        [ONUNLOAD] () {
            const $taroPath = this.$taroPath;
            if (true) {
                taroWindowProvider.trigger(CONTEXT_ACTIONS.DESTORY, $taroPath);
            }
            safeExecute($taroPath, ONUNLOAD);
            unmounting = true;
            Current.app.unmount($taroPath, ()=>{
                unmounting = false;
                instances.delete($taroPath);
                if (pageElement) {
                    pageElement.ctx = null;
                    pageElement = null;
                }
                if (prepareMountList.length) {
                    prepareMountList.forEach((fn)=>fn());
                    prepareMountList = [];
                }
            });
        },
        [ONREADY] () {
            hasLoaded.then(()=>{
                safeExecute(this.$taroPath, ON_READY);
                _raf(()=>eventCenter.trigger(getOnReadyEventKey(id)));
                this.onReady.called = true;
            });
        },
        [ONSHOW] (options2 = {}) {
            hasLoaded.then(()=>{
                Current.page = this;
                setCurrentRouter(this);
                if (true) {
                    taroWindowProvider.trigger(CONTEXT_ACTIONS.RECOVER, this.$taroPath);
                }
                safeExecute(this.$taroPath, ON_SHOW, options2);
                _raf(()=>eventCenter.trigger(getOnShowEventKey(id)));
            });
        },
        [ONHIDE] () {
            if (true) {
                taroWindowProvider.trigger(CONTEXT_ACTIONS.RESTORE, this.$taroPath);
            }
            if (Current.page === this) {
                Current.page = null;
                Current.router = null;
            }
            safeExecute(this.$taroPath, ON_HIDE);
            eventCenter.trigger(getOnHideEventKey(id));
        }
    };
    if (false) {
        config.getOpenerEventChannel = ()=>{
            return EventChannel.pageChannel;
        };
    }
    LIFECYCLES.forEach((lifecycle)=>{
        let isDefer = false;
        lifecycle = lifecycle.replace(/^defer:/, ()=>{
            isDefer = true;
            return "";
        });
        config[lifecycle] = function() {
            const exec = ()=>safeExecute(this.$taroPath, lifecycle, ...arguments);
            if (isDefer) {
                hasLoaded.then(exec);
            } else {
                return exec();
            }
        };
    });
    SIDE_EFFECT_LIFECYCLES.forEach((lifecycle)=>{
        var _a2;
        if (component[lifecycle] || ((_a2 = component.prototype) === null || _a2 === void 0 ? void 0 : _a2[lifecycle]) || component[lifecycle.replace(/^on/, "enable")] || (pageConfig === null || pageConfig === void 0 ? void 0 : pageConfig[lifecycle.replace(/^on/, "enable")])) {
            config[lifecycle] = function(...args) {
                var _a3;
                const target = (_a3 = args[0]) === null || _a3 === void 0 ? void 0 : _a3.target;
                if (target === null || target === void 0 ? void 0 : target.id) {
                    const id2 = target.id;
                    const element = env.document.getElementById(id2);
                    if (element) {
                        target.dataset = element.dataset;
                    }
                }
                return safeExecute(this.$taroPath, lifecycle, ...args);
            };
        }
    });
    config.eh = eventHandler;
    if (!isUndefined(data)) {
        config.data = data;
    }
    hooks.call("modifyPageObject", config);
    return config;
}
function createComponentConfig(component, componentName, data) {
    const id = componentName !== null && componentName !== void 0 ? componentName : `taro_component_${pageId()}`;
    let componentElement = null;
    const [ATTACHED, DETACHED] = hooks.call("getMiniLifecycleImpl").component;
    const config = {
        [ATTACHED] () {
            var _a2;
            perf.start(PAGE_INIT);
            this.pageIdCache = ((_a2 = this.getPageId) === null || _a2 === void 0 ? void 0 : _a2.call(this)) || pageId();
            const path = getPath(id, {
                id: this.pageIdCache
            });
            Current.app.mount(component, path, ()=>{
                componentElement = env.document.getElementById(path);
                ensure(componentElement !== null, "\u6CA1\u6709\u627E\u5230\u7EC4\u4EF6\u5B9E\u4F8B\u3002");
                this.$taroInstances = instances.get(path);
                safeExecute(path, ON_LOAD);
                if (true) {
                    componentElement.ctx = this;
                    componentElement.performUpdate(true);
                }
            });
        },
        [DETACHED] () {
            const path = getPath(id, {
                id: this.pageIdCache
            });
            Current.app.unmount(path, ()=>{
                instances.delete(path);
                if (componentElement) {
                    componentElement.ctx = null;
                }
            });
        },
        methods: {
            eh: eventHandler
        }
    };
    if (!isUndefined(data)) {
        config.data = data;
    }
    [
        OPTIONS,
        EXTERNAL_CLASSES,
        BEHAVIORS
    ].forEach((key)=>{
        var _a2;
        config[key] = (_a2 = component[key]) !== null && _a2 !== void 0 ? _a2 : EMPTY_OBJ;
    });
    return config;
}
function createRecursiveComponentConfig(componentName) {
    const isCustomWrapper = componentName === CUSTOM_WRAPPER;
    const [ATTACHED, DETACHED] = hooks.call("getMiniLifecycleImpl").component;
    const lifeCycles = isCustomWrapper ? {
        [ATTACHED] () {
            var _a2, _b;
            const componentId = ((_a2 = this.data.i) === null || _a2 === void 0 ? void 0 : _a2.sid) || ((_b = this.props.i) === null || _b === void 0 ? void 0 : _b.sid);
            if (isString(componentId)) {
                customWrapperCache.set(componentId, this);
                const el = env.document.getElementById(componentId);
                if (el) {
                    el.ctx = this;
                }
            }
        },
        [DETACHED] () {
            var _a2, _b;
            const componentId = ((_a2 = this.data.i) === null || _a2 === void 0 ? void 0 : _a2.sid) || ((_b = this.props.i) === null || _b === void 0 ? void 0 : _b.sid);
            if (isString(componentId)) {
                customWrapperCache.delete(componentId);
                const el = env.document.getElementById(componentId);
                if (el) {
                    el.ctx = null;
                }
            }
        }
    } : EMPTY_OBJ;
    const extraOptions = {};
    if (false) {
        extraOptions.addGlobalClass = true;
    }
    return hooks.call("modifyRecursiveComponentConfig", Object.assign({
        properties: {
            i: {
                type: Object,
                value: {
                    ["nn"]: getComponentsAlias(internalComponents)[VIEW]._num
                }
            },
            l: {
                type: String,
                value: ""
            }
        },
        options: Object.assign(Object.assign({}, extraOptions), {
            virtualHost: !isCustomWrapper
        }),
        methods: {
            eh: eventHandler
        }
    }, lifeCycles), {
        isCustomWrapper
    });
}
var instances, pageId;
var init_common = __esm({
    "node_modules/@tarojs/runtime/dist/dsl/common.js" () {
        init_dist();
        init_raf();
        init_window();
        init_constants();
        init_current();
        init_event();
        init_emitter();
        init_env();
        init_perf();
        init_utils();
        init_router();
        instances = /* @__PURE__ */ new Map();
        pageId = incrementId();
    }
});
// node_modules/@tarojs/runtime/dist/next-tick.js
var TIMEOUT, nextTick;
var init_next_tick = __esm({
    "node_modules/@tarojs/runtime/dist/next-tick.js" () {
        init_current();
        init_env();
        TIMEOUT = 100;
        nextTick = (cb, ctx)=>{
            const beginTime = Date.now();
            const router = Current.router;
            const timerFunc = ()=>{
                setTimeout(function() {
                    ctx ? cb.call(ctx) : cb();
                }, 1);
            };
            if (router === null) return timerFunc();
            const path = router.$taroPath;
            function next() {
                var _a2, _b, _c;
                const pageElement = env.document.getElementById(path);
                if (pageElement === null || pageElement === void 0 ? void 0 : pageElement.pendingUpdate) {
                    if (false) {
                        (_c = (_b = (_a2 = pageElement.firstChild) === null || _a2 === void 0 ? void 0 : _a2["componentOnReady"]) === null || _b === void 0 ? void 0 : _b.call(_a2).then(()=>{
                            timerFunc();
                        })) !== null && _c !== void 0 ? _c : timerFunc();
                    } else {
                        pageElement.enqueueUpdateCallback(cb, ctx);
                    }
                } else if (Date.now() - beginTime > TIMEOUT) {
                    timerFunc();
                } else {
                    setTimeout(()=>next(), 20);
                }
            }
            next();
        };
    }
});
// node_modules/@tarojs/runtime/dist/polyfill/array.js
var init_array = __esm({
    "node_modules/@tarojs/runtime/dist/polyfill/array.js" () {
        init_dist();
    }
});
// node_modules/@tarojs/runtime/dist/polyfill/intersection-observer.js
var init_intersection_observer = __esm({
    "node_modules/@tarojs/runtime/dist/polyfill/intersection-observer.js" () {
        init_dist();
        init_constants();
        init_lodash();
        init_window();
    }
});
// node_modules/@tarojs/runtime/dist/polyfill/object.js
var init_object = __esm({
    "node_modules/@tarojs/runtime/dist/polyfill/object.js" () {
        init_dist();
    }
});
// node_modules/@tarojs/runtime/dist/polyfill/index.js
function handlePolyfill() {
    if (false) {
        handleObjectAssignPolyfill();
    }
    if (false) {
        handleObjectEntriesPolyfill();
    }
    if (false) {
        handleObjectDefinePropertyPolyfill();
    }
    if (false) {
        handleArrayFindPolyfill();
    }
    if (false) {
        handleArrayIncludesPolyfill();
    }
    if (false) {
        if (false) {
            handleIntersectionObserverPolyfill();
        }
    }
}
var init_polyfill = __esm({
    "node_modules/@tarojs/runtime/dist/polyfill/index.js" () {
        init_dist();
        init_array();
        init_intersection_observer();
        init_object();
        if (false) {
            handlePolyfill();
        }
    }
});
// node_modules/@tarojs/runtime/dist/dom-external/inner-html/scaner.js
function initPosition() {
    return {
        index: 0,
        column: 0,
        line: 0
    };
}
function feedPosition(position, str, len) {
    const start = position.index;
    const end = position.index = start + len;
    for(let i = start; i < end; i++){
        const char = str.charAt(i);
        if (char === "\n") {
            position.line++;
            position.column = 0;
        } else {
            position.column++;
        }
    }
}
function jumpPosition(position, str, end) {
    const len = end - position.index;
    return feedPosition(position, str, len);
}
function copyPosition(position) {
    return {
        index: position.index,
        line: position.line,
        column: position.column
    };
}
function isWhitespaceChar(char) {
    return whitespace.test(char);
}
function isEqualSignChar(char) {
    return equalSign.test(char);
}
function shouldBeIgnore(tagName) {
    const name = tagName.toLowerCase();
    if (options.html.skipElements.has(name)) {
        return true;
    }
    return false;
}
function findTextEnd(str, index) {
    while(true){
        const textEnd = str.indexOf("<", index);
        if (textEnd === -1) {
            return textEnd;
        }
        const char = str.charAt(textEnd + 1);
        if (char === "/" || char === "!" || alphanumeric.test(char)) {
            return textEnd;
        }
        index = textEnd + 1;
    }
}
function isWordEnd(cursor, wordBegin, html) {
    if (!isWhitespaceChar(html.charAt(cursor))) return false;
    const len = html.length;
    for(let i = cursor - 1; i > wordBegin; i--){
        const char = html.charAt(i);
        if (!isWhitespaceChar(char)) {
            if (isEqualSignChar(char)) return false;
            break;
        }
    }
    for(let i = cursor + 1; i < len; i++){
        const char = html.charAt(i);
        if (!isWhitespaceChar(char)) {
            if (isEqualSignChar(char)) return false;
            return true;
        }
    }
}
var whitespace, equalSign, alphanumeric, Scaner;
var init_scaner = __esm({
    "node_modules/@tarojs/runtime/dist/dom-external/inner-html/scaner.js" () {
        init_options();
        whitespace = /\s/;
        equalSign = /=/;
        alphanumeric = /[A-Za-z0-9]/;
        Scaner = class {
            scan() {
                const { html, position } = this;
                const len = html.length;
                while(position.index < len){
                    const start = position.index;
                    this.scanText();
                    if (position.index === start) {
                        const isComment2 = html.startsWith("!--", start + 1);
                        if (isComment2) {
                            this.scanComment();
                        } else {
                            const tagName = this.scanTag();
                            if (shouldBeIgnore(tagName)) {
                                this.scanSkipTag(tagName);
                            }
                        }
                    }
                }
                return this.tokens;
            }
            scanText() {
                const type = "text";
                const { html, position } = this;
                let textEnd = findTextEnd(html, position.index);
                if (textEnd === position.index) {
                    return;
                }
                if (textEnd === -1) {
                    textEnd = html.length;
                }
                const start = copyPosition(position);
                const content = html.slice(position.index, textEnd);
                jumpPosition(position, html, textEnd);
                const end = copyPosition(position);
                this.tokens.push({
                    type,
                    content,
                    position: {
                        start,
                        end
                    }
                });
            }
            scanComment() {
                const type = "comment";
                const { html, position } = this;
                const start = copyPosition(position);
                feedPosition(position, html, 4);
                let contentEnd = html.indexOf("-->", position.index);
                let commentEnd = contentEnd + 3;
                if (contentEnd === -1) {
                    contentEnd = commentEnd = html.length;
                }
                const content = html.slice(position.index, contentEnd);
                jumpPosition(position, html, commentEnd);
                this.tokens.push({
                    type,
                    content,
                    position: {
                        start,
                        end: copyPosition(position)
                    }
                });
            }
            scanTag() {
                this.scanTagStart();
                const tagName = this.scanTagName();
                this.scanAttrs();
                this.scanTagEnd();
                return tagName;
            }
            scanTagStart() {
                const type = "tag-start";
                const { html, position } = this;
                const secondChar = html.charAt(position.index + 1);
                const close = secondChar === "/";
                const start = copyPosition(position);
                feedPosition(position, html, close ? 2 : 1);
                this.tokens.push({
                    type,
                    close,
                    position: {
                        start
                    }
                });
            }
            scanTagEnd() {
                const type = "tag-end";
                const { html, position } = this;
                const firstChar = html.charAt(position.index);
                const close = firstChar === "/";
                feedPosition(position, html, close ? 2 : 1);
                const end = copyPosition(position);
                this.tokens.push({
                    type,
                    close,
                    position: {
                        end
                    }
                });
            }
            scanTagName() {
                const type = "tag";
                const { html, position } = this;
                const len = html.length;
                let start = position.index;
                while(start < len){
                    const char = html.charAt(start);
                    const isTagChar = !(isWhitespaceChar(char) || char === "/" || char === ">");
                    if (isTagChar) break;
                    start++;
                }
                let end = start + 1;
                while(end < len){
                    const char = html.charAt(end);
                    const isTagChar = !(isWhitespaceChar(char) || char === "/" || char === ">");
                    if (!isTagChar) break;
                    end++;
                }
                jumpPosition(position, html, end);
                const tagName = html.slice(start, end);
                this.tokens.push({
                    type,
                    content: tagName
                });
                return tagName;
            }
            scanAttrs() {
                const { html, position, tokens } = this;
                let cursor = position.index;
                let quote = null;
                let wordBegin = cursor;
                const words = [];
                const len = html.length;
                while(cursor < len){
                    const char = html.charAt(cursor);
                    if (quote) {
                        const isQuoteEnd = char === quote;
                        if (isQuoteEnd) {
                            quote = null;
                        }
                        cursor++;
                        continue;
                    }
                    const isTagEnd = char === "/" || char === ">";
                    if (isTagEnd) {
                        if (cursor !== wordBegin) {
                            words.push(html.slice(wordBegin, cursor));
                        }
                        break;
                    }
                    if (isWordEnd(cursor, wordBegin, html)) {
                        if (cursor !== wordBegin) {
                            words.push(html.slice(wordBegin, cursor));
                        }
                        wordBegin = cursor + 1;
                        cursor++;
                        continue;
                    }
                    const isQuoteStart = char === "'" || char === '"';
                    if (isQuoteStart) {
                        quote = char;
                        cursor++;
                        continue;
                    }
                    cursor++;
                }
                jumpPosition(position, html, cursor);
                const wLen = words.length;
                const type = "attribute";
                for(let i = 0; i < wLen; i++){
                    const word = words[i];
                    const isNotPair = word.includes("=");
                    if (isNotPair) {
                        const secondWord = words[i + 1];
                        if (secondWord && secondWord.startsWith("=")) {
                            if (secondWord.length > 1) {
                                const newWord = word + secondWord;
                                tokens.push({
                                    type,
                                    content: newWord
                                });
                                i += 1;
                                continue;
                            }
                            const thirdWord = words[i + 2];
                            i += 1;
                            if (thirdWord) {
                                const newWord = word + "=" + thirdWord;
                                tokens.push({
                                    type,
                                    content: newWord
                                });
                                i += 1;
                                continue;
                            }
                        }
                    }
                    if (word.endsWith("=")) {
                        const secondWord = words[i + 1];
                        if (secondWord && !secondWord.includes("=")) {
                            const newWord2 = word + secondWord;
                            tokens.push({
                                type,
                                content: newWord2
                            });
                            i += 1;
                            continue;
                        }
                        const newWord = word.slice(0, -1);
                        tokens.push({
                            type,
                            content: newWord
                        });
                        continue;
                    }
                    tokens.push({
                        type,
                        content: word
                    });
                }
            }
            scanSkipTag(tagName) {
                const { html, position } = this;
                const safeTagName = tagName.toLowerCase();
                const len = html.length;
                while(position.index < len){
                    const nextTag = html.indexOf("</", position.index);
                    if (nextTag === -1) {
                        this.scanText();
                        break;
                    }
                    jumpPosition(position, html, nextTag);
                    const name = this.scanTag();
                    if (safeTagName === name.toLowerCase()) {
                        break;
                    }
                }
            }
            constructor(html){
                this.tokens = [];
                this.position = initPosition();
                this.html = html;
            }
        };
    }
});
// node_modules/@tarojs/runtime/dist/dom-external/inner-html/utils.js
function unquote(str) {
    const car = str.charAt(0);
    const end = str.length - 1;
    const isQuoteStart = car === '"' || car === "'";
    if (isQuoteStart && car === str.charAt(end)) {
        return str.slice(1, end);
    }
    return str;
}
var init_utils2 = __esm({
    "node_modules/@tarojs/runtime/dist/dom-external/inner-html/utils.js" () {}
});
// node_modules/@tarojs/runtime/dist/dom-external/inner-html/style.js
function getPreviousElement(el) {
    const parent = el.parentElement;
    if (!parent) return null;
    const prev = el.previousSibling;
    if (!prev) return null;
    if (prev.nodeType === 1) {
        return prev;
    } else {
        return getPreviousElement(prev);
    }
}
function sortStyles(styles) {
    return styles.sort((s1, s2)=>{
        const hundreds1 = getHundredsWeight(s1.selectorList);
        const hundreds2 = getHundredsWeight(s2.selectorList);
        if (hundreds1 !== hundreds2) return hundreds1 - hundreds2;
        const tens1 = getTensWeight(s1.selectorList);
        const tens2 = getTensWeight(s2.selectorList);
        if (tens1 !== tens2) return tens1 - tens2;
        const ones1 = getOnesWeight(s1.selectorList);
        const ones2 = getOnesWeight(s2.selectorList);
        return ones1 - ones2;
    });
}
function getHundredsWeight(selectors) {
    return selectors.reduce((pre, cur)=>pre + (cur.id ? 1 : 0), 0);
}
function getTensWeight(selectors) {
    return selectors.reduce((pre, cur)=>pre + cur.class.length + cur.attrs.length, 0);
}
function getOnesWeight(selectors) {
    return selectors.reduce((pre, cur)=>pre + (cur.tag ? 1 : 0), 0);
}
var LEFT_BRACKET, RIGHT_BRACKET, CLASS_SELECTOR, ID_SELECTOR, CHILD_COMBINATOR, GENERAL_SIBLING_COMBINATOR, ADJACENT_SIBLING_COMBINATOR, StyleTagParser;
var init_style2 = __esm({
    "node_modules/@tarojs/runtime/dist/dom-external/inner-html/style.js" () {
        init_utils2();
        LEFT_BRACKET = "{";
        RIGHT_BRACKET = "}";
        CLASS_SELECTOR = ".";
        ID_SELECTOR = "#";
        CHILD_COMBINATOR = ">";
        GENERAL_SIBLING_COMBINATOR = "~";
        ADJACENT_SIBLING_COMBINATOR = "+";
        StyleTagParser = class {
            extractStyle(src) {
                const REG_STYLE = /<style\s?[^>]*>((.|\n|\s)+?)<\/style>/g;
                let html = src;
                html = html.replace(REG_STYLE, (_, $1)=>{
                    const style2 = $1.trim();
                    this.stringToSelector(style2);
                    return "";
                });
                return html.trim();
            }
            stringToSelector(style2) {
                let lb = style2.indexOf(LEFT_BRACKET);
                while(lb > -1){
                    const rb = style2.indexOf(RIGHT_BRACKET);
                    const selectors = style2.slice(0, lb).trim();
                    let content = style2.slice(lb + 1, rb);
                    content = content.replace(/:(.*);/g, function(_, $1) {
                        const t = $1.trim().replace(/ +/g, "+++");
                        return `:${t};`;
                    });
                    content = content.replace(/ /g, "");
                    content = content.replace(/\+\+\+/g, " ");
                    if (!/;$/.test(content)) {
                        content += ";";
                    }
                    selectors.split(",").forEach((src)=>{
                        const selectorList = this.parseSelector(src);
                        this.styles.push({
                            content,
                            selectorList
                        });
                    });
                    style2 = style2.slice(rb + 1);
                    lb = style2.indexOf(LEFT_BRACKET);
                }
            }
            parseSelector(src) {
                const list = src.trim().replace(/ *([>~+]) */g, " $1").replace(/ +/g, " ").replace(/\[\s*([^[\]=\s]+)\s*=\s*([^[\]=\s]+)\s*\]/g, "[$1=$2]").split(" ");
                const selectors = list.map((item)=>{
                    const firstChar = item.charAt(0);
                    const selector = {
                        isChild: firstChar === CHILD_COMBINATOR,
                        isGeneralSibling: firstChar === GENERAL_SIBLING_COMBINATOR,
                        isAdjacentSibling: firstChar === ADJACENT_SIBLING_COMBINATOR,
                        tag: null,
                        id: null,
                        class: [],
                        attrs: []
                    };
                    item = item.replace(/^[>~+]/, "");
                    item = item.replace(/\[(.+?)\]/g, function(_, $1) {
                        const [key, value] = $1.split("=");
                        const all = $1.indexOf("=") === -1;
                        const attr = {
                            all,
                            key,
                            value: all ? null : value
                        };
                        selector.attrs.push(attr);
                        return "";
                    });
                    item = item.replace(/([.#][A-Za-z0-9-_]+)/g, function(_, $1) {
                        if ($1[0] === ID_SELECTOR) {
                            selector.id = $1.substr(1);
                        } else if ($1[0] === CLASS_SELECTOR) {
                            selector.class.push($1.substr(1));
                        }
                        return "";
                    });
                    if (item !== "") {
                        selector.tag = item;
                    }
                    return selector;
                });
                return selectors;
            }
            matchStyle(tagName, el, list) {
                const res = sortStyles(this.styles).reduce((str, { content, selectorList }, i)=>{
                    let idx = list[i];
                    let selector = selectorList[idx];
                    const nextSelector = selectorList[idx + 1];
                    if ((nextSelector === null || nextSelector === void 0 ? void 0 : nextSelector.isGeneralSibling) || (nextSelector === null || nextSelector === void 0 ? void 0 : nextSelector.isAdjacentSibling)) {
                        selector = nextSelector;
                        idx += 1;
                        list[i] += 1;
                    }
                    let isMatch = this.matchCurrent(tagName, el, selector);
                    if (isMatch && selector.isGeneralSibling) {
                        let prev = getPreviousElement(el);
                        while(prev){
                            if (prev.h5tagName && this.matchCurrent(prev.h5tagName, prev, selectorList[idx - 1])) {
                                isMatch = true;
                                break;
                            }
                            prev = getPreviousElement(prev);
                            isMatch = false;
                        }
                    }
                    if (isMatch && selector.isAdjacentSibling) {
                        const prev = getPreviousElement(el);
                        if (!prev || !prev.h5tagName) {
                            isMatch = false;
                        } else {
                            const isSiblingMatch = this.matchCurrent(prev.h5tagName, prev, selectorList[idx - 1]);
                            if (!isSiblingMatch) {
                                isMatch = false;
                            }
                        }
                    }
                    if (isMatch) {
                        if (idx === selectorList.length - 1) {
                            return str + content;
                        } else if (idx < selectorList.length - 1) {
                            list[i] += 1;
                        }
                    } else {
                        if (selector.isChild && idx > 0) {
                            list[i] -= 1;
                            if (this.matchCurrent(tagName, el, selectorList[list[i]])) {
                                list[i] += 1;
                            }
                        }
                    }
                    return str;
                }, "");
                return res;
            }
            matchCurrent(tagName, el, selector) {
                if (selector.tag && selector.tag !== tagName) return false;
                if (selector.id && selector.id !== el.id) return false;
                if (selector.class.length) {
                    const classList = el.className.split(" ");
                    for(let i = 0; i < selector.class.length; i++){
                        const cls = selector.class[i];
                        if (classList.indexOf(cls) === -1) {
                            return false;
                        }
                    }
                }
                if (selector.attrs.length) {
                    for(let i = 0; i < selector.attrs.length; i++){
                        const { all, key, value } = selector.attrs[i];
                        if (all && !el.hasAttribute(key)) {
                            return false;
                        } else {
                            const attr = el.getAttribute(key);
                            if (attr !== unquote(value || "")) {
                                return false;
                            }
                        }
                    }
                }
                return true;
            }
            constructor(){
                this.styles = [];
            }
        };
    }
});
// node_modules/@tarojs/runtime/dist/dom-external/inner-html/tags.js
function makeMap(str, expectsLowerCase) {
    const map = /* @__PURE__ */ Object.create(null);
    const list = str.split(",");
    for(let i = 0; i < list.length; i++){
        map[list[i]] = true;
    }
    return (val)=>!!map[val.toLowerCase()];
}
var specialMiniElements, internalCompsList, isMiniElements, isInlineElements, isBlockElements;
var init_tags = __esm({
    "node_modules/@tarojs/runtime/dist/dom-external/inner-html/tags.js" () {
        init_dist();
        specialMiniElements = {
            img: "image",
            iframe: "web-view"
        };
        internalCompsList = Object.keys(internalComponents).map((i)=>i.toLowerCase()).join(",");
        isMiniElements = makeMap(internalCompsList);
        isInlineElements = makeMap("a,i,abbr,iframe,select,acronym,slot,small,span,bdi,kbd,strong,big,map,sub,sup,br,mark,mark,meter,template,canvas,textarea,cite,object,time,code,output,u,data,picture,tt,datalist,var,dfn,del,q,em,s,embed,samp,b");
        isBlockElements = makeMap("address,fieldset,li,article,figcaption,main,aside,figure,nav,blockquote,footer,ol,details,form,p,dialog,h1,h2,h3,h4,h5,h6,pre,dd,header,section,div,hgroup,table,dl,hr,ul,dt");
    }
});
// node_modules/@tarojs/runtime/dist/dom-external/inner-html/parser.js
function hasTerminalParent(tagName, stack) {
    const tagParents = closingTagAncestorBreakers[tagName];
    if (tagParents) {
        let currentIndex = stack.length - 1;
        while(currentIndex >= 0){
            const parentTagName = stack[currentIndex].tagName;
            if (parentTagName === tagName) {
                break;
            }
            if (tagParents && tagParents.includes(parentTagName)) {
                return true;
            }
            currentIndex--;
        }
    }
    return false;
}
function getTagName(tag) {
    if (options.html.renderHTMLTag) {
        return tag;
    }
    if (specialMiniElements[tag]) {
        return specialMiniElements[tag];
    } else if (isMiniElements(tag)) {
        return tag;
    } else if (isBlockElements(tag)) {
        return "view";
    } else if (isInlineElements(tag)) {
        return "text";
    }
    return "view";
}
function splitEqual(str) {
    const sep = "=";
    const idx = str.indexOf(sep);
    if (idx === -1) return [
        str
    ];
    const key = str.slice(0, idx).trim();
    const value = str.slice(idx + sep.length).trim();
    return [
        key,
        value
    ];
}
function format(children, document2, styleOptions, parent) {
    return children.filter((child)=>{
        if (child.type === "comment") {
            return false;
        } else if (child.type === "text") {
            return child.content !== "";
        }
        return true;
    }).map((child)=>{
        if (child.type === "text") {
            let text = document2.createTextNode(child.content);
            if (isFunction(options.html.transformText)) {
                text = options.html.transformText(text, child);
            }
            parent === null || parent === void 0 ? void 0 : parent.appendChild(text);
            return text;
        }
        const el = document2.createElement(getTagName(child.tagName));
        el.h5tagName = child.tagName;
        parent === null || parent === void 0 ? void 0 : parent.appendChild(el);
        if (!options.html.renderHTMLTag) {
            el.className = `h5-${child.tagName}`;
        }
        for(let i = 0; i < child.attributes.length; i++){
            const attr = child.attributes[i];
            const [key, value] = splitEqual(attr);
            if (key === "class") {
                el.className += " " + unquote(value);
            } else if (key[0] === "o" && key[1] === "n") {
                continue;
            } else {
                el.setAttribute(key, value == null ? true : unquote(value));
            }
        }
        const { styleTagParser, descendantList } = styleOptions;
        const list = descendantList.slice();
        const style2 = styleTagParser.matchStyle(child.tagName, el, list);
        el.setAttribute("style", style2 + el.style.cssText);
        format(child.children, document2, {
            styleTagParser,
            descendantList: list
        }, el);
        if (isFunction(options.html.transformElement)) {
            return options.html.transformElement(el, child);
        }
        return el;
    });
}
function parser(html, document2) {
    const styleTagParser = new StyleTagParser();
    html = styleTagParser.extractStyle(html);
    const tokens = new Scaner(html).scan();
    const root = {
        tagName: "",
        children: [],
        type: "element",
        attributes: []
    };
    const state = {
        tokens,
        cursor: 0,
        stack: [
            root
        ]
    };
    parse(state);
    return format(root.children, document2, {
        styleTagParser,
        descendantList: Array(styleTagParser.styles.length).fill(0)
    });
}
function parse(state) {
    const { tokens, stack } = state;
    let { cursor } = state;
    const len = tokens.length;
    let nodes = stack[stack.length - 1].children;
    while(cursor < len){
        const token = tokens[cursor];
        if (token.type !== "tag-start") {
            nodes.push(token);
            cursor++;
            continue;
        }
        const tagToken = tokens[++cursor];
        cursor++;
        const tagName = tagToken.content.toLowerCase();
        if (token.close) {
            let index = stack.length;
            let shouldRewind = false;
            while(--index > -1){
                if (stack[index].tagName === tagName) {
                    shouldRewind = true;
                    break;
                }
            }
            while(cursor < len){
                const endToken = tokens[cursor];
                if (endToken.type !== "tag-end") break;
                cursor++;
            }
            if (shouldRewind) {
                stack.splice(index);
                break;
            } else {
                continue;
            }
        }
        const isClosingTag = options.html.closingElements.has(tagName);
        let shouldRewindToAutoClose = isClosingTag;
        if (shouldRewindToAutoClose) {
            shouldRewindToAutoClose = !hasTerminalParent(tagName, stack);
        }
        if (shouldRewindToAutoClose) {
            let currentIndex = stack.length - 1;
            while(currentIndex > 0){
                if (tagName === stack[currentIndex].tagName) {
                    stack.splice(currentIndex);
                    const previousIndex = currentIndex - 1;
                    nodes = stack[previousIndex].children;
                    break;
                }
                currentIndex = currentIndex - 1;
            }
        }
        const attributes = [];
        let attrToken;
        while(cursor < len){
            attrToken = tokens[cursor];
            if (attrToken.type === "tag-end") break;
            attributes.push(attrToken.content);
            cursor++;
        }
        cursor++;
        const children = [];
        const element = {
            type: "element",
            tagName: tagToken.content,
            attributes,
            children
        };
        nodes.push(element);
        const hasChildren = !(attrToken.close || options.html.voidElements.has(tagName));
        if (hasChildren) {
            stack.push({
                tagName,
                children
            });
            const innerState = {
                tokens,
                cursor,
                stack
            };
            parse(innerState);
            cursor = innerState.cursor;
        }
    }
    state.cursor = cursor;
}
var closingTagAncestorBreakers;
var init_parser = __esm({
    "node_modules/@tarojs/runtime/dist/dom-external/inner-html/parser.js" () {
        init_dist();
        init_options();
        init_scaner();
        init_style2();
        init_tags();
        init_utils2();
        closingTagAncestorBreakers = {
            li: [
                "ul",
                "ol",
                "menu"
            ],
            dt: [
                "dl"
            ],
            dd: [
                "dl"
            ],
            tbody: [
                "table"
            ],
            thead: [
                "table"
            ],
            tfoot: [
                "table"
            ],
            tr: [
                "table"
            ],
            td: [
                "table"
            ]
        };
    }
});
// node_modules/@tarojs/runtime/dist/dom-external/inner-html/html.js
function setInnerHTML(element, html) {
    while(element.firstChild){
        element.removeChild(element.firstChild);
    }
    const children = parser(html, element.ownerDocument);
    for(let i = 0; i < children.length; i++){
        element.appendChild(children[i]);
    }
}
var init_html = __esm({
    "node_modules/@tarojs/runtime/dist/dom-external/inner-html/html.js" () {
        init_options();
        init_parser();
        options.html = {
            skipElements: /* @__PURE__ */ new Set([
                "style",
                "script"
            ]),
            voidElements: /* @__PURE__ */ new Set([
                "!doctype",
                "area",
                "base",
                "br",
                "col",
                "command",
                "embed",
                "hr",
                "img",
                "input",
                "keygen",
                "link",
                "meta",
                "param",
                "source",
                "track",
                "wbr"
            ]),
            closingElements: /* @__PURE__ */ new Set([
                "html",
                "head",
                "body",
                "p",
                "dt",
                "dd",
                "li",
                "option",
                "thead",
                "th",
                "tbody",
                "tr",
                "td",
                "tfoot",
                "colgroup"
            ]),
            renderHTMLTag: false
        };
    }
});
// node_modules/@tarojs/runtime/dist/dom-external/element.js
function getBoundingClientRectImpl() {
    if (!options.miniGlobal) return Promise.resolve(null);
    return new Promise((resolve)=>{
        const query = options.miniGlobal.createSelectorQuery();
        if (false) {
            query.select(`#${this.uid}`).boundingClientRect().exec((res)=>{
                resolve(res);
            });
            return;
        }
        query.select(`#${this.uid}`).boundingClientRect((res)=>{
            resolve(res);
        }).exec();
    });
}
function getTemplateContent(ctx) {
    if (ctx.nodeName === "template") {
        const document2 = ctx.ownerDocument;
        const content = document2.createElement(DOCUMENT_FRAGMENT);
        content.childNodes = ctx.childNodes;
        ctx.childNodes = [
            content
        ];
        content.parentNode = ctx;
        content.childNodes.forEach((nodes)=>{
            nodes.parentNode = content;
        });
        return content;
    }
}
var init_element2 = __esm({
    "node_modules/@tarojs/runtime/dist/dom-external/element.js" () {
        init_constants();
        init_options();
    }
});
// node_modules/@tarojs/runtime/dist/dom-external/node.js
function insertAdjacentHTML(position, html) {
    var _a2, _b;
    const parsedNodes = parser(html, this.ownerDocument);
    for(let i = 0; i < parsedNodes.length; i++){
        const n = parsedNodes[i];
        switch(position){
            case "beforebegin":
                (_a2 = this.parentNode) === null || _a2 === void 0 ? void 0 : _a2.insertBefore(n, this);
                break;
            case "afterbegin":
                if (this.hasChildNodes()) {
                    this.insertBefore(n, this.childNodes[0]);
                } else {
                    this.appendChild(n);
                }
                break;
            case "beforeend":
                this.appendChild(n);
                break;
            case "afterend":
                (_b = this.parentNode) === null || _b === void 0 ? void 0 : _b.appendChild(n);
                break;
        }
    }
}
function cloneNode(isDeep = false) {
    const document2 = this.ownerDocument;
    let newNode;
    if (this.nodeType === 1) {
        newNode = document2.createElement(this.nodeName);
    } else if (this.nodeType === 3) {
        newNode = document2.createTextNode("");
    }
    for(const key in this){
        const value = this[key];
        if ([
            PROPS,
            DATASET
        ].includes(key) && typeof value === OBJECT) {
            newNode[key] = Object.assign({}, value);
        } else if (key === "_value") {
            newNode[key] = value;
        } else if (key === STYLE) {
            newNode.style._value = Object.assign({}, value._value);
            newNode.style._usedStyleProp = new Set(Array.from(value._usedStyleProp));
        }
    }
    if (isDeep) {
        newNode.childNodes = this.childNodes.map((node)=>node.cloneNode(true));
    }
    return newNode;
}
function contains(node) {
    let isContains = false;
    this.childNodes.some((childNode)=>{
        const { uid } = childNode;
        if (uid === node.uid || uid === node.id || childNode.contains(node)) {
            isContains = true;
            return true;
        }
    });
    return isContains;
}
var init_node2 = __esm({
    "node_modules/@tarojs/runtime/dist/dom-external/node.js" () {
        init_constants();
        init_parser();
    }
});
// node_modules/@tarojs/runtime/dist/dom-external/index.js
var init_dom_external = __esm({
    "node_modules/@tarojs/runtime/dist/dom-external/index.js" () {
        init_dist();
        init_element();
        init_node();
        init_html();
        init_element2();
        init_node2();
        if ("mini" !== PLATFORM_TYPE.WEB && "mini" !== PLATFORM_TYPE.HARMONY) {
            if (ENABLE_INNER_HTML) {
                TaroNode.extend("innerHTML", {
                    set (html) {
                        setInnerHTML.call(this, this, html);
                    },
                    get () {
                        return "";
                    }
                });
                if (ENABLE_ADJACENT_HTML) {
                    TaroNode.extend("insertAdjacentHTML", insertAdjacentHTML);
                }
            }
            if (ENABLE_CLONE_NODE) {
                TaroNode.extend("cloneNode", cloneNode);
            }
            if (ENABLE_CONTAINS) {
                TaroNode.extend("contains", contains);
            }
            if (ENABLE_SIZE_APIS) {
                TaroElement.extend("getBoundingClientRect", getBoundingClientRectImpl);
            }
            if (ENABLE_TEMPLATE_CONTENT) {
                TaroElement.extend("content", {
                    get () {
                        return getTemplateContent(this);
                    }
                });
            }
        }
    }
});
// node_modules/@tarojs/runtime/dist/index.js
var dist_exports = {};
__export(dist_exports, {
    A: ()=>A,
    APP: ()=>APP,
    BEHAVIORS: ()=>BEHAVIORS,
    BODY: ()=>BODY,
    CATCHMOVE: ()=>CATCHMOVE,
    CATCH_VIEW: ()=>CATCH_VIEW,
    CHANGE: ()=>CHANGE,
    CLASS: ()=>CLASS,
    CLICK_VIEW: ()=>CLICK_VIEW,
    COMMENT: ()=>COMMENT,
    COMPILE_MODE: ()=>COMPILE_MODE,
    CONFIRM: ()=>CONFIRM,
    CONTAINER: ()=>CONTAINER,
    CONTEXT_ACTIONS: ()=>CONTEXT_ACTIONS,
    CURRENT_TARGET: ()=>CURRENT_TARGET,
    CUSTOM_WRAPPER: ()=>CUSTOM_WRAPPER,
    Current: ()=>Current,
    DATASET: ()=>DATASET,
    DATE: ()=>DATE,
    DOCUMENT_ELEMENT_NAME: ()=>DOCUMENT_ELEMENT_NAME,
    DOCUMENT_FRAGMENT: ()=>DOCUMENT_FRAGMENT,
    EVENT_CALLBACK_RESULT: ()=>EVENT_CALLBACK_RESULT,
    EXTERNAL_CLASSES: ()=>EXTERNAL_CLASSES,
    Events: ()=>Events,
    FOCUS: ()=>FOCUS,
    FormElement: ()=>FormElement,
    HEAD: ()=>HEAD,
    HOOKS_APP_ID: ()=>HOOKS_APP_ID,
    HTML: ()=>HTML,
    History: ()=>History,
    ID: ()=>ID,
    INPUT: ()=>INPUT,
    KEY_CODE: ()=>KEY_CODE,
    Location: ()=>Location,
    MutationObserver: ()=>MutationObserver2,
    OBJECT: ()=>OBJECT,
    ON_HIDE: ()=>ON_HIDE,
    ON_LOAD: ()=>ON_LOAD,
    ON_READY: ()=>ON_READY,
    ON_SHOW: ()=>ON_SHOW,
    OPTIONS: ()=>OPTIONS,
    PAGE_INIT: ()=>PAGE_INIT,
    PROPERTY_THRESHOLD: ()=>PROPERTY_THRESHOLD,
    PROPS: ()=>PROPS,
    PURE_VIEW: ()=>PURE_VIEW,
    ROOT_STR: ()=>ROOT_STR,
    SET_DATA: ()=>SET_DATA,
    SET_TIMEOUT: ()=>SET_TIMEOUT,
    STATIC_VIEW: ()=>STATIC_VIEW,
    STYLE: ()=>STYLE,
    SVGElement: ()=>SVGElement,
    Style: ()=>Style,
    TARGET: ()=>TARGET,
    TARO_RUNTIME: ()=>TARO_RUNTIME,
    TIME_STAMP: ()=>TIME_STAMP,
    TOUCHMOVE: ()=>TOUCHMOVE,
    TYPE: ()=>TYPE,
    TaroElement: ()=>TaroElement,
    TaroEvent: ()=>TaroEvent,
    TaroNode: ()=>TaroNode,
    TaroRootElement: ()=>TaroRootElement,
    TaroText: ()=>TaroText,
    UID: ()=>UID,
    URL: ()=>TaroURLProvider,
    URLSearchParams: ()=>URLSearchParams,
    VALUE: ()=>VALUE,
    VIEW: ()=>VIEW,
    addLeadingSlash: ()=>addLeadingSlash,
    cancelAnimationFrame: ()=>_caf,
    convertNumber2PX: ()=>convertNumber2PX,
    createComponentConfig: ()=>createComponentConfig,
    createEvent: ()=>createEvent,
    createPageConfig: ()=>createPageConfig,
    createRecursiveComponentConfig: ()=>createRecursiveComponentConfig,
    customWrapperCache: ()=>customWrapperCache,
    debounce: ()=>debounce,
    document: ()=>taroDocumentProvider,
    env: ()=>env,
    eventCenter: ()=>eventCenter,
    eventHandler: ()=>eventHandler,
    eventSource: ()=>eventSource,
    extend: ()=>extend,
    getComponentsAlias: ()=>getComponentsAlias2,
    getComputedStyle: ()=>taroGetComputedStyleProvider,
    getCurrentInstance: ()=>getCurrentInstance,
    getCurrentPage: ()=>getCurrentPage,
    getHomePage: ()=>getHomePage,
    getOnHideEventKey: ()=>getOnHideEventKey,
    getOnReadyEventKey: ()=>getOnReadyEventKey,
    getOnShowEventKey: ()=>getOnShowEventKey,
    getPageInstance: ()=>getPageInstance,
    getPath: ()=>getPath,
    handlePolyfill: ()=>handlePolyfill,
    hasBasename: ()=>hasBasename,
    history: ()=>taroHistoryProvider,
    hooks: ()=>hooks,
    hydrate: ()=>hydrate,
    incrementId: ()=>incrementId,
    injectPageInstance: ()=>injectPageInstance,
    isComment: ()=>isComment,
    isElement: ()=>isElement,
    isHasExtractProp: ()=>isHasExtractProp,
    isParentBinded: ()=>isParentBinded,
    isText: ()=>isText,
    location: ()=>taroLocationProvider,
    navigator: ()=>nav,
    nextTick: ()=>nextTick,
    now: ()=>now,
    options: ()=>options,
    parseUrl: ()=>parseUrl,
    perf: ()=>perf,
    removePageInstance: ()=>removePageInstance,
    requestAnimationFrame: ()=>_raf,
    safeExecute: ()=>safeExecute,
    shortcutAttr: ()=>shortcutAttr,
    stringify: ()=>stringify,
    stripBasename: ()=>stripBasename,
    stripSuffix: ()=>stripSuffix,
    stripTrailing: ()=>stripTrailing,
    throttle: ()=>throttle,
    window: ()=>taroWindowProvider
});
var init_dist2 = __esm({
    "node_modules/@tarojs/runtime/dist/index.js" () {
        init_dom_external();
        init_env();
        init_dist();
        init_document2();
        init_getComputedStyle();
        init_history();
        init_location();
        init_navigator();
        init_raf();
        init_URL();
        init_URLSearchParams();
        init_window();
        init_element();
        init_event();
        init_form();
        init_node();
        init_root();
        init_style();
        init_svg();
        init_text();
        init_mutation_observer();
        init_constants();
        init_current();
        init_event_source();
        init_common();
        init_emitter();
        init_hydrate();
        init_next_tick();
        init_options();
        init_perf();
        init_utils();
        init_polyfill();
        init_lodash();
        init_router();
    }
});
export { PROPERTY_THRESHOLD, TARO_RUNTIME, HOOKS_APP_ID, SET_DATA, PAGE_INIT, ROOT_STR, HTML, HEAD, BODY, APP, CONTAINER, DOCUMENT_ELEMENT_NAME, DOCUMENT_FRAGMENT, ID, UID, CLASS, STYLE, FOCUS, VIEW, STATIC_VIEW, PURE_VIEW, CLICK_VIEW, PROPS, DATASET, OBJECT, VALUE, INPUT, CHANGE, CUSTOM_WRAPPER, TARGET, CURRENT_TARGET, TYPE, CONFIRM, TIME_STAMP, KEY_CODE, TOUCHMOVE, DATE, SET_TIMEOUT, COMPILE_MODE, CATCHMOVE, CATCH_VIEW, COMMENT, ON_LOAD, ON_READY, ON_SHOW, ON_HIDE, OPTIONS, EXTERNAL_CLASSES, EVENT_CALLBACK_RESULT, BEHAVIORS, A, CONTEXT_ACTIONS, MutationObserver2 as MutationObserver, eventCenter, env, taroGetComputedStyleProvider, History, Current, getCurrentInstance, URLSearchParams, TaroURLProvider, parseUrl, Location, nav, now, _raf, _caf, taroWindowProvider, taroLocationProvider, taroHistoryProvider, incrementId, isElement, isText, isComment, isHasExtractProp, isParentBinded, shortcutAttr, customWrapperCache, extend, getComponentsAlias2 as getComponentsAlias, convertNumber2PX, eventSource, hydrate, TaroNode, Style, TaroElement, options, TaroEvent, createEvent, eventHandler, FormElement, throttle, debounce, perf, TaroRootElement, TaroText, taroDocumentProvider, SVGElement, addLeadingSlash, hasBasename, stripBasename, stripTrailing, stripSuffix, getHomePage, getCurrentPage, injectPageInstance, getPageInstance, removePageInstance, safeExecute, stringify, getPath, getOnReadyEventKey, getOnShowEventKey, getOnHideEventKey, createPageConfig, createComponentConfig, createRecursiveComponentConfig, nextTick, handlePolyfill, dist_exports, init_dist2 as init_dist };
