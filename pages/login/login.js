Page({
  data: {
    phone: '',
    code: ''
  },
  
  onLoad() {
    // 检查微信授权
    this.checkWechatAuth()
  },
  
  checkWechatAuth() {
    wx.login({
      success: (res) => {
        // 发送 res.code 到后台换取 openId, sessionKey
        this.getOpenId(res.code)
      }
    })
  },
  
  getOpenId(code) {
    wx.request({
      url: 'your-api/wechat/login',
      method: 'POST',
      data: { code },
      success: (res) => {
        // 处理登录逻辑
      }
    })
  }
})