from datetime import datetime

import sys
import psutil
import requests
import time
import GPUtil
import subprocess

class SystemMonitor:
    def __init__(self, webhook_url, temp_threshold=80, power_threshold=450):
        self.webhook_url = webhook_url
        self.temp_threshold = temp_threshold
        self.power_threshold = power_threshold  # 添加功率阈值

    def get_cpu_temperature(self):
        try:
            # 对于 MacOS
            if sys.platform == 'darwin':
                try:
                    output = subprocess.check_output(['/usr/local/bin/osx-cpu-temp'], encoding='utf-8')
                    return float(output.strip().replace('°C', ''))
                except:
                    return None

            # 对于 Linux，使用上面的文件读取方法
            return self._get_linux_temperature()
        except:
            return None

    def _get_linux_temperature(self):
        try:
            temp_files = [
                '/sys/class/thermal/thermal_zone0/temp',
                '/sys/class/hwmon/hwmon0/temp1_input',
                '/sys/class/hwmon/hwmon1/temp1_input'
            ]

            for temp_file in temp_files:
                try:
                    with open(temp_file, 'r') as f:
                        temp = int(f.read().strip())
                        return temp / 1000.0
                except:
                    continue

            return None
        except:
            return None


    def get_gpu_info(self):
        try:
            # 使用GPUtil获取基本信息
            gpus = GPUtil.getGPUs()
            if not gpus:
                return None
                
            gpu = gpus[0]  # 获取第一个GPU
            gpu_info = {
                'temperature': gpu.temperature,
                'memory_used': gpu.memoryUsed,
                'memory_total': gpu.memoryTotal,
                'memory_util': gpu.memoryUtil * 100
            }
            
            # 使用nvidia-smi获取功率信息
            try:
                # 获取功率信息
                power_output = subprocess.check_output(
                    ['nvidia-smi', '--query-gpu=power.draw,power.limit', '--format=csv,noheader,nounits'],
                    encoding='utf-8'
                ).strip()
                
                if power_output:
                    power_draw, power_limit = map(float, power_output.split(','))
                    gpu_info['power_draw'] = power_draw  # 当前功耗(W)
                    gpu_info['power_limit'] = power_limit  # 功率限制(W)
                    gpu_info['power_percent'] = (power_draw / power_limit) * 100 if power_limit > 0 else 0
            except (subprocess.SubprocessError, FileNotFoundError, ValueError):
                # 如果nvidia-smi不可用或解析失败，不包含功率信息
                pass
                
            return gpu_info
        except Exception as e:
            print(f"获取GPU信息失败: {e}")
            return None

    def send_wechat_alert(self, message):
        data = {
            "msgtype": "text",
            "text": {
                "content": message
            }
        }
        try:
            response = requests.post(self.webhook_url, json=data)
            return response.status_code == 200
        except:
            return False

    def monitor(self, interval=60):
        while True:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            alert_message = []

            # 检查CPU温度
            cpu_temp = self.get_cpu_temperature()
            print("cpu temp: ", cpu_temp)
            if cpu_temp and cpu_temp > self.temp_threshold:
                alert_message.append(f"CPU温度过高: {cpu_temp}°C")

            # 检查GPU状态
            gpu_info = self.get_gpu_info()
            if gpu_info:
                if gpu_info['temperature'] > self.temp_threshold:
                    alert_message.append(f"GPU温度过高: {gpu_info['temperature']}°C")
                
                # 添加内存使用信息
                alert_message.append(f"GPU显存使用: {gpu_info['memory_used']}/{gpu_info['memory_total']}MB ({gpu_info['memory_util']:.1f}%)")
                
                # 添加功率监控
                if 'power_draw' in gpu_info and 'power_limit' in gpu_info and 'power_percent' in gpu_info and gpu_info['power_draw']/gpu_info['power_limit'] > 0.8:
                    alert_message.append(f"GPU功率: {gpu_info['power_draw']:.2f}W / {gpu_info['power_limit']:.2f}W ({gpu_info['power_percent']:.1f}%)")
                    
                    # 检查功率是否超过阈值
                    if gpu_info['power_draw'] > self.power_threshold:
                        alert_message.append(f"GPU功率过高警告: {gpu_info['power_draw']:.2f}W > {self.power_threshold}W")

            # 如果有警告信息，发送到企业微信
            if alert_message:
                message = f"nuc 系统监控 - {current_time}\n" + "\n".join(alert_message)
                print(message)  # 控制台输出便于调试
                self.send_wechat_alert(message)

            time.sleep(interval)

if __name__ == "__main__":
    # 替换为你的企业微信机器人webhook地址
    WEBHOOK_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=dee71918-65c3-4baa-a6d7-0243be1e046e"
    monitor = SystemMonitor(WEBHOOK_URL, temp_threshold=50)
    monitor.monitor(interval=60)  # 每60秒检查一次