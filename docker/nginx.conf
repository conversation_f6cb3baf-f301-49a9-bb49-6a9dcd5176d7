# Nginx 主配置文件

events {
    worker_connections 1024;
}

http {
    # 基本设置
    include      /etc/nginx/mime.types;
    keepalive_timeout  65;
    default_type  text/html;
    sendfile        on;

    server {
        listen 80;

        server_name localhost;

        root /usr/share/nginx/html;

        index index.html index.htm;

        location / {
            try_files $uri $uri/ =404;
        }

        location /website/login {
            try_files /website/login.html =404;
        }

        location /website/mine {
            try_files /website/mine.html =404;
        }

        location /website/pay {
            try_files /website/pay.html =404;
        }

        location /website/certificate {
            try_files /website/certificate.html =404;
        }   
        location /website/pay-history {
            try_files /website/pay-history.html =404;
        }    
        location /website/gem {
            try_files /website/gem.html =404;
        }                

        location /login/management {
            try_files /login/management.html =404;
        }
        location ~ ^/login-.*$ {
            try_files /login.html =404;
        }
        location /login {
            try_files /login.html =404;
        }
        location ~ ^/quiz/(.+)$ {
            try_files /quiz/$1.html /quiz.html;
        }
        location ~ ^/quiz-new/(.+)$ {
            try_files /quiz-new/$1.html /quiz-new.html;
        }
        location = /quiz {
            try_files /quiz.html =404;
        }
        location ~ ^/answer/(.+)$ {
            try_files /answer/$1.html /answer.html;
        }
        location ~ ^/question/(.+)$ {
            try_files /question/$1.html /question.html;
        }
        location = /answer {
            try_files /answer.html =404;
        }
        location = /waiting {
            try_files /waiting.html =404;
        }
        error_page 404 /404.html;
        location = /404.html {
            root /usr/share/nginx/html;
        }
    }

}