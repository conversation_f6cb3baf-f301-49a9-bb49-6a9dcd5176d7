stages:
  - build
  - build-prod
  - stable

include:
  - project: 'g1/platform/gitlab-ci-templates'
    ref: master
    file: 'templates/deploy-k8s.yaml'

variables:
  GITOPS_APP_NAME: app-gusto-english-exam-front

workflow:
  # rules的规则：匹配到第一个，后面的短路
  rules:
    # test分支时各变量设置为测试环境的值
    - if: '$CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "dev-3"'
      variables:
        IMAGE: registry.cn-shenzhen.aliyuncs.com/project5e-test/app-gusto-english-exam-front
        GITOPS_ENVIRONMENT: test
        DOCKER_USERNAME: $TEST_DOCKER_USERNAME
        DOCKER_PASSWORD: $TEST_DOCKER_PASSWORD
    - if: '$CI_COMMIT_BRANCH == "main"'
      variables:
        IMAGE: registry-vpc.cn-beijing.aliyuncs.com/4tune/app-gusto-english-exam-front
        GITOPS_ENVIRONMENT: prod
        DOCKER_USERNAME: $PROD_DOCKER_USERNAME
        DOCKER_PASSWORD: $PROD_DOCKER_PASSWORD

build:
  stage: build
  tags:
    - 4tune
    - beijing
    - prod
  script:
    - yarn
    - yarn build:test
    - mv out docker/
    - cd docker
    - docker build -t ${IMAGE} .
    - docker tag ${IMAGE} ${IMAGE}:${CI_COMMIT_SHORT_SHA}
    - docker login --username=${DOCKER_USERNAME} --password=${DOCKER_PASSWORD} ${IMAGE}
    - docker push ${IMAGE}:${CI_COMMIT_SHORT_SHA}
    - docker rmi ${IMAGE}:${CI_COMMIT_SHORT_SHA}
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "dev-3"'
build-prod:
  stage: build-prod
  tags:
    - 4tune
    - beijing
    - prod
  script:
    - yarn
    - yarn build:prod
    - mv out docker/
    - cd docker
    - docker build -t ${IMAGE} .
    - docker tag ${IMAGE} ${IMAGE}:${CI_COMMIT_SHORT_SHA}
    - docker login --username=${DOCKER_USERNAME} --password=${DOCKER_PASSWORD} ${IMAGE}
    - docker push ${IMAGE}:${CI_COMMIT_SHORT_SHA}
    - docker rmi ${IMAGE}:${CI_COMMIT_SHORT_SHA}
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'   


deploy-stable:
  stage: stable
  tags:
    - 4tune
    - beijing
    - prod
  extends:
    - .deploy-stable
  environment:
    name: ${GITOPS_ENVIRONMENT}
    url: ${ENVIRONMENT_URL}
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "dev-3"'
    - if: '$CI_COMMIT_BRANCH == "main"'
