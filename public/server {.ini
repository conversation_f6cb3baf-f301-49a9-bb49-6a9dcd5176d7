server {
    listen 80;
    listen [::]:80;
    server_name dict.echo101.com;
    
    # 将 HTTP 重定向到 HTTPS (可选，取决于你是否需要 HTTPS)
    # return 301 https://$host$request_uri;

    # 如果你不需要 HTTPS，可以删除上面的重定向，保留下面的配置
    root /var/www/dict.echo101.com/public;  # 静态文件目录，调整为你的实际路径
    index dict.html index.html;             # 主入口 HTML 文件
    
    location / {
        try_files $uri $uri/ =404;
    }
    
    # 代理 /api 请求到本地服务
    location /api/ {
        proxy_pass http://127.0.0.1:13458/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 日志配置
    access_log /var/log/nginx/dict.echo101.com_access.log;
    error_log /var/log/nginx/dict.echo101.com_error.log;
}

# HTTPS 配置 (如果需要)
# server {
#     listen 443 ssl http2;
#     listen [::]:443 ssl http2;
#     server_name dict.echo101.com;
#     
#     ssl_certificate /etc/letsencrypt/live/dict.echo101.com/fullchain.pem;
#     ssl_certificate_key /etc/letsencrypt/live/dict.echo101.com/privkey.pem;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_prefer_server_ciphers on;
#     ssl_ciphers EECDH+AESGCM:EDH+AESGCM;
#     ssl_session_timeout 1d;
#     ssl_session_cache shared:SSL:50m;
#     ssl_stapling on;
#     ssl_stapling_verify on;
#     
#     root /var/www/dict.echo101.com/public;
#     index dict.html index.html;
#     
#     location / {
#         try_files $uri $uri/ =404;
#     }
#     
#     location /api/ {
#         proxy_pass http://127.0.0.1:13458/;
#         proxy_http_version 1.1;
#         proxy_set_header Upgrade $http_upgrade;
#         proxy_set_header Connection 'upgrade';
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#         proxy_cache_bypass $http_upgrade;
#     }
#     
#     access_log /var/log/nginx/dict.echo101.com_access.log;
#     error_log /var/log/nginx/dict.echo101.com_error.log;
# }