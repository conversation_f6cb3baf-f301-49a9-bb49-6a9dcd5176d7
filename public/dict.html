<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Online Dictionary</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }
        
        .search-box {
            display: flex;
            margin-bottom: 30px;
        }
        
        #word-input {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 4px 0 0 4px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }
        
        #word-input:focus {
            border-color: #3498db;
        }
        
        #search-btn {
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 0 4px 4px 0;
            padding: 0 20px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        #search-btn:hover {
            background-color: #2980b9;
        }
        
        .result-container {
            display: none;
            margin-top: 20px;
        }
        
        .word-title {
            font-size: 32px;
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        
        .definition-item {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .part-of-speech {
            color: #e74c3c;
            font-style: italic;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .definition {
            font-size: 18px;
            margin-bottom: 10px;
        }
        
        .example {
            color: #7f8c8d;
            padding-left: 15px;
            border-left: 3px solid #3498db;
            font-style: italic;
        }
        
        .error-message {
            color: #e74c3c;
            text-align: center;
            display: none;
            font-size: 18px;
            padding: 15px;
            background-color: #fadbd8;
            border-radius: 4px;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .loading::after {
            content: "...";
            animation: dots 1.5s steps(5, end) infinite;
        }
        
        @keyframes dots {
            0%, 20% { content: "."; }
            40% { content: ".."; }
            60%, 100% { content: "..."; }
        }
        
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .container {
                padding: 20px;
            }
            
            .word-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Online Dictionary</h1>
        
        <div class="search-box">
            <input type="text" id="word-input" placeholder="Enter a word to search..." autocomplete="off">
            <button id="search-btn">Search</button>
        </div>
        
        <div class="loading" id="loading">Searching</div>
        <div class="error-message" id="error-message"></div>
        
        <div class="result-container" id="result-container">
            <h2 class="word-title" id="word-title"></h2>
            <div id="definitions-list"></div>
        </div>
    </div>

    <script>
        function searchWord() {
            const word = wordInput.value.trim();
            
            if (!word) {
                showError('Please enter a word to search');
                return;
            }
            
            // Reset status
            resultContainer.style.display = 'none';
            errorMessage.style.display = 'none';
            loading.style.display = 'block';
            
            // Call API to search for the word
            fetch(`https://dict.echo101.com/api/dict/${encodeURIComponent(word)}`)
                .then(response => {
                    if (response.status === 404) {
                        throw new Error('word not found');
                    }
                    if (!response.ok) {
                        throw new Error('Network response error');
                    }
                    return response.json();
                })
                .then(data => {
                    loading.style.display = 'none';
                    
                    if (data.code === 0 && data.entries && data.entries.length > 0) {
                        displayResult(data.entries[0]);
                    } else {
                        showError('No definition found for this word');
                    }
                })
                .catch(error => {
                    loading.style.display = 'none';
                    showError(error.message);
                    console.error('Search error:', error);
                });
        }        document.addEventListener('DOMContentLoaded', () => {
            const wordInput = document.getElementById('word-input');
            const searchBtn = document.getElementById('search-btn');
            const resultContainer = document.getElementById('result-container');
            const definitionsList = document.getElementById('definitions-list');
            const wordTitle = document.getElementById('word-title');
            const loading = document.getElementById('loading');
            const errorMessage = document.getElementById('error-message');
            
            // Enter key triggers search
            wordInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    searchWord();
                }
            });
            
            // Click search button
            searchBtn.addEventListener('click', searchWord);
            
            function searchWord() {
                const word = wordInput.value.trim();
                
                if (!word) {
                    showError('Please enter a word to search');
                    return;
                }
                
                // Reset status
                resultContainer.style.display = 'none';
                errorMessage.style.display = 'none';
                loading.style.display = 'block';
                
                // Call API to search for the word
                fetch(`https://dict.echo101.com/api/dict/${encodeURIComponent(word)}`)
                    .then(response => {
                        if (response.status === 404) {
                            throw new Error('word not found');
                        }
                        if (!response.ok) {
                            throw new Error('Network response error');
                        }
                        return response.json();
                    })
                    .then(data => {
                        loading.style.display = 'none';
                        
                        if (data.code === 0 && data.entries && data.entries.length > 0) {
                            displayResult(data.entries[0]);
                        } else {
                            showError('No definition found for this word');
                        }
                    })
                    .catch(error => {
                        loading.style.display = 'none';
                        showError(error.message);
                        console.error('Search error:', error);
                    });
            }
            
            function displayResult(entry) {
                // Display word
                wordTitle.textContent = entry.word;
                
                // Clear previous results
                definitionsList.innerHTML = '';
                
                // Add all definitions and examples
                if (entry.definitions && entry.definitions.length > 0) {
                    entry.definitions.forEach(def => {
                        const definitionItem = document.createElement('div');
                        definitionItem.className = 'definition-item';
                        
                        // Part of speech
                        if (def.pos) {
                            const posElement = document.createElement('p');
                            posElement.className = 'part-of-speech';
                            posElement.textContent = def.pos;
                            definitionItem.appendChild(posElement);
                        }
                        
                        // Definition
                        const definitionElement = document.createElement('p');
                        definitionElement.className = 'definition';
                        definitionElement.textContent = def.definition;
                        definitionItem.appendChild(definitionElement);
                        
                        // Example
                        if (def.example) {
                            const exampleElement = document.createElement('p');
                            exampleElement.className = 'example';
                            exampleElement.textContent = def.example;
                            definitionItem.appendChild(exampleElement);
                        }
                        
                        definitionsList.appendChild(definitionItem);
                    });
                } else {
                    const noDefinition = document.createElement('p');
                    noDefinition.textContent = 'No definition found';
                    definitionsList.appendChild(noDefinition);
                }
                
                // Show result container
                resultContainer.style.display = 'block';
            }
            
            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.style.display = 'block';
                resultContainer.style.display = 'none';
            }
        });
    </script>
</body>
</html>