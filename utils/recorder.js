const recorderManager = wx.getRecorderManager()

const startRecord = () => {
  recorderManager.start({
    duration: 60000,
    sampleRate: 16000,
    numberOfChannels: 1,
    encodeBitRate: 96000,
    format: 'mp3'
  })
}

const stopRecord = () => {
  return new Promise((resolve) => {
    recorderManager.stop()
    recorderManager.onStop((res) => {
      resolve(res.tempFilePath)
    })
  })
}

module.exports = { startRecord, stopRecord }