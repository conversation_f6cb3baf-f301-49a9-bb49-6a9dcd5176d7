# gusto 考试项目

## usage

``` md
开发 yarn dev 
编译 yarn build
```

## 技术栈

[css 库](https://tailwindcss.com/docs/installation) <br />
[ui库](https://ui.shadcn.com/docs/components/button) <br />
[网络库](https://www.axios-http.cn/docs/intro)

## 如何使用ui库的组件

> 这个ui库和以前用的不太一样，他会把组件库 对应的组件源码直接在项目目录中生成


### quiz

考试系统

- 首次考试流程

判断当前用户是否登录(token判断) - 无则跳转登录页面
判断当前用户是否有权限考试 - 无则跳转支付页面存在购买产品才允许考试
进入提示弹窗(介绍模块) 
进入验证录音权限模块
开始考试 - 第一题开始答题
提交答案

- 考试到半路 中间刷新浏览器     §

要求保存答案 + 题目， 简单处理（不储存倒计时等逻辑）刷新后从当前题目开始。 为了保证不用处理史数据回显，刷新时当前正在回答的题目清空需要重新开始。

判断当前用户是否登录(token判断) - 无则跳转登录页面
判断当前用户是否有权限考试 - 无则跳转支付页面存在购买产品才允许考试
本地存在储存数据 - 则直接跳转到 对应的题目开始继续考试
本地无储存数据 - 则提示有一场已经在进行中的考试, 现在重新开始考试，然后从头开始考试
提交答案


#### 关于考试的流程

1. 关于答题

如果存在前置弹窗，需要先弹出弹窗，然后开始答题。答题分为读题倒计时 和 答题倒计时，读题倒计时结束后，开始答题倒计时，答题倒计时结束后，自动提交答案，然后进入下一题。 
